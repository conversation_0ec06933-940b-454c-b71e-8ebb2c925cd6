from PyQt6.QtCore import QRunnable, QThreadPool, QMutex
import time
import re

# <PERSON><PERSON> s<PERSON>ch toàn cục để theo dõi các worker đang chạy
_ACTIVE_WORKERS = []

# Đ<PERSON><PERSON> tượng đơn giản để lưu trữ kết quả và callback
class TaskResult:
    def __init__(self):
        self.is_completed = False
        self.result = None
        self.error = None
        self.logs = []
        self.processed_items = []
        self.callback = None
        self.mutex = QMutex()
    
    def add_log(self, message):
        self.mutex.lock()
        self.logs.append(message)
        self.mutex.unlock()
    
    def add_processed_item(self, brand_code, classification, product_details):
        self.mutex.lock()
        self.processed_items.append((brand_code, classification, product_details))
        self.mutex.unlock()
    
    def set_result(self, result):
        self.mutex.lock()
        self.result = result
        self.is_completed = True
        self.mutex.unlock()
    
    def set_error(self, error):
        self.mutex.lock()
        self.error = error
        self.is_completed = True
        self.mutex.unlock()

# <PERSON><PERSON><PERSON> chuyển đổi tên cột (A, B, C, ..., Z, AA, AB, ...) sang chỉ số cột (0-based)
def excel_column_to_index(column_name):
    """
    Chuyển đổi tên cột Excel (A, B, C, ..., Z, AA, AB, ...) sang chỉ số cột (0-based)
    Ví dụ: A -> 0, Z -> 25, AA -> 26, AB -> 27, ...
    """
    if not column_name:
        return -1
        
    column_name = column_name.upper()
    result = 0
    for c in column_name:
        # Xử lý từng ký tự riêng lẻ, không phải chuỗi hoàn chỉnh
        result = result * 26 + (ord(c) - ord('A') + 1)
    return result - 1  # Trừ 1 vì chỉ số bắt đầu từ 0

class SheetLoaderRunnable(QRunnable):
    """Runnable worker cho việc load Google Sheet không sử dụng signals"""
    
    def __init__(self, spreadsheet_id, oauth_base64):
        super().__init__()
        self.spreadsheet_id = spreadsheet_id
        self.oauth_base64 = oauth_base64
        self.result = TaskResult()
        _ACTIVE_WORKERS.append(self)
        
    def run(self):
        try:
            from gsheet_manager import GoogleSheetManager
            gs_manager = GoogleSheetManager(auth_type="oauth", credentials_data=self.oauth_base64)
            spreadsheet = gs_manager.open_by_key(self.spreadsheet_id)
            self.result.set_result((gs_manager, spreadsheet))
        except Exception as e:
            self.result.add_log(f"Error loading sheet: {str(e)}")
            self.result.set_error(str(e))
        finally:
            # Xóa worker khỏi danh sách
            if self in _ACTIVE_WORKERS:
                _ACTIVE_WORKERS.remove(self)

class ProcessingRunnable(QRunnable):
    """Runnable worker cho việc xử lý dữ liệu không sử dụng signals"""
    
    def __init__(self, spreadsheet, brand_sheet_name, deal_sheet_name,
                 brand_code_col, brand_type_col, deal_brand_code_col, deal_product_col,
                 last_row, model, brand_name_col="D", deal_price_col="AB"):
        super().__init__()
        self.spreadsheet = spreadsheet
        self.brand_sheet_name = brand_sheet_name
        self.deal_sheet_name = deal_sheet_name
        self.brand_code_col = brand_code_col
        self.brand_type_col = brand_type_col
        self.brand_name_col = brand_name_col
        self.deal_brand_code_col = deal_brand_code_col
        self.deal_product_col = deal_product_col
        self.deal_price_col = deal_price_col  # Thêm cột giá
        self.last_row = last_row
        self.model = model
        self.result = TaskResult()
        self.is_cancelled = False
        self.mutex = QMutex()
        self.last_activity_time = time.time()  # Thêm biến theo dõi thời gian hoạt động cuối cùng
        self.spreadsheet_id = ""  # Khởi tạo biến spreadsheet_id
        self.current_row = 0  # Theo dõi dòng hiện tại đang xử lý
        _ACTIVE_WORKERS.append(self)
        
    def is_alive(self):
        """Kiểm tra xem worker còn sống không (còn hoạt động trong thời gian gần đây)"""
        current_time = time.time()
        self.mutex.lock()
        last_time = self.last_activity_time
        self.mutex.unlock()
        
        # Nếu hoạt động trong vòng 2 phút gần đây, worker vẫn sống
        return (current_time - last_time) < 120
    
    def update_activity(self):
        """Cập nhật thời gian hoạt động gần đây nhất"""
        self.mutex.lock()
        self.last_activity_time = time.time()
        self.mutex.unlock()
        
    def cancel(self):
        """Dừng xử lý an toàn"""
        self.mutex.lock()
        self.is_cancelled = True
        self.mutex.unlock()
        
    def run(self):
        try:
            # Cập nhật thời gian hoạt động
            self.update_activity()
            
            # Thêm log để báo hiệu worker đã bắt đầu
            self.result.add_log("ProcessingRunnable đang bắt đầu...")
            
            # Import các hàm cần thiết từ ai_classification.py
            import re, time, json, base64, pickle, openai, os, sqlite3
            from pathlib import Path
            from collections import deque
            from datetime import datetime
            from difflib import SequenceMatcher
            
            # Tái sử dụng các hàm cần thiết từ module chính
            try:
                import ai_classification
                from ai_classification import (
                    extract_product_category, 
                    save_product_classification,
                    parse_spreadsheet_id,
                    find_similar_product,
                    format_price_vnd,
                    DB_PATH
                )
                self.result.add_log("Đã import thành công các module cần thiết")
                self.update_activity()  # Cập nhật hoạt động
            except Exception as e:
                self.result.add_log(f"Lỗi khi import modules: {str(e)}")
                self.result.set_error(f"Lỗi khi import modules: {str(e)}")
                return
            
            brand_feedback = {}
            brand_products = {}  
            brand_products_detail = {}
            
            try:
                # Kiểm tra spreadsheet trước khi truy cập
                if self.spreadsheet is None:
                    self.result.add_log("Lỗi: spreadsheet là None")
                    self.result.set_error("Spreadsheet chưa được tải hoặc không hợp lệ")
                    return
                    
                # Lấy spreadsheet_id từ thuộc tính của spreadsheet
                try:
                    self.spreadsheet_id = getattr(self.spreadsheet, 'id', "")
                    if self.spreadsheet_id:
                        self.result.add_log(f"Đã xác định spreadsheet ID: {self.spreadsheet_id}")
                except Exception as e:
                    self.result.add_log(f"Không thể lấy spreadsheet ID: {str(e)}")
                    self.spreadsheet_id = ""
                    
                self.result.add_log("Đang truy cập worksheets...")    
                brand_sheet = self.spreadsheet.worksheet(self.brand_sheet_name)
                deal_sheet = self.spreadsheet.worksheet(self.deal_sheet_name)
                self.result.add_log(f"Đã truy cập worksheet thành công: {self.brand_sheet_name} và {self.deal_sheet_name}")
                self.update_activity()  # Cập nhật hoạt động
            except Exception as e:
                self.result.add_log(f"Lỗi truy cập worksheets: {str(e)}")
                self.result.set_error(f"Lỗi truy cập worksheets: {str(e)}")
                return

            try:
                self.result.add_log("Đang đọc dữ liệu từ Brand list...")
                brand_all = brand_sheet.get_all_values()
                self.result.add_log(f"Đã đọc {len(brand_all)} dòng từ Brand list")
                self.update_activity()  # Cập nhật hoạt động
                
                if len(brand_all) < 2:
                    self.result.add_log("Không có dữ liệu trong Brand list.")
                    self.result.set_result({"feedback": brand_feedback})
                    return
                    
                # Xử lý đến dòng được chỉ định (nếu nhập)
                try:
                    if self.last_row and self.last_row.strip():
                        last = int(self.last_row)
                        brand_data = brand_all[1:last]  # Bỏ tiêu đề
                        self.result.add_log(f"Xử lý đến dòng {last}")
                    else:
                        brand_data = brand_all[1:]
                        self.result.add_log(f"Xử lý tất cả {len(brand_data)} dòng")
                except Exception as e:
                    self.result.add_log(f"Lỗi khi xác định dòng cuối ({self.last_row}): {str(e)}")
                    brand_data = brand_all[1:]
                    
                self.result.add_log("Đang đọc dữ liệu từ Deal list...")
                deal_all = deal_sheet.get_all_values()
                self.result.add_log(f"Đã đọc {len(deal_all)} dòng từ Deal list")
                self.update_activity()  # Cập nhật hoạt động
                
                if len(deal_all) < 4:
                    self.result.add_log("Không có dữ liệu trong Deal list.")
                    self.result.set_result({"feedback": brand_feedback})
                    return
                    
                deal_data = deal_all[3:]  # Bỏ 3 dòng tiêu đề
                self.result.add_log(f"Sẽ xử lý {len(deal_data)} dòng từ Deal list")

                # Tạo mapping: Brand code -> danh sách sản phẩm
                self.result.add_log("Đang tạo mapping brand code -> sản phẩm...")
                deal_mapping = {}
                processed_count = 0
                
                # Chuyển đổi tên cột thành chỉ số
                idx_brand = excel_column_to_index(self.deal_brand_code_col)
                idx_product = excel_column_to_index(self.deal_product_col)
                idx_price = excel_column_to_index(self.deal_price_col)
                
                self.result.add_log(f"Chỉ số cột: Brand={idx_brand} ({self.deal_brand_code_col}), Product={idx_product} ({self.deal_product_col}), Price={idx_price} ({self.deal_price_col})")
                
                # Kiểm tra các chỉ số cột
                if idx_brand < 0 or idx_product < 0 or idx_price < 0:
                    self.result.add_log(f"Lỗi: Chỉ số cột không hợp lệ: Brand={idx_brand}, Product={idx_product}, Price={idx_price}")
                    self.result.set_error(f"Tên cột không hợp lệ. Vui lòng kiểm tra lại các trường: Brand Code={self.deal_brand_code_col}, Product={self.deal_product_col}, Price={self.deal_price_col}")
                    return
                
                for row in deal_data:
                    # Kiểm tra hủy bỏ định kỳ để tránh block quá lâu
                    processed_count += 1
                    if processed_count % 100 == 0:
                        self.result.add_log(f"Đã xử lý {processed_count}/{len(deal_data)} dòng trong Deal list")
                        self.update_activity()  # Cập nhật hoạt động
                        
                    self.mutex.lock()
                    cancelled = self.is_cancelled
                    self.mutex.unlock()
                    if cancelled:
                        self.result.add_log("Đã hủy xử lý.")
                        self.result.set_result({"feedback": brand_feedback})
                        return
                        
                    try:
                        # Kiểm tra độ dài của row để tránh lỗi index
                        if not row:
                            continue
                            
                        # Kiểm tra độ dài của row để tránh IndexError
                        if len(row) <= max(idx_brand, idx_product):
                            continue
                        
                        brand_code = row[idx_brand].strip() if idx_brand < len(row) else ""
                        product_name = row[idx_product].strip() if idx_product < len(row) else ""
                        
                        if not brand_code or not product_name:
                            continue
                        
                        # Đọc giá sản phẩm nếu có
                        price_value = 0
                        if idx_price < len(row):
                            try:
                                # Làm sạch giá (loại bỏ ký tự không phải số)
                                price_text = row[idx_price].strip()
                                cleaned_price = re.sub(r'[^\d]', '', price_text)
                                if cleaned_price:
                                    price_value = int(cleaned_price)
                                    
                                    # Kiểm tra giá trị hợp lý (dưới 100 triệu)
                                    MAX_REASONABLE_PRICE = 100000000  # 100 triệu
                                    if price_value > MAX_REASONABLE_PRICE:
                                        # Có thể là lỗi định dạng, thử chia cho 1000
                                        if price_value % 1000 == 0:
                                            self.result.add_log(f"  - Giá quá cao ({price_value}), chia cho 1000 -> {price_value//1000}")
                                            price_value = price_value // 1000
                                    
                            except Exception as e:
                                self.result.add_log(f"Lỗi xử lý giá cho sản phẩm '{product_name}': {str(e)}")
                                price_value = 0
                        
                        # Thêm kiểm tra cột N - Pick (chỉ xử lý nếu có "Yes")
                        idx_pick = 13  # Cột N (0-based index)
                        idx_review = 14  # Cột O (0-based index)
                        
                        # Kiểm tra để đảm bảo các chỉ số trong phạm vi
                        pick_value = row[idx_pick].strip() if len(row) > idx_pick else ""
                        review_value = row[idx_review].strip() if len(row) > idx_review else ""
                        
                        if pick_value.lower() != "yes":
                            continue
                        
                        has_review = bool(review_value)
                        
                        if brand_code:
                            if brand_code not in deal_mapping:
                                deal_mapping[brand_code] = []
                            deal_mapping[brand_code].append({
                                "name": product_name,
                                "has_review": has_review,
                                "price": price_value  # Thêm giá vào dữ liệu
                            })
                    except Exception as e:
                        self.result.add_log(f"Lỗi xử lý dòng trong Deal list: {str(e)}")
                        continue

                self.result.add_log(f"Đã tạo mapping cho {len(deal_mapping)} brand code từ Deal list")
                
                # Xử lý dữ liệu theo từng brand
                updated_count = 0
                brand_count = len(brand_data)
                self.result.add_log(f"Bắt đầu xử lý {brand_count} brand code...")
                
                # Chuyển đổi tên cột Brand list thành chỉ số
                idx_brand = excel_column_to_index(self.brand_code_col)
                idx_type = excel_column_to_index(self.brand_type_col)
                idx_brand_name = excel_column_to_index(self.brand_name_col)
                
                self.result.add_log(f"Chỉ số cột Brand list: Brand Code={idx_brand} ({self.brand_code_col}), Type={idx_type} ({self.brand_type_col}), Brand Name={idx_brand_name} ({self.brand_name_col})")
                
                # Kiểm tra các chỉ số cột
                if idx_brand < 0 or idx_type < 0 or idx_brand_name < 0:
                    self.result.add_log(f"Lỗi: Chỉ số cột Brand list không hợp lệ: Brand Code={idx_brand}, Type={idx_type}, Brand Name={idx_brand_name}")
                    self.result.set_error(f"Tên cột Brand list không hợp lệ. Vui lòng kiểm tra lại các trường: Brand Code={self.brand_code_col}, Type={self.brand_type_col}, Brand Name={self.brand_name_col}")
                    return
                
                # Biến đếm số lần lỗi API liên tiếp để có thể tạm dừng nếu cần
                consecutive_api_errors = 0
                max_consecutive_errors = 5  # Số lần lỗi liên tiếp tối đa trước khi dừng
                
                for i, row in enumerate(brand_data, start=2):
                    # Cập nhật dòng hiện tại đang xử lý
                    self.current_row = i
                    
                    # Kiểm tra hủy bỏ định kỳ
                    if i % 10 == 0:
                        self.result.add_log(f"Đang xử lý brand {i-1}/{brand_count}...")
                        self.update_activity()  # Cập nhật hoạt động
                        
                    self.mutex.lock()
                    cancelled = self.is_cancelled
                    self.mutex.unlock()
                    if cancelled:
                        self.result.add_log("Đã hủy xử lý.")
                        break
                        
                    try:
                        if not row:
                            continue
                            
                        # Kiểm tra độ dài row
                        brand_code = row[idx_brand].strip() if idx_brand < len(row) else ""
                        brand_name = row[idx_brand_name].strip() if idx_brand_name < len(row) and len(row) > idx_brand_name else ""
                    except Exception as e:
                        self.result.add_log(f"Lỗi đọc dòng {i} trong Brand list: {str(e)}")
                        continue

                    if not brand_code:
                        self.result.add_log(f"Dòng {i}: Brand code trống. Bỏ qua dòng này.")
                        continue

                    if brand_code not in deal_mapping:
                        self.result.add_log(f"Brand code '{brand_code}': Không tìm thấy sản phẩm trong Deal list.")
                        continue
                        
                    self.result.add_log(f"Đang xử lý '{brand_code}' (Dòng {i})...")

                    product_items = deal_mapping[brand_code]
                    classifications_with_priority = []
                    product_names = []  # Track all product names for this brand
                    product_details = []  # Store product name and its classification
                    
                    self.result.add_log(f"Brand '{brand_code}' có {len(product_items)} sản phẩm cần phân loại")
                    
                    has_api_error_this_brand = False  # Flag để theo dõi lỗi API trong brand hiện tại
                    
                    for product_item in product_items:
                        self.mutex.lock()
                        cancelled = self.is_cancelled
                        self.mutex.unlock()
                        if cancelled:
                            break
                            
                        try:
                            product = product_item["name"]
                            has_review = product_item["has_review"]
                            price = product_item.get("price", 0)  # Lấy giá sản phẩm
                            product_names.append(product)  # Save product name for reference
                            
                            # Log để theo dõi
                            self.result.add_log(f"  - Đang phân loại sản phẩm: {product}")
                            
                            # Cập nhật hoạt động trước khi gọi API
                            self.update_activity()
                            
                            # Gọi hàm extract_product_category với giá
                            result, _, extracted_data = extract_product_category(product, price=price, model=self.model)
                            
                            # Cập nhật hoạt động sau khi gọi API
                            self.update_activity()
                            
                            # Kiểm tra nếu có lỗi
                            if result == "Unknown" and isinstance(extracted_data, dict) and "error" in extracted_data:
                                error_msg = extracted_data["error"]
                                self.result.add_log(f"  - Lỗi khi phân loại: {error_msg}")
                                
                                # Kiểm tra nếu là lỗi API OpenAI
                                if "520" in error_msg or "cloudflare" in error_msg.lower() or "openai" in error_msg.lower():
                                    has_api_error_this_brand = True
                                    consecutive_api_errors += 1
                                    
                                    # Nếu quá nhiều lỗi liên tiếp, tạm dừng xử lý
                                    if consecutive_api_errors >= max_consecutive_errors:
                                        self.result.add_log(f"Đã xảy ra {consecutive_api_errors} lỗi API liên tiếp. Dừng quá trình xử lý.")
                                        self.result.set_error(f"Quá nhiều lỗi API liên tiếp ({consecutive_api_errors}). Vui lòng kiểm tra kết nối mạng và thử lại sau.")
                                        break
                                        
                                # Bỏ qua sản phẩm này, chuyển sang sản phẩm tiếp theo
                                continue
                            else:
                                # Reset số lần lỗi liên tiếp nếu thành công
                                consecutive_api_errors = 0
                            
                            if result:
                                self.result.add_log(f"  - Kết quả phân loại: {result}")
                                classifications_with_priority.append({
                                    "category": result,
                                    "has_review": has_review
                                })
                                # Save the product and its classification
                                product_details.append((product, result))
                                # Store in database for future reference with price
                                try:
                                    save_product_classification(product, result, brand_code, price, self.deal_sheet_name, self.spreadsheet_id)
                                except Exception as save_error:
                                    self.result.add_log(f"  - Lỗi khi lưu phân loại: {str(save_error)}")
                                
                                # Store product details in database if we have extracted data
                                if extracted_data and isinstance(extracted_data, dict) and "error" not in extracted_data:
                                    # Thêm thông tin sản phẩm và brand name vào extracted_data để lưu
                                    extracted_data["product_info"] = product
                                    
                                    # Nếu brand name từ sheet khác rỗng và khác với brand đã trích xuất, ưu tiên sử dụng
                                    if brand_name and (not extracted_data.get("brand") or brand_name.lower() != extracted_data.get("brand").lower()):
                                        extracted_data["brand"] = brand_name.lower()
                                        
                                    # Lưu thông tin đầy đủ vào database
                                    try:
                                        ai_classification.save_product_details(extracted_data)
                                    except Exception as detail_error:
                                        self.result.add_log(f"  - Lỗi khi lưu chi tiết sản phẩm: {str(detail_error)}")
                                        
                        except Exception as e:
                            self.result.add_log(f"  - Lỗi khi phân loại sản phẩm '{product}': {str(e)}")
                            continue
                    
                    # Cập nhật hoạt động sau khi xử lý một brand
                    self.update_activity()
                
                    # Kiểm tra nếu đã vượt quá số lỗi API cho phép
                    if consecutive_api_errors >= max_consecutive_errors:
                        break
                        
                    self.mutex.lock()
                    cancelled = self.is_cancelled
                    self.mutex.unlock()
                    if cancelled:
                        self.result.add_log("Đã hủy xử lý.")
                        break
                        
                    # Nếu có lỗi API trong brand này, bỏ qua việc cập nhật
                    if has_api_error_this_brand:
                        self.result.add_log(f"Bỏ qua cập nhật cho brand '{brand_code}' do lỗi API")
                        continue
                        
                    # Sắp xếp theo ưu tiên: đầu tiên là các sản phẩm có review, sau đó là các sản phẩm không có review
                    classifications_with_priority.sort(key=lambda x: 0 if x["has_review"] else 1)
                    
                    # Lấy danh sách các loại sản phẩm duy nhất nhưng vẫn giữ thứ tự ưu tiên
                    seen = set()
                    unique_classifications = []
                    for item in classifications_with_priority:
                        category = item["category"]
                        if category not in seen:
                            seen.add(category)
                            unique_classifications.append(category)
                    
                    # Kiểm tra nếu không có phân loại nào
                    if not unique_classifications:
                        self.result.add_log(f"Brand code '{brand_code}': Không có phân loại nào được tìm thấy. Bỏ qua cập nhật.")
                        continue
                    
                    final_result = ", ".join(unique_classifications)
                    
                    # Thông báo hoàn tất phân loại brand với một dòng duy nhất kết hợp
                    self.result.add_log(f"Hoàn tất phân loại brand '{brand_code}' với {len(product_details)} sản phẩm: {final_result}")
                    
                    try:
                        if idx_type >= 0:
                            brand_sheet.update_cell(i, idx_type + 1, final_result)
                            updated_count += 1
                            brand_feedback[brand_code] = final_result
                            # Store product names as well for feedback context
                            brand_products[brand_code] = " | ".join(product_names[:3])  # Store up to 3 product names
                            # Store detailed product information for the dialog
                            brand_products_detail[brand_code] = product_details
                            
                            # Add processed item
                            self.result.add_processed_item(brand_code, final_result, product_details)
                        else:
                            self.result.add_log(f"Không thể cập nhật: Chỉ số cột Type không hợp lệ ({idx_type})")
                        
                    except Exception as e:
                        self.result.add_log(f"Lỗi cập nhật dòng {i}: {str(e)}")
                
                # Cập nhật hoạt động khi hoàn tất toàn bộ xử lý
                self.update_activity()

                # Pass both the classification results and detailed product data
                result_data = {
                    "feedback": brand_feedback,
                    "products": brand_products,
                    "product_details": brand_products_detail
                }
                
                self.result.add_log(f"Xử lý hoàn tất. Đã cập nhật {updated_count} dòng.")
                
                # Kiểm tra các biến API từ module ai_classification
                try:
                    self.result.add_log(f"Tổng số lần gọi API: {ai_classification.api_call_count}")
                    self.result.add_log(f"Tổng token đã dùng: {ai_classification.total_tokens_used}")
                except Exception as api_error:
                    self.result.add_log(f"Lỗi khi truy cập thống kê API: {str(api_error)}")
                
                self.result.set_result(result_data)
                
            except Exception as e:
                import traceback
                error_traceback = traceback.format_exc()
                self.result.add_log(f"Lỗi khi xử lý dữ liệu từ worksheets: {str(e)}")
                self.result.add_log(f"Chi tiết lỗi: {error_traceback}")
                self.result.set_error(f"Lỗi khi xử lý dữ liệu từ worksheets: {str(e)}")
                
        except Exception as e:
            import traceback
            traceback_str = traceback.format_exc()
            print(f"Exception in ProcessingRunnable: {e}\n{traceback_str}")
            self.result.add_log(f"Lỗi xử lý: {e}")
            self.result.add_log(f"Chi tiết: {traceback_str}")
            self.result.set_error(str(e))
        finally:
            # Cập nhật hoạt động lần cuối
            self.update_activity()
            # Xóa worker khỏi danh sách khi hoàn thành
            if self in _ACTIVE_WORKERS:
                _ACTIVE_WORKERS.remove(self)

def cleanup_signals():
    """Xóa tất cả worker đang chạy"""
    global _ACTIVE_WORKERS
    print(f"Đang dọn dẹp {len(_ACTIVE_WORKERS)} worker còn đang chạy...")
    
    # Hủy tất cả worker đang chạy
    for worker in list(_ACTIVE_WORKERS):
        if hasattr(worker, 'cancel'):
            try:
                worker.cancel()
            except:
                pass
    
    # Xóa tất cả worker
    _ACTIVE_WORKERS.clear() 