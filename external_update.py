import sys
import base64
import traceback
import time
import os
import logging
import datetime
import json
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QLineEdit,
    QComboBox, QPushButton, QTextEdit, QProgressBar, QWidget, QMessageBox, QDialog, QGridLayout, QRadioButton, QToolButton, QMenu, QSizePolicy, QTableWidget, QTableWidgetItem, QCheckBox
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt, QEvent, QTimer, QMutex
from PyQt6.QtGui import QTextCursor, QColor

# Import GoogleSheetManager từ gsheet_manager.py
from gsheet_manager import GoogleSheetManager

# Đường dẫn thư mục cấu hình
CONFIG_DIR = os.path.join(os.environ.get('LOCALAPPDATA', os.path.expanduser('~')), 'Data All in One', 'External Update')
CONFIG_FILE = os.path.join(CONFIG_DIR, 'config.json')

# Hàm để tạo thư mục cấu hình nếu chưa tồn tại
def ensure_config_dir():
    try:
        if not os.path.exists(CONFIG_DIR):
            os.makedirs(CONFIG_DIR, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Không thể tạo thư mục cấu hình: {str(e)}")
        return False

# Hàm để lưu cấu hình vào file
def save_config(process_conditions, column_groups):
    if not ensure_config_dir():
        return False

    try:
        config = {
            'process_conditions': process_conditions,
            'column_groups': column_groups,
            'last_updated': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logging.info(f"Đã lưu cấu hình vào {CONFIG_FILE}")
        return True
    except Exception as e:
        logging.error(f"Lỗi khi lưu cấu hình: {str(e)}")
        return False

# Hàm để đọc cấu hình từ file
def load_config():
    try:
        if not os.path.exists(CONFIG_FILE):
            logging.info(f"File cấu hình không tồn tại: {CONFIG_FILE}")
            return None

        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logging.info(f"Đã đọc cấu hình từ {CONFIG_FILE}")
        return config
    except Exception as e:
        logging.error(f"Lỗi khi đọc cấu hình: {str(e)}")
        return None

# Chuỗi Base64 của client OAuth
CREDENTIALS_BASE64 = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Thêm OAuth credentials khác từ các chương trình còn lại
ALTERNATIVE_CREDENTIALS = [
    # Credentials từ import_data.py
    "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
]

# Định nghĩa nhóm cột để sử dụng trong các điều kiện xử lý
COLUMN_GROUPS = {
    "price": ["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"],
    "stock": ["Số lượng tồn kho"],
    "gift": [
        "Quà tặng kèm (nếu có)",
        "Mã quà tặng kèm (Item ID quà tặng) nếu có",
        "Giá trị quà tặng kèm",
        "Link quà tặng kèm",
        "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
    ],
    "voucher": [
        "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
        "Mức giảm tối đa",
        "Áp dụng cho đơn từ"
    ],
    "commission": ["Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"],
    "name": ["Tên sản phẩm"]
}

# Dictionary lưu trữ cấu hình các điều kiện xử lý và các cột liên quan
PROCESS_CONDITIONS = {
    "đổi stock": {
        "column_groups": ["stock"],
        "description": "Thay đổi số lượng tồn kho"
    },
    "đổi giá": {
        "column_groups": ["price"],
        "description": "Thay đổi giá sản phẩm"
    },
    "thêm quà": {
        "column_groups": ["gift"],
        "description": "Thêm quà tặng kèm"
    },
    "đổi quà": {
        "column_groups": ["gift"],
        "description": "Thay đổi quà tặng kèm"
    },
    "đổi giá và quà": {
        "column_groups": ["price", "gift"],
        "description": "Thay đổi cả giá và quà tặng kèm"
    },
    "cms": {
        "column_groups": ["commission"],
        "description": "Thay đổi tỷ lệ hoa hồng"
    },
    "xoá quà": {
        "column_groups": ["gift"],
        "description": "Xóa quà tặng kèm hiện có"
    },
    "đổi giá và stock": {
        "column_groups": ["price", "stock"],
        "description": "Thay đổi cả giá và tồn kho"
    },
    "đổi tên sp + đổi giá": {
        "column_groups": ["name", "price"],
        "description": "Thay đổi tên sản phẩm và giá"
    },
    "thêm voucher, quà và thay đổi stock": {
        "column_groups": ["voucher", "gift", "stock", "commission"],
        "description": "Thêm voucher, quà và thay đổi tồn kho"
    },
    "thêm voucher và stock": {
        "column_groups": ["voucher", "stock", "commission"],
        "description": "Thêm voucher và thay đổi tồn kho"
    },
    "đổi giá và thêm voucher": {
        "column_groups": ["price", "voucher", "commission"],
        "description": "Thay đổi giá và thêm voucher"
    },
    "thêm voucher + thêm quà": {
        "column_groups": ["voucher", "gift", "commission"],
        "description": "Thêm voucher và quà tặng kèm"
    },
    "thêm voucher": {
        "column_groups": ["voucher", "commission"],
        "description": "Thêm voucher giảm giá"
    }
}

# Ánh xạ cột cho sheet nguồn
SOURCE_COLUMN_MAPPING = {
    "Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu": "H",
    "Tên sản phẩm": "I",
    "Mã phân loại (Model ID)": "J",
    "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)": "M",
    "Số lượng tồn kho": "N",
    "Quà tặng kèm (nếu có)": "Q",
    "Mã quà tặng kèm (Item ID quà tặng) nếu có": "R",
    "Giá trị quà tặng kèm": "S",
    "Link quà tặng kèm": "T",
    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)": "U",
    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)": "Z",
    "Mức giảm tối đa": "AA",
    "Áp dụng cho đơn từ": "AB",
    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm": "AC"
}

# Ánh xạ cột cho sheet đích
TARGET_COLUMN_MAPPING = {
    "Mã sản phẩm (Item ID)": "D",
    "Mã phân loại (Model ID)": "K",
    "Tên sản phẩm": "J",
    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)": "AA",
    "Mức giảm tối đa": "AB",
    "Áp dụng cho đơn từ": "AC",
    "Số lượng tồn kho": "O",
    "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)": "N",
    "Quà tặng kèm (nếu có)": "R",
    "Mã quà tặng kèm (Item ID quà tặng) nếu có": "S",
    "Giá trị quà tặng kèm": "T",
    "Link quà tặng kèm": "U",
    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)": "V",
    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm": "AD"
}

# Hàm chuyển ký tự cột thành số (A -> 1, B -> 2, ...)
def column_letter_to_number(letter):
    letter = letter.upper()
    number = 0
    for char in letter:
        number = number * 26 + (ord(char) - ord('A') + 1)
    return number

# Dialog để ánh xạ cột
class ColumnMappingDialog(QDialog):
    def __init__(self, source_mapping, target_mapping, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Data Assortment All in One")
        self.setGeometry(200, 200, 600, 400)
        self.source_mapping = source_mapping.copy()
        self.target_mapping = target_mapping.copy()
        self.current_mapping = self.source_mapping  # Mặc định là sheet nguồn
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Radio buttons để chọn sheet
        radio_layout = QHBoxLayout()
        self.source_radio = QRadioButton("Internal Columns")
        self.target_radio = QRadioButton("External Columns")
        self.source_radio.setChecked(True)  # Mặc định chọn sheet nguồn
        radio_layout.addWidget(self.source_radio)
        radio_layout.addWidget(self.target_radio)
        layout.addLayout(radio_layout)

        # Kết nối sự kiện radio button
        self.source_radio.toggled.connect(self.switch_mapping)
        self.target_radio.toggled.connect(self.switch_mapping)

        # Grid layout cho ánh xạ cột
        self.grid_layout = QGridLayout()
        self.inputs = {}
        self.update_mapping_ui()
        layout.addLayout(self.grid_layout)

        # Nút lưu và hủy
        button_layout = QHBoxLayout()
        save_button = QPushButton("Lưu")
        cancel_button = QPushButton("Hủy")
        save_button.clicked.connect(self.accept)
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(save_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def switch_mapping(self):
        # Xóa các widget cũ trong grid layout
        for i in reversed(range(self.grid_layout.count())):
            widget = self.grid_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        # Cập nhật mapping hiện tại
        if self.source_radio.isChecked():
            self.current_mapping = self.source_mapping
        else:
            self.current_mapping = self.target_mapping

        # Cập nhật giao diện
        self.update_mapping_ui()

    def update_mapping_ui(self):
        self.inputs = {}
        for i, (header, column) in enumerate(self.current_mapping.items()):
            self.grid_layout.addWidget(QLabel(header), i, 0)
            line_edit = QLineEdit(column)
            self.grid_layout.addWidget(line_edit, i, 1)
            self.inputs[header] = line_edit

    def get_mappings(self):
        for header, line_edit in self.inputs.items():
            self.current_mapping[header] = line_edit.text().strip().upper()
        return self.source_mapping, self.target_mapping

# Dialog để quản lý điều kiện xử lý
class ProcessConditionDialog(QDialog):
    def __init__(self, process_conditions, column_groups, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Quản lý điều kiện xử lý")
        self.setGeometry(200, 200, 800, 600)
        self.process_conditions = process_conditions.copy()
        self.column_groups = column_groups.copy()
        self.modified = False
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # Label hướng dẫn
        intro_layout = QVBoxLayout()
        intro_label = QLabel("Quản lý các điều kiện xử lý dữ liệu. Bạn có thể thêm, sửa, xóa các điều kiện và chọn các nhóm cột liên quan.")
        intro_label.setWordWrap(True)

        # Thêm thông tin về nơi lưu cấu hình
        config_info = QLabel(f"Cấu hình sẽ được lưu tại: {CONFIG_FILE}")
        config_info.setWordWrap(True)
        config_info.setStyleSheet("color: #666; font-size: 11px;")

        intro_layout.addWidget(intro_label)
        intro_layout.addWidget(config_info)
        layout.addLayout(intro_layout)

        # Phần danh sách điều kiện
        list_group = QGroupBox("Danh sách điều kiện")
        list_layout = QVBoxLayout()

        # Tạo bảng cho danh sách điều kiện
        self.condition_table = QTableWidget()
        self.condition_table.setColumnCount(3)
        self.condition_table.setHorizontalHeaderLabels(["Điều kiện", "Mô tả", "Nhóm cột"])
        self.condition_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.condition_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.condition_table.cellClicked.connect(self.load_condition)

        # Thiết lập kích thước cột
        self.condition_table.horizontalHeader().setSectionResizeMode(0, self.condition_table.horizontalHeader().ResizeMode.ResizeToContents)
        self.condition_table.horizontalHeader().setSectionResizeMode(1, self.condition_table.horizontalHeader().ResizeMode.Stretch)
        self.condition_table.horizontalHeader().setSectionResizeMode(2, self.condition_table.horizontalHeader().ResizeMode.Stretch)

        # Thêm nút "Thêm điều kiện mới" ở dưới bảng
        add_button_layout = QHBoxLayout()
        self.add_condition_button = QPushButton("+ Thêm điều kiện mới")
        self.add_condition_button.setStyleSheet("""
            QPushButton {
                color: #4CAF50;
                font-weight: bold;
            }
        """)
        self.add_condition_button.clicked.connect(self.clear_condition_form)
        add_button_layout.addWidget(self.add_condition_button, alignment=Qt.AlignmentFlag.AlignLeft)

        list_layout.addWidget(self.condition_table)
        list_layout.addLayout(add_button_layout)
        list_group.setLayout(list_layout)

        # Phần chỉnh sửa điều kiện
        edit_group = QGroupBox("Thông tin điều kiện")
        edit_layout = QGridLayout()

        # Tên điều kiện
        edit_layout.addWidget(QLabel("Tên điều kiện:"), 0, 0)
        self.condition_name = QLineEdit()
        edit_layout.addWidget(self.condition_name, 0, 1, 1, 3)

        # Mô tả
        edit_layout.addWidget(QLabel("Mô tả:"), 1, 0)
        self.condition_desc = QLineEdit()
        edit_layout.addWidget(self.condition_desc, 1, 1, 1, 3)

        # Nhóm cột
        edit_layout.addWidget(QLabel("Chọn nhóm cột:"), 2, 0, 1, 4)
        self.group_checkboxes = {}

        # Tạo checkbox cho mỗi nhóm cột
        checkbox_layout = QGridLayout()
        for i, (group_name, columns) in enumerate(self.column_groups.items()):
            row = i // 3
            col = i % 3

            # Tạo layout con để hiển thị tên nhóm và tooltip cho chi tiết
            group_layout = QHBoxLayout()

            # Tạo checkbox với tên nhóm cột
            checkbox = QCheckBox(group_name)
            checkbox.setToolTip("\n".join(columns))
            self.group_checkboxes[group_name] = checkbox

            group_layout.addWidget(checkbox)

            # Thêm vào grid layout
            checkbox_layout.addLayout(group_layout, row, col)

        # Widget cho các checkbox
        checkbox_container = QWidget()
        checkbox_container.setLayout(checkbox_layout)
        edit_layout.addWidget(checkbox_container, 3, 0, 1, 4)

        # Nút chức năng trong phần edit
        action_layout = QHBoxLayout()

        self.save_button = QPushButton("Lưu điều kiện")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.delete_button = QPushButton("Xóa điều kiện")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)

        self.save_button.clicked.connect(self.save_condition)
        self.delete_button.clicked.connect(self.delete_condition)

        action_layout.addWidget(self.save_button)
        action_layout.addWidget(self.delete_button)

        edit_layout.addLayout(action_layout, 4, 0, 1, 4)
        edit_group.setLayout(edit_layout)

        # Thêm các phần vào layout chính
        layout.addWidget(list_group, 3)  # Chiếm 3 phần
        layout.addWidget(edit_group, 2)  # Chiếm 2 phần

        # Nút đóng ở dưới cùng
        close_layout = QHBoxLayout()
        self.ok_button = QPushButton("Hoàn tất")
        self.ok_button.setFixedSize(120, 30)
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.ok_button.clicked.connect(self.accept)

        self.cancel_button = QPushButton("Hủy")
        self.cancel_button.setFixedSize(120, 30)
        self.cancel_button.clicked.connect(self.reject)

        close_layout.addStretch()
        close_layout.addWidget(self.cancel_button)
        close_layout.addWidget(self.ok_button)

        layout.addLayout(close_layout)

        self.setLayout(layout)

        # Cập nhật bảng ban đầu
        self.load_conditions_table()
        self.clear_condition_form()

    def load_conditions_table(self):
        """Cập nhật bảng điều kiện xử lý"""
        self.condition_table.setRowCount(0)  # Xóa tất cả các dòng hiện tại

        for i, (condition_name, condition_info) in enumerate(self.process_conditions.items()):
            self.condition_table.insertRow(i)

            # Cột tên điều kiện
            self.condition_table.setItem(i, 0, QTableWidgetItem(condition_name))

            # Cột mô tả
            self.condition_table.setItem(i, 1, QTableWidgetItem(condition_info["description"]))

            # Cột nhóm cột (hiển thị dưới dạng danh sách)
            column_groups = ", ".join(condition_info["column_groups"])
            self.condition_table.setItem(i, 2, QTableWidgetItem(column_groups))

    def load_condition(self, row, col):
        """Tải thông tin điều kiện được chọn vào form chỉnh sửa"""
        condition_name = self.condition_table.item(row, 0).text()
        if condition_name in self.process_conditions:
            condition_info = self.process_conditions[condition_name]

            # Cập nhật form
            self.condition_name.setText(condition_name)
            self.condition_desc.setText(condition_info["description"])

            # Cập nhật checkbox
            for group_name, checkbox in self.group_checkboxes.items():
                checkbox.setChecked(group_name in condition_info["column_groups"])

    def clear_condition_form(self):
        """Xóa thông tin trong form để tạo điều kiện mới"""
        self.condition_name.setText("")
        self.condition_desc.setText("")

        # Bỏ chọn tất cả checkbox
        for checkbox in self.group_checkboxes.values():
            checkbox.setChecked(False)

        # Focus vào ô tên điều kiện để người dùng bắt đầu nhập
        self.condition_name.setFocus()

    def save_condition(self):
        """Lưu thông tin điều kiện từ form"""
        condition_name = self.condition_name.text().strip().lower()
        description = self.condition_desc.text().strip()

        if not condition_name:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập tên điều kiện.")
            return

        if not description:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập mô tả cho điều kiện.")
            return

        # Lấy danh sách nhóm cột được chọn
        selected_groups = []
        for group_name, checkbox in self.group_checkboxes.items():
            if checkbox.isChecked():
                selected_groups.append(group_name)

        if not selected_groups:
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn ít nhất một nhóm cột.")
            return

        # Kiểm tra xem đây là điều kiện mới hay sửa điều kiện cũ
        is_new = condition_name not in self.process_conditions

        # Cập nhật hoặc thêm mới điều kiện
        self.process_conditions[condition_name] = {
            "column_groups": selected_groups,
            "description": description
        }

        # Đánh dấu rằng có thay đổi
        self.modified = True

        # Cập nhật bảng
        self.load_conditions_table()

        # Tự động chọn điều kiện vừa lưu trong bảng
        for row in range(self.condition_table.rowCount()):
            if self.condition_table.item(row, 0).text() == condition_name:
                self.condition_table.selectRow(row)
                break

        # Hiển thị thông báo thành công
        message = f"Đã {'thêm' if is_new else 'cập nhật'} điều kiện '{condition_name}'.\n"
        message += "Điều kiện sẽ được lưu tự động khi bạn nhấn nút 'Hoàn tất'."

        QMessageBox.information(self, "Thành công", message)

    def delete_condition(self):
        """Xóa điều kiện hiện tại"""
        condition_name = self.condition_name.text().strip().lower()

        if not condition_name or condition_name not in self.process_conditions:
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn một điều kiện để xóa.")
            return

        # Xác nhận xóa
        reply = QMessageBox.question(
            self, "Xác nhận xóa",
            f"Bạn có chắc chắn muốn xóa điều kiện '{condition_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Xóa điều kiện
            del self.process_conditions[condition_name]

            # Đánh dấu rằng có thay đổi
            self.modified = True

            # Cập nhật bảng và xóa form
            self.load_conditions_table()
            self.clear_condition_form()

            # Hiển thị thông báo thành công
            QMessageBox.information(self, "Thành công", f"Đã xóa điều kiện '{condition_name}'.")

    def get_process_conditions(self):
        """Trả về dữ liệu điều kiện xử lý đã cập nhật"""
        return self.process_conditions

# Thiết lập logging cho toàn bộ ứng dụng
log_dir = os.path.join(os.path.expanduser("~"), "DataAssortmentLogs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"data_assortment_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("DataAssortmentAIO")

# Lớp xử lý dữ liệu
class DataProcessor(QThread):
    update_progress = pyqtSignal(int)
    update_log = pyqtSignal(str)
    update_processing_detail = pyqtSignal(str, str)  # Thêm signal mới, gửi (message, level)
    finished = pyqtSignal()
    error = pyqtSignal(str)

    def __init__(self, source_link, target_link, source_sheet, target_sheet, start_row, header_row, source_mapping, target_mapping, manager, process_conditions=None):
        super().__init__()
        self.source_link = source_link
        self.target_link = target_link
        self.source_sheet = source_sheet
        self.target_sheet = target_sheet
        self.start_row = start_row
        self.header_row = header_row
        self.source_mapping = source_mapping
        self.target_mapping = target_mapping
        self.manager = manager
        # Sử dụng PROCESS_CONDITIONS toàn cục nếu không có tham số được truyền vào
        self.process_conditions = process_conditions if process_conditions is not None else PROCESS_CONDITIONS
        self.batch_size = 150        # Quay về 150 thay vì 250
        self.batch_row_limit = 100   # Quay về 100 thay vì 150
        self.status = "idle"  # Trạng thái xử lý: idle, running, error, finished
        self.mutex = QMutex()  # Mutex để bảo vệ truy cập dữ liệu giữa các luồng
        self.abort_flag = False  # Cờ để báo hiệu hủy bỏ thực thi
        self.current_batch = 0  # Batch hiện tại đang xử lý
        self.total_batches = 0  # Tổng số batch
        self.api_min_delay = 0.3     # Giữ nguyên ở 0.3
        self.last_api_call = 0  # Thời điểm của lần gọi API cuối cùng
        self.max_retries = 3  # Số lần thử lại tối đa khi gọi API thất bại
        self.processed_records = 0  # Số bản ghi đã xử lý
        self.updated_records = 0    # Số bản ghi đã cập nhật thành công

    # Các cột cần định dạng số
    NUMBER_FORMAT_COLUMNS = [
        "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
        "Mức giảm tối đa",
        "Áp dụng cho đơn từ",
        "Số lượng tồn kho",
        "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
    ]

    # Các cột cần định dạng phần trăm
    PERCENT_FORMAT_COLUMNS = [
        "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
        "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
    ]

    def abort(self):
        """Báo hiệu dừng xử lý"""
        self.mutex.lock()
        self.abort_flag = True
        self.mutex.unlock()
        self.update_log.emit("Đã nhận lệnh dừng xử lý")

    def should_abort(self):
        """Kiểm tra có nên dừng xử lý hay không"""
        self.mutex.lock()
        abort = self.abort_flag
        self.mutex.unlock()
        return abort

    def run(self):
        global_exception_handler = None
        try:
            # Đặt exception handler toàn cục cho thread này
            import sys
            original_excepthook = sys.excepthook

            def global_exception_handler(exc_type, exc_value, exc_traceback):
                self.error.emit(f"Lỗi nghiêm trọng: {exc_type.__name__}: {exc_value}")
                self.update_log.emit(f"Chi tiết lỗi: {''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}")
                original_excepthook(exc_type, exc_value, exc_traceback)

            sys.excepthook = global_exception_handler

            self.status = "running"
            self.update_log.emit("===== Bắt đầu quá trình xử lý dữ liệu =====")

            # Tất cả các bước chuẩn bị ban đầu
            source_spreadsheet, target_spreadsheet, source_worksheet, target_worksheet, source_header_map, target_header_map = self.prepare_sheets()
            if self.should_abort() or self.status == "error":
                self.update_log.emit("Quá trình chuẩn bị gặp lỗi hoặc bị hủy, dừng xử lý")
                return

            # Lấy dữ liệu từ sheets
            source_data, target_data = self.load_sheet_data(source_worksheet, target_worksheet)
            if self.should_abort() or self.status == "error":
                self.update_log.emit("Quá trình tải dữ liệu gặp lỗi hoặc bị hủy, dừng xử lý")
                return

            # Xác định phạm vi dữ liệu cần xử lý
            start_row, end_row, total_rows = self.determine_data_range(source_data)
            if self.should_abort() or self.status == "error" or total_rows <= 0:
                self.update_log.emit("Không có dữ liệu để xử lý.")
                self.status = "error"
                return

            # Chia thành các batch và xử lý
            self.process_data_in_batches(source_data, target_data, source_worksheet, target_worksheet,
                                         source_header_map, target_header_map, start_row, end_row, total_rows)

            if self.should_abort():
                self.update_log.emit("Quá trình xử lý dữ liệu bị hủy bởi người dùng")
                self.status = "aborted"
            else:
                # Sửa đổi: Chỉ hiển thị thông báo "Xử lý dữ liệu hoàn tất" một lần
                if self.status != "finished":  # Kiểm tra xem đã hoàn thành chưa
                    self.update_log.emit("===== Xử lý dữ liệu hoàn tất =====")
                    self.status = "finished"

            self.finished.emit()

        except Exception as e:
            self.error.emit(f"Lỗi không xác định trong quá trình xử lý: {str(e)}")
            self.update_log.emit(f"Chi tiết lỗi: {traceback.format_exc()}")
            self.status = "error"

        finally:
            # Khôi phục exception handler
            if global_exception_handler:
                sys.excepthook = original_excepthook

    def prepare_sheets(self):
        """Chuẩn bị kết nối và lấy thông tin sheets"""
        try:
            # Mở spreadsheet
            self.update_log.emit("Đang kết nối đến Google Sheets...")
            source_spreadsheet = self.manager.open_by_key(self.extract_spreadsheet_id(self.source_link))
            target_spreadsheet = self.manager.open_by_key(self.extract_spreadsheet_id(self.target_link))
            self.update_log.emit("Đã kết nối với cả hai spreadsheet thành công")

            # Lấy sheet
            source_worksheet = source_spreadsheet.worksheet(self.source_sheet)
            target_worksheet = target_spreadsheet.worksheet(self.target_sheet)
            self.update_log.emit(f"Đã tìm thấy worksheet: {self.source_sheet} và {self.target_sheet}")

            # Chuyển ánh xạ cột thành số
            source_header_map = {}
            for header, column_letter in self.source_mapping.items():
                if column_letter:
                    source_header_map[header] = column_letter_to_number(column_letter)

            target_header_map = {}
            for header, column_letter in self.target_mapping.items():
                if column_letter:
                    target_header_map[header] = column_letter_to_number(column_letter)

            # Kiểm tra cột bắt buộc
            required_headers = [
                "Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu", "Tên sản phẩm",
                "Số lượng tồn kho", "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"
            ]
            missing_headers = [header for header in required_headers if header not in source_header_map or not source_header_map[header]]
            if missing_headers:
                self.error.emit(f"Thiếu cột bắt buộc trong sheet nguồn: {', '.join(missing_headers)}")
                self.status = "error"
                return None, None, None, None, None, None

            required_target_headers = [
                "Mã sản phẩm (Item ID)", "Mã phân loại (Model ID)", "Tên sản phẩm",
                "Số lượng tồn kho", "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"
            ]
            missing_target_headers = [header for header in required_target_headers if header not in target_header_map or not target_header_map[header]]
            if missing_target_headers:
                self.error.emit(f"Thiếu cột bắt buộc trong sheet đích: {', '.join(missing_target_headers)}")
                self.status = "error"
                return None, None, None, None, None, None

            return source_spreadsheet, target_spreadsheet, source_worksheet, target_worksheet, source_header_map, target_header_map

        except Exception as e:
            self.error.emit(f"Lỗi khi chuẩn bị sheet: {str(e)}")
            self.update_log.emit(f"Chi tiết lỗi: {traceback.format_exc()}")
            self.status = "error"
            return None, None, None, None, None, None

    def load_sheet_data(self, source_worksheet, target_worksheet):
        """Tải dữ liệu từ Google Sheet"""
        try:
            # Lấy dữ liệu từ source sheet
            self.update_log.emit("Đang tải dữ liệu từ sheet nguồn...")
            source_data = source_worksheet.get_all_values()
            self.update_log.emit(f"Đã tải {len(source_data)} dòng từ sheet nguồn")

            # Lấy dữ liệu từ target sheet
            self.update_log.emit("Đang tải dữ liệu từ sheet đích...")
            target_data = target_worksheet.get_all_values()
            self.update_log.emit(f"Đã tải {len(target_data)} dòng từ sheet đích")

            return source_data, target_data

        except Exception as e:
            self.error.emit(f"Lỗi khi tải dữ liệu: {str(e)}")
            self.update_log.emit(f"Chi tiết lỗi: {traceback.format_exc()}")
            self.status = "error"
            return None, None

    def determine_data_range(self, source_data):
        """Xác định phạm vi dữ liệu cần xử lý"""
        try:
            # Xác định dòng bắt đầu
            if self.start_row:
                # Nếu đã nhập dòng bắt đầu cụ thể, sử dụng giá trị đó (trừ 1 vì index bắt đầu từ 0)
                start_row = self.start_row - 1
            else:
                # Tìm dòng "PA đã action đến đây"
                start_row = self.find_start_row(source_data)

            self.update_log.emit(f"Bắt đầu xử lý từ dòng {start_row + 1}")

            # Tìm dòng cuối cùng (dòng có cột A rỗng)
            end_row = start_row
            while end_row < len(source_data) and source_data[end_row][0]:
                end_row += 1
            total_rows = end_row - start_row

            if total_rows <= 0:
                self.error.emit("Không có dữ liệu để xử lý.")
                self.status = "error"
                return 0, 0, 0

            return start_row, end_row, total_rows

        except Exception as e:
            self.error.emit(f"Lỗi khi xác định phạm vi dữ liệu: {str(e)}")
            self.update_log.emit(f"Chi tiết lỗi: {traceback.format_exc()}")
            self.status = "error"
            return 0, 0, 0

    def process_data_in_batches(self, source_data, target_data, source_worksheet, target_worksheet,
                               source_header_map, target_header_map, start_row, end_row, total_rows):
        """Xử lý dữ liệu theo các batch"""
        try:
            # Xử lý dữ liệu theo batch, mỗi batch self.batch_row_limit dòng
            current_row = start_row
            batch_count = 0
            self.total_batches = (total_rows + self.batch_row_limit - 1) // self.batch_row_limit # Làm tròn lên
            self.processed_records = 0
            self.updated_records = 0

            # Hiển thị thông tin tổng quan về dữ liệu cần xử lý
            total_time_est = self.total_batches * 1.5  # ước tính 1.5 phút mỗi batch
            self.log_to_file(f"📊 THÔNG TIN XỬ LÝ:", "INFO")
            self.log_to_file(f"   • Tổng số dòng: {total_rows} dòng", "INFO")
            self.log_to_file(f"   • Số batch: {self.total_batches} batch (mỗi batch {self.batch_row_limit} dòng)", "INFO")
            self.log_to_file(f"   • Thời gian ước tính: {total_time_est:.1f} phút", "INFO")
            self.log_to_file(f"   • Bắt đầu từ dòng: {start_row + 1}, kết thúc tại dòng: {end_row}", "INFO")
            self.log_to_file("🔄 ĐANG XỬ LÝ DỮ LIỆU...", "INFO")

            while current_row < end_row and not self.should_abort():
                try:
                    batch_count += 1
                    self.current_batch = batch_count
                    batch_end = min(current_row + self.batch_row_limit, end_row)
                    batch_rows = batch_end - current_row

                    start_time = time.time()
                    self.log_to_file(f"⏳ Đang xử lý batch {batch_count}/{self.total_batches} (dòng {current_row + 1}-{batch_end})", "INFO")

                    # Xử lý từng batch riêng biệt
                    updates_made = self.process_single_batch(source_data, target_data, target_worksheet, source_header_map,
                                             target_header_map, current_row, batch_end, batch_count, start_row, total_rows)

                    # Dùng QTimer để đảm bảo UI được cập nhật sau mỗi batch
                    QApplication.processEvents()

                    # Ngừng một chút để tránh hạn chế API
                    time.sleep(0.5)

                    # Cập nhật thống kê
                    self.processed_records += batch_rows

                    # Tính thời gian xử lý
                    end_time = time.time()
                    duration = end_time - start_time

                    # Cập nhật hàng hiện tại cho batch tiếp theo
                    self.log_to_file(f"✅ Đã hoàn thành batch {batch_count}/{self.total_batches} trong {duration:.1f} giây", "SUCCESS")
                    current_row = batch_end

                except Exception as e:
                    self.log_to_file(f"Lỗi khi xử lý batch {batch_count}: {str(e)}", "ERROR")
                    self.log_to_file(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
                    self.log_to_file("Đang cố gắng tiếp tục với batch tiếp theo...", "WARNING")
                    # Cố gắng tiếp tục với batch tiếp theo
                    current_row = batch_end

            # Hiển thị thống kê sau khi hoàn thành
            self.log_to_file("📊 KẾT QUẢ XỬ LÝ:", "INFO")
            self.log_to_file(f"   • Tổng số dòng đã xử lý: {self.processed_records}/{total_rows} dòng", "INFO")
            self.log_to_file(f"   • Tổng số cập nhật thành công: {self.updated_records} mục", "INFO")

            # Chỉ cập nhật "PA đã action đến đây" sau khi xử lý tất cả batch
            if not self.should_abort():
                try:
                    self.log_to_file("🔄 Đang cập nhật 'PA đã action đến đây'...", "INFO")
                    self.update_pa_action(source_worksheet, end_row)
                    self.log_to_file("✅ Đã cập nhật 'PA đã action đến đây' thành công", "SUCCESS")
                except Exception as e:
                    self.log_to_file(f"Lỗi khi cập nhật 'PA đã action đến đây': {str(e)}", "ERROR")
                    self.log_to_file(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")

        except Exception as e:
            self.log_to_file(f"Lỗi trong quá trình xử lý batch: {str(e)}", "ERROR")
            self.log_to_file(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")

    def process_single_batch(self, source_data, target_data, target_worksheet, source_header_map,
                            target_header_map, current_row, batch_end, batch_count, start_row, total_rows):
        """Xử lý một batch dữ liệu duy nhất"""
        try:
            updates = []  # Danh sách cập nhật cho batch hiện tại

            # Danh sách các điều kiện cần bỏ qua
            skip_conditions = ["rút deal", "đổi review thành atc", "rút phân loại"]

            # Xử lý từng dòng trong batch hiện tại
            for i, row in enumerate(source_data[current_row:batch_end], start=current_row):
                if self.should_abort():
                    self.log_to_file(f"Đã nhận lệnh dừng khi đang xử lý dòng {i + 1}")
                    return

                try:
                    condition = row[0].strip() if row and len(row) > 0 else ""
                    if not condition:
                        self.log_to_file(f"Bỏ qua dòng {i + 1} vì không có điều kiện xử lý")
                        continue

                    # Kiểm tra và bỏ qua các điều kiện đã chỉ định hoặc không định nghĩa
                    if condition.lower() in skip_conditions:
                        self.log_to_file(f"Bỏ qua dòng {i + 1} với điều kiện '{condition}' theo yêu cầu", "INFO")
                        continue

                    # Kiểm tra xem điều kiện có được định nghĩa trong process_conditions không
                    if condition.lower() not in self.process_conditions:
                        self.log_to_file(f"Bỏ qua dòng {i + 1} với điều kiện '{condition}' vì không có định nghĩa xử lý", "WARNING")
                        continue

                    # Lấy dữ liệu từ sheet nguồn
                    try:
                        item_id = row[source_header_map["Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu"] - 1].strip()
                        model_id = row[source_header_map["Mã phân loại (Model ID)"] - 1].strip() if "Mã phân loại (Model ID)" in source_header_map else ""
                        product_name = row[source_header_map["Tên sản phẩm"] - 1]
                    except (IndexError, KeyError) as e:
                        self.log_to_file(f"Lỗi khi đọc dữ liệu dòng {i + 1}: {str(e)}", "ERROR")
                        continue

                    # Tìm dòng khớp trong target sheet
                    found = False
                    item_id_numeric = None

                    # Kiểm tra xem item_id có phải số không
                    try:
                        item_id_numeric = int(str(item_id).strip())
                    except (ValueError, TypeError):
                        item_id_numeric = None

                    for j, target_row in enumerate(target_data[self.header_row:], start=self.header_row + 1):
                        try:
                            target_item_id = target_row[target_header_map["Mã sản phẩm (Item ID)"] - 1].strip() if len(target_row) >= target_header_map["Mã sản phẩm (Item ID)"] else ""
                            target_model_id = target_row[target_header_map["Mã phân loại (Model ID)"] - 1].strip() if "Mã phân loại (Model ID)" in target_header_map and len(target_row) >= target_header_map["Mã phân loại (Model ID)"] else ""

                            # Chuẩn hóa ID sản phẩm thành chuỗi để so sánh chính xác
                            source_item_id = str(item_id).strip()
                            normalized_target_id = str(target_item_id).strip()

                            # Kiểm tra nhiều điều kiện khớp
                            is_matching = False

                            # Kiểm tra khớp chuỗi
                            if normalized_target_id == source_item_id:
                                is_matching = True
                            # Kiểm tra khớp số nếu cả hai là số
                            elif item_id_numeric is not None:
                                try:
                                    target_id_numeric = int(normalized_target_id)
                                    if target_id_numeric == item_id_numeric:
                                        is_matching = True
                                except (ValueError, TypeError):
                                    pass

                            # Nếu ID khớp và model ID cũng khớp (nếu có)
                            if is_matching and (not model_id or str(target_model_id).strip() == str(model_id).strip()):
                                self.process_row(condition, row, target_row, target_worksheet, source_header_map, target_header_map, j, updates)
                                found = True
                                break
                        except (IndexError, KeyError) as e:
                            self.log_to_file(f"Lỗi khi so khớp dòng đích {j}: {str(e)}", "WARNING")
                            continue

                    if not found:
                        # Thêm thông tin debug chi tiết
                        debug_info = f"ID tìm kiếm: '{item_id}'"
                        if item_id_numeric is not None:
                            debug_info += f" (dạng số: {item_id_numeric})"

                        # Log thêm thông tin header để debug
                        header_info = ""
                        if "Mã sản phẩm (Item ID)" in target_header_map:
                            header_col = target_header_map["Mã sản phẩm (Item ID)"]
                            header_info = f" (cột {header_col})"

                        # Tìm và hiển thị ID gần giống trong target data
                        similar_ids = []
                        for idx, target_row in enumerate(target_data[self.header_row:self.header_row+20], start=self.header_row + 1):
                            try:
                                if len(target_row) >= target_header_map["Mã sản phẩm (Item ID)"]:
                                    target_id = target_row[target_header_map["Mã sản phẩm (Item ID)"] - 1].strip()
                                    # Nếu ID có nội dung giống
                                    if str(item_id) in target_id or target_id in str(item_id) or (
                                       item_id_numeric is not None and str(item_id_numeric) in target_id):
                                        similar_ids.append(f"'{target_id}' (dòng {idx})")
                                        if len(similar_ids) >= 5:  # Giới hạn số lượng ID hiển thị
                                            break
                            except (IndexError, KeyError):
                                continue

                        if similar_ids:
                            debug_info += f" - ID tương tự tìm thấy{header_info}: {', '.join(similar_ids)}"
                            self.log_to_file(f"Không tìm thấy sản phẩm {product_name} (ID: {item_id}) trong sheet đích. {debug_info}", "WARNING")
                        else:
                            self.log_to_file(f"Không tìm thấy sản phẩm {product_name} (ID: {item_id}) trong sheet đích{header_info}")

                    # Ghi dữ liệu theo lô nếu danh sách cập nhật đủ lớn
                    if len(updates) >= self.batch_size:
                        try:
                            success = self.batch_update_values_with_retry(target_worksheet, updates)
                            if success:
                                self.log_to_file(f"Đã cập nhật batch dữ liệu (dòng {i + 1})")
                                updates = []  # Tạo mới danh sách updates thay vì clear
                            else:
                                self.log_to_file(f"Không thể cập nhật dữ liệu batch tại dòng {i + 1} sau nhiều lần thử", "ERROR")
                                # Thử tiếp tục với một batch mới, bỏ qua các cập nhật thất bại
                                updates = []
                        except Exception as e:
                            self.log_to_file(f"Lỗi nghiêm trọng khi cập nhật batch tại dòng {i + 1}: {str(e)}", "ERROR")
                            self.log_to_file(f"Chi tiết: {traceback.format_exc()}", "ERROR")
                            updates = []  # Reset updates để tiếp tục

                    # Cập nhật tiến trình dựa trên tổng số dòng
                    progress = int(((i - start_row + 1) / total_rows) * 100)
                    self.update_progress.emit(progress)

                    # Đảm bảo UI được cập nhật
                    QApplication.processEvents()

                except Exception as e:
                    self.log_to_file(f"Lỗi khi xử lý dòng {i + 1}: {str(e)}", "ERROR")
                    self.log_to_file(f"Chi tiết: {traceback.format_exc()}", "ERROR")
                    # Tiếp tục vòng lặp mặc dù có lỗi ở dòng hiện tại
                    continue

            # Ghi các cập nhật còn lại trong batch hiện tại
            if updates and not self.should_abort():
                try:
                    success = self.batch_update_values_with_retry(target_worksheet, updates)
                    if success:
                        self.log_to_file(f"Đã cập nhật {len(updates)} dữ liệu còn lại từ batch {batch_count}")
                    else:
                        self.log_to_file(f"Không thể cập nhật dữ liệu còn lại từ batch {batch_count} sau nhiều lần thử", "ERROR")
                except Exception as e:
                    self.log_to_file(f"Lỗi khi cập nhật dữ liệu cuối batch {batch_count}: {str(e)}", "ERROR")
                    self.log_to_file(f"Chi tiết: {traceback.format_exc()}", "ERROR")

        except Exception as e:
            self.log_to_file(f"Lỗi trong quá trình xử lý batch {batch_count}: {str(e)}", "ERROR")
            self.log_to_file(f"Chi tiết: {traceback.format_exc()}", "ERROR")

    def log_to_file(self, message, level="INFO"):
        """Ghi log vào file và hiển thị trên UI"""
        message = str(message).strip()
        if not message:
            return

        # Kiểm tra xem thông báo đã có biểu tượng chưa
        has_emoji = any(emoji in message for emoji in ["✅", "⚠️", "❌", "🔴", "📊", "🔄"])

        # Định dạng theo loại log
        prefix = ""
        if level == "INFO":
            logger.info(message)
            # Không thêm prefix cho info log
        elif level == "WARNING":
            logger.warning(message)
            if not has_emoji:
                prefix = "⚠️ CẢNH BÁO: "
        elif level == "ERROR":
            logger.error(message)
            if not has_emoji:
                prefix = "❌ LỖI: "
        elif level == "CRITICAL":
            logger.critical(message)
            if not has_emoji:
                prefix = "🔴 LỖI NGHIÊM TRỌNG: "
        elif level == "SUCCESS":
            logger.info(f"[SUCCESS] {message}")
            if not has_emoji:
                prefix = "✅ "

        # Hiển thị thông báo trên UI cho log chính
        self.update_log.emit(f"{prefix}{message}")

        # Gửi chi tiết xử lý để hiển thị trong dialog thông qua signal mới
        formatted_message = f"{prefix}{message}"
        self.update_processing_detail.emit(formatted_message, level)

    def wait_between_api_calls(self):
        """Chờ giữa các lần gọi API để tránh vượt quá giới hạn tần suất"""
        current_time = time.time()
        elapsed = current_time - self.last_api_call
        if elapsed < self.api_min_delay:
            wait_time = self.api_min_delay - elapsed
            time.sleep(wait_time)
        self.last_api_call = time.time()

    def extract_spreadsheet_id(self, link):
        """Trích xuất Spreadsheet ID từ URL Google Sheet"""
        # Xử lý các dạng URL khác nhau
        if not link:
            return ""

        # Nếu link đã là spreadsheet ID (không chứa / hoặc .)
        if '/' not in link and '.' not in link and len(link) >= 25:
            return link

        # Trích xuất từ full URL
        if '/d/' in link:
            # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
            id_part = link.split('/d/')[1]
            return id_part.split('/')[0].split('?')[0].split('#')[0]
        elif 'spreadsheets/d/' in link:
            # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
            id_part = link.split('spreadsheets/d/')[1]
            return id_part.split('/')[0].split('?')[0].split('#')[0]
        elif 'key=' in link:
            # Format: https://docs.google.com/spreadsheets/d/key=SPREADSHEET_ID
            id_part = link.split('key=')[1]
            return id_part.split('&')[0].split('#')[0]

        # Trả về chính link nếu không phù hợp với bất kỳ định dạng nào
        return link

    def find_start_row(self, data):
        for i, row in enumerate(data):
            if any(cell and "PA đã action đến đây" in cell for cell in row):
                return i + 2  # Dòng sau "PA đã action đến đây"
        return 3  # Dòng 3 mặc định (index bắt đầu từ 1 trong UI)

    def process_row(self, condition, source_row, target_row, target_worksheet, source_header_map, target_header_map, row_index, updates):
        condition = condition.strip().lower()

        # Lấy thông tin sản phẩm cho log
        product_name = source_row[source_header_map["Tên sản phẩm"] - 1]
        model_id = source_row[source_header_map["Mã phân loại (Model ID)"] - 1].strip() if "Mã phân loại (Model ID)" in source_header_map else ""

        # Đơn giản hóa log sản phẩm
        item_id = source_row[source_header_map["Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu"] - 1].strip()
        short_name = product_name
        if len(short_name) > 50:
            short_name = short_name[:47] + "..."

        # Ghi log gọn gàng
        log_message = f"Xử lý dòng {row_index} | {condition.upper()} | {short_name} (ID: {item_id})"
        if model_id:
            log_message += f" | Model: {model_id}"
        self.log_to_file(log_message)

        # Đếm số trường được cập nhật
        updates_before = len(updates)

        # Kiểm tra xem điều kiện có tồn tại trong danh sách cấu hình hay không
        if condition in self.process_conditions:
            # Sử dụng cấu hình từ self.process_conditions
            condition_config = self.process_conditions[condition]
            column_groups = condition_config["column_groups"]

            # Xử lý từng nhóm cột được chỉ định
            for group_name in column_groups:
                if group_name in COLUMN_GROUPS:
                    # Lấy danh sách cột trong nhóm
                    columns = COLUMN_GROUPS[group_name]

                    # Xử lý từng cột trong nhóm
                    for col in columns:
                        if col in target_header_map and col in source_header_map:
                            # Trường hợp đặc biệt: xóa quà tặng
                            if condition == "xoá quà" and group_name == "gift":
                                col_index = target_header_map[col]
                                updates.append((row_index, col_index, ""))
                            else:
                                # Trường hợp thông thường: sao chép giá trị từ nguồn sang đích
                                col_index = target_header_map[col]
                                source_col_index = source_header_map[col]
                                new_value = source_row[source_col_index - 1]

                                # Xử lý giá trị mặc định cho voucher
                                if group_name == "voucher" and not new_value and col != "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm":
                                    new_value = "0"

                                updates.append((row_index, col_index, new_value))
        else:
            # Fallback: Sử dụng logic cũ nếu điều kiện không nằm trong danh sách cấu hình
            self.log_to_file(f"Cảnh báo: Điều kiện '{condition}' không được định nghĩa trong cấu hình. Sử dụng xử lý mặc định.", "WARNING")

            # Danh sách các cột cần cập nhật
            if condition == "đổi stock":
                if "Số lượng tồn kho" in target_header_map:
                    stock_col = target_header_map["Số lượng tồn kho"]
                    new_value = source_row[source_header_map["Số lượng tồn kho"] - 1]
                    updates.append((row_index, stock_col, new_value))

            elif condition == "đổi giá":
                if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                    price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                    new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                    updates.append((row_index, price_col, new_value))

            elif condition in ["thêm quà", "đổi quà"]:
                gift_cols = [
                    "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                    "Giá trị quà tặng kèm", "Link quà tặng kèm",
                    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
                ]
                for col in gift_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1]
                        updates.append((row_index, col_index, new_value))

            elif condition == "đổi giá và quà":
                if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                    price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                    new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                    updates.append((row_index, price_col, new_value))
                gift_cols = [
                    "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                    "Giá trị quà tặng kèm", "Link quà tặng kèm",
                    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
                ]
                for col in gift_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1]
                        updates.append((row_index, col_index, new_value))

            elif condition == "cms":
                if "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm" in target_header_map:
                    cms_col = target_header_map["Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"]
                    new_value = source_row[source_header_map["Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"] - 1]
                    updates.append((row_index, cms_col, new_value))

            elif condition == "xoá quà":
                gift_cols = [
                    "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                    "Giá trị quà tặng kèm", "Link quà tặng kèm",
                    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
                ]
                for col in gift_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        updates.append((row_index, col_index, ""))

            elif condition == "đổi giá và stock":
                if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                    price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                    new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                    updates.append((row_index, price_col, new_value))
                if "Số lượng tồn kho" in target_header_map:
                    stock_col = target_header_map["Số lượng tồn kho"]
                    new_value = source_row[source_header_map["Số lượng tồn kho"] - 1]
                    updates.append((row_index, stock_col, new_value))

            elif condition == "đổi tên sp + đổi giá":
                if "Tên sản phẩm" in target_header_map:
                    name_col = target_header_map["Tên sản phẩm"]
                    new_value = source_row[source_header_map["Tên sản phẩm"] - 1]
                    updates.append((row_index, name_col, new_value))
                if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                    price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                    new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                    updates.append((row_index, price_col, new_value))

            elif condition == "thêm voucher, quà và thay đổi stock":
                voucher_cols = [
                    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)", "Mức giảm tối đa", "Áp dụng cho đơn từ",
                    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
                ]
                gift_cols = [
                    "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                    "Giá trị quà tặng kèm", "Link quà tặng kèm",
                    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
                ]
                if "Số lượng tồn kho" in target_header_map:
                    stock_col = target_header_map["Số lượng tồn kho"]
                    new_value = source_row[source_header_map["Số lượng tồn kho"] - 1]
                    updates.append((row_index, stock_col, new_value))
                for col in voucher_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1] if source_row[source_col_index - 1] else "0"
                        updates.append((row_index, col_index, new_value))
                for col in gift_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1]
                        updates.append((row_index, col_index, new_value))

            elif condition == "thêm voucher và stock":
                voucher_cols = [
                    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)", "Mức giảm tối đa", "Áp dụng cho đơn từ",
                    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
                ]
                if "Số lượng tồn kho" in target_header_map:
                    stock_col = target_header_map["Số lượng tồn kho"]
                    new_value = source_row[source_header_map["Số lượng tồn kho"] - 1]
                    updates.append((row_index, stock_col, new_value))
                for col in voucher_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1] if source_row[source_col_index - 1] else "0"
                        updates.append((row_index, col_index, new_value))

            elif condition == "đổi giá và thêm voucher":
                if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                    price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                    new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                    updates.append((row_index, price_col, new_value))
                voucher_cols = [
                    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)", "Mức giảm tối đa", "Áp dụng cho đơn từ",
                    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
                ]
                for col in voucher_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1] if source_row[source_col_index - 1] else "0"
                        updates.append((row_index, col_index, new_value))

            elif condition == "thêm voucher + thêm quà":
                voucher_cols = [
                    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)", "Mức giảm tối đa", "Áp dụng cho đơn từ",
                    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
                ]
                gift_cols = [
                    "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                    "Giá trị quà tặng kèm", "Link quà tặng kèm",
                    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
                ]
                for col in voucher_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1] if source_row[source_col_index - 1] else "0"
                        updates.append((row_index, col_index, new_value))
                for col in gift_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1]
                        updates.append((row_index, col_index, new_value))

            elif condition == "thêm voucher":
                voucher_cols = [
                    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)", "Mức giảm tối đa", "Áp dụng cho đơn từ",
                    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
                ]
                for col in voucher_cols:
                    if col in target_header_map:
                        col_index = target_header_map[col]
                        source_col_index = source_header_map[col]
                        new_value = source_row[source_col_index - 1] if source_row[source_col_index - 1] else "0"
                        updates.append((row_index, col_index, new_value))

        # Đếm số trường đã cập nhật
        updates_after = len(updates)
        updated_fields = updates_after - updates_before
        if updated_fields > 0:
            self.updated_records += 1

    def get_format_for_column(self, column_name):
        """Xác định định dạng cần áp dụng cho cột dựa trên tên cột"""
        if column_name in self.NUMBER_FORMAT_COLUMNS:
            return {
                "numberFormat": {
                    "type": "NUMBER",
                    "pattern": "#########"
                }
            }
        elif column_name in self.PERCENT_FORMAT_COLUMNS:
            return {
                "numberFormat": {
                    "type": "PERCENT",
                    "pattern": "0%"
                }
            }
        return None  # Không áp dụng định dạng đặc biệt

    def batch_update_values_with_retry(self, worksheet, updates):
        """Thực hiện cập nhật giá trị với cơ chế thử lại"""
        if not updates:
            return True

        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    self.log_to_file(f"Thử lại lần {attempt+1} cho {len(updates)} cập nhật...", "WARNING")
                else:
                    self.log_to_file(f"Đang cập nhật {len(updates)} mục dữ liệu", "INFO")

                # Chờ để tránh vượt quá giới hạn tần suất API
                self.wait_between_api_calls()

                # Thử cập nhật dữ liệu
                self.batch_update_values(worksheet, updates)

                # Nếu thành công
                return True

            except Exception as e:
                self.log_to_file(f"Lần thử {attempt+1} thất bại: {str(e)}", "WARNING")

                # Chờ lâu hơn trước khi thử lại
                wait_time = (attempt + 1) * 2  # 2, 4, 6... giây
                self.log_to_file(f"Chờ {wait_time} giây trước khi thử lại...", "INFO")
                time.sleep(wait_time)

        # Nếu đã thử hết số lần mà vẫn thất bại
        self.log_to_file(f"Không thể cập nhật dữ liệu sau {self.max_retries} lần thử", "ERROR")
        return False

    def batch_update_values(self, worksheet, updates):
        if not updates:
            return

        try:
            service = self.manager.get_sheets_service()
            spreadsheet_id = worksheet.spreadsheet.id
            requests = []

            # Tìm ánh xạ ngược từ chỉ số cột về tên cột
            column_index_to_name = {}
            for name, index in self.target_mapping.items():
                column_index_to_name[column_letter_to_number(index)] = name

            # Nhóm các cập nhật theo hàng để giảm số lượng API call
            updates_by_row = {}
            for row_index, col_index, value in updates:
                if row_index not in updates_by_row:
                    updates_by_row[row_index] = []
                updates_by_row[row_index].append((col_index, value))

            # Xử lý từng hàng một cách riêng biệt
            for row_index, row_updates in list(updates_by_row.items()):  # Tạo một bản sao của items để tránh lỗi khi sửa đổi dict
                try:
                    # Bỏ qua các updates không hợp lệ
                    if not row_index or row_index <= 0:
                        self.log_to_file(f"Bỏ qua updates không hợp lệ cho hàng {row_index}", "WARNING")
                        continue

                    # Xác định phạm vi cần lấy
                    min_col = min(col_index - 1 for col_index, _ in row_updates)
                    max_col = max(col_index for col_index, _ in row_updates)

                    # Cập nhật từng ô trong hàng
                    for col_index, value in row_updates:
                        format_spec = None
                        if col_index in column_index_to_name:
                            column_name = column_index_to_name[col_index]
                            format_spec = self.get_format_for_column(column_name)

                        # Xử lý giá trị null/empty
                        if value is None:
                            value = ""

                        # Chuẩn bị giá trị cell
                        cell_value = {"stringValue": str(value)}

                        # Xử lý các định dạng đặc biệt (số, phần trăm)
                        if value and str(value).strip():
                            try:
                                # Xử lý định dạng phần trăm
                                if format_spec and format_spec.get("numberFormat", {}).get("type") == "PERCENT":
                                    percent_value = str(value)
                                    if "%" in percent_value:
                                        percent_value = percent_value.replace("%", "").strip()
                                    try:
                                        # Chuyển đổi thành số thập phân
                                        num_value = float(percent_value)
                                        # Google Sheets hiểu giá trị phần trăm là số thập phân (0.31 cho 31%)
                                        # Nếu giá trị >= 1, cần chia cho 100 để chuyển thành số thập phân
                                        if num_value >= 1:
                                            num_value = num_value / 100
                                        cell_value = {"numberValue": num_value}
                                    except ValueError:
                                        pass
                                # Xử lý định dạng số thông thường
                                elif format_spec and format_spec.get("numberFormat", {}).get("type") == "NUMBER":
                                    try:
                                        num_value = float(str(value).replace(',', '').strip())
                                        cell_value = {"numberValue": num_value}
                                    except ValueError:
                                        pass
                            except Exception as e:
                                self.log_to_file(f"Lỗi xử lý định dạng ô [{row_index},{col_index}]: {e}", "WARNING")

                        # Tạo dữ liệu cập nhật chỉ với giá trị mới, giữ nguyên định dạng
                        cell_data = {"userEnteredValue": cell_value}

                        # Tạo request cập nhật
                        requests.append({
                            "updateCells": {
                                "range": {
                                    "sheetId": worksheet.id,
                                    "startRowIndex": row_index - 1,
                                    "endRowIndex": row_index,
                                    "startColumnIndex": col_index - 1,
                                    "endColumnIndex": col_index
                                },
                                "rows": [{"values": [cell_data]}],
                                "fields": "userEnteredValue"  # Chỉ cập nhật giá trị, giữ nguyên định dạng
                            }
                        })
                except Exception as row_error:
                    self.log_to_file(f"Lỗi xử lý hàng {row_index}: {row_error}", "ERROR")
                    # Tiếp tục với các hàng khác

            # Nếu không có request nào, dừng lại
            if not requests:
                self.log_to_file("Không có dữ liệu hợp lệ để cập nhật", "WARNING")
                return

            # Chia nhỏ requests thành các nhóm nhỏ hơn
            # Giảm số lượng requests mỗi lần xuống để tránh vượt quota
            max_requests_per_batch = 50  # Quay về 50 thay vì 75
            batches = [requests[i:i + max_requests_per_batch] for i in range(0, len(requests), max_requests_per_batch)]

            for i, batch_requests in enumerate(batches):
                try:
                    self.log_to_file(f"Gửi cập nhật {i+1}/{len(batches)} ({len(batch_requests)} mục)...")

                    # Đảm bảo interval giữa các lần gọi API
                    self.wait_between_api_calls()

                    # Ghi dữ liệu theo lô
                    response = service.spreadsheets().batchUpdate(
                        spreadsheetId=spreadsheet_id,
                        body={"requests": batch_requests}
                    ).execute()

                    self.log_to_file(f"✅ Đã cập nhật nhóm {i+1}/{len(batches)}", "SUCCESS")

                    # Đảm bảo UI được cập nhật
                    QApplication.processEvents()

                    # Chờ giữa các batch để tránh vượt quá tần suất gọi API
                    time.sleep(1)

                except Exception as e:
                    self.log_to_file(f"Lỗi cập nhật dữ liệu nhóm {i+1}/{len(batches)}: {str(e)}", "ERROR")
                    self.log_to_file(f"Chi tiết: {traceback.format_exc()}", "ERROR")
                    # Ném lại ngoại lệ để phương thức retry có thể xử lý
                    raise

        except Exception as e:
            self.log_to_file(f"Lỗi trong quá trình chuẩn bị cập nhật dữ liệu: {str(e)}", "ERROR")
            self.log_to_file(f"Chi tiết: {traceback.format_exc()}", "ERROR")
            raise

    def update_pa_action(self, worksheet, end_row):
        try:
            service = self.manager.get_sheets_service()
            spreadsheet_id = worksheet.spreadsheet.id

            # Get the sheetId - use direct property
            sheet_id = worksheet.id

            self.update_log.emit(f"Chuẩn bị thêm đánh dấu PA tại dòng {end_row + 1}")

            # Xóa dòng "PA đã action đến đây" cũ
            old_pa_row = None
            # Lấy tất cả dữ liệu một lần để tìm dòng PA cũ
            all_values = worksheet.get_all_values()

            for i, row in enumerate(all_values):
                if any(cell and "PA đã action đến đây" in cell for cell in row):
                    old_pa_row = i
                    break

            # Đếm số dòng có dữ liệu thực sự (không tính dòng trống)
            data_rows = 0
            for i, row in enumerate(all_values):
                if any(cell.strip() for cell in row):
                    data_rows = i + 1

            # Cập nhật end_row để phản ánh dòng có dữ liệu cuối cùng
            if data_rows > 0:
                end_row = data_rows

            # Xử lý xóa dòng PA cũ trước
            if old_pa_row is not None:
                try:
                    request = {
                        "deleteDimension": {
                            "range": {
                                "sheetId": sheet_id,
                                "dimension": "ROWS",
                                "startIndex": old_pa_row,
                                "endIndex": old_pa_row + 1
                            }
                        }
                    }

                    # Thêm cơ chế retry cho việc xóa dòng cũ
                    max_retries = 3
                    retry_count = 0
                    while retry_count < max_retries:
                        try:
                            # Tạo service mới cho mỗi lần gọi để đảm bảo token được refresh
                            service = self.manager.get_sheets_service()

                            # KHÔNG sử dụng build_http vì nó không chứa credentials
                            # Thay vào đó, sử dụng service trực tiếp với timeout dài hơn
                            service.spreadsheets().batchUpdate(
                                spreadsheetId=spreadsheet_id,
                                body={"requests": [request]}
                            ).execute()  # Loại bỏ tham số timeout

                            self.update_log.emit(f"Đã xóa đánh dấu PA cũ tại dòng {old_pa_row + 1}")

                            # Nếu end_row lớn hơn old_pa_row, giảm end_row đi 1 vì đã xóa 1 dòng
                            if end_row > old_pa_row:
                                end_row -= 1

                            # Làm mới worksheet để có dữ liệu mới nhất sau khi xóa
                            # Thay vì refresh(), lấy lại worksheet từ spreadsheet
                            try:
                                # Lấy lại worksheet từ spreadsheet
                                worksheet = self.manager.open_by_key(spreadsheet_id).worksheet(worksheet.title)
                                self.update_log.emit(f"Đã làm mới dữ liệu worksheet sau khi xóa PA cũ")
                            except Exception as refresh_error:
                                self.update_log.emit(f"Lưu ý: Không thể làm mới worksheet: {str(refresh_error)}")
                            break
                        except TimeoutError as te:
                            retry_count += 1
                            self.update_log.emit(f"Timeout khi xóa PA cũ - thử lại lần {retry_count}/{max_retries}")
                            if retry_count >= max_retries:
                                self.update_log.emit(f"Không thể xóa đánh dấu PA cũ sau {max_retries} lần thử - tiếp tục với việc thêm mới")
                            else:
                                time.sleep(5)  # Đợi 5 giây trước khi thử lại
                        except Exception as e:
                            self.update_log.emit(f"Lỗi khi xóa đánh dấu PA cũ: {str(e)}")
                            break
                except Exception as e:
                    self.update_log.emit(f"Lỗi khi xóa đánh dấu PA cũ: {str(e)}")

            # Sau khi xóa dòng cũ, thêm dòng mới và cập nhật "PA đã action đến đây" với định dạng yêu cầu
            try:
                # 1. Thêm dòng mới (sử dụng appendRow thay vì insertDimension)
                worksheet.append_row([""] * 50)

                # 2-4. Định dạng dòng với batchUpdate
                requests = [
                    # Tô màu đỏ cho toàn bộ dòng
                    {
                        "updateCells": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": end_row,
                                "endRowIndex": end_row + 1,
                                "startColumnIndex": 0,
                                "endColumnIndex": 50  # Đủ rộng để bao phủ tất cả các cột
                            },
                            "rows": [{
                                "values": [{
                                    "userEnteredFormat": {
                                        "backgroundColor": {"red": 1, "green": 0, "blue": 0}
                                    }
                                } for _ in range(50)]
                            }],
                            "fields": "userEnteredFormat.backgroundColor"
                        }
                    },
                    # Merge ô từ cột E đến K (index 4-10)
                    {
                        "mergeCells": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": end_row,
                                "endRowIndex": end_row + 1,
                                "startColumnIndex": 4,
                                "endColumnIndex": 11  # Cột K là index 10, +1 để đến endColumnIndex
                            },
                            "mergeType": "MERGE_ALL"
                        }
                    },
                    # Ghi "PA đã action đến đây" vào khu vực merge và định dạng
                    {
                        "updateCells": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": end_row,
                                "endRowIndex": end_row + 1,
                                "startColumnIndex": 4,
                                "endColumnIndex": 11
                            },
                            "rows": [{
                                "values": [{
                                    "userEnteredValue": {"stringValue": "PA đã action đến đây, bổ sung thêm nhập bên dưới nhé BD"},
                                    "userEnteredFormat": {
                                        "backgroundColor": {"red": 1, "green": 1, "blue": 1},  # Nền trắng
                                        "textFormat": {
                                            "foregroundColor": {"red": 0.42, "green": 0.55, "blue": 0.95},  # Cornflower blue
                                            "bold": True,
                                            "fontFamily": "Times New Roman",
                                            "fontSize": 12
                                        },
                                        "horizontalAlignment": "CENTER",
                                        "verticalAlignment": "MIDDLE"
                                    }
                                }]
                            }],
                            "fields": "userEnteredValue,userEnteredFormat"
                        }
                    }
                ]

                # Thêm cơ chế retry cho việc thêm PA mới
                max_retries = 3
                retry_count = 0
                success = False

                while retry_count < max_retries and not success:
                    try:
                        # Tạo service mới cho mỗi lần gọi để đảm bảo token được refresh
                        service = self.manager.get_sheets_service()

                        # KHÔNG sử dụng build_http vì nó không chứa credentials
                        # Thay vào đó, sử dụng service trực tiếp với timeout dài hơn
                        service.spreadsheets().batchUpdate(
                            spreadsheetId=spreadsheet_id,
                            body={"requests": requests}
                        ).execute()  # Loại bỏ tham số timeout

                        self.update_log.emit(f"✅ Đã thêm đánh dấu PA tại dòng {end_row + 1}")
                        success = True
                    except TimeoutError as te:
                        retry_count += 1
                        self.update_log.emit(f"Timeout khi cập nhật PA - thử lại lần {retry_count}/{max_retries}")
                        if retry_count >= max_retries:
                            # Đã thử hết số lần retry nhưng vẫn thất bại
                            self.error.emit(f"Không thể cập nhật PA sau {max_retries} lần thử")
                            self.update_log.emit(f"Chi tiết lỗi: {str(te)}")
                        else:
                            time.sleep(5)  # Đợi 5 giây trước khi thử lại
                    except Exception as e:
                        self.error.emit(f"Lỗi khi tạo đánh dấu PA: {str(e)}")
                        import traceback
                        self.update_log.emit(f"Chi tiết lỗi: {traceback.format_exc()}")
                        break

                # Nếu đã thử hết các lần mà không thành công, nhưng chúng ta đã append dòng rỗng
                # Thông báo cho người dùng rằng dòng đã được thêm nhưng không định dạng
                if not success:
                    self.update_log.emit("Đã thêm dòng PA nhưng không thể áp dụng định dạng do timeout")
                    # Chỉ thông báo thành công khi dòng đã được thêm
                    if worksheet and end_row and end_row > 0:
                        self.update_log.emit("✅ Đã cập nhật 'PA đã action đến đây' thành công")
            except Exception as e:
                self.error.emit(f"Lỗi khi tạo đánh dấu PA: {str(e)}")
                import traceback
                self.update_log.emit(f"Chi tiết lỗi: {traceback.format_exc()}")
        except Exception as e:
            self.error.emit(f"Lỗi chung trong update_pa_action: {str(e)}")
            import traceback
            self.update_log.emit(f"Chi tiết lỗi: {traceback.format_exc()}")
            # Chỉ thông báo thành công khi dòng đã được thêm
            if worksheet and end_row and end_row > 0:
                self.update_log.emit("✅ Đã cập nhật 'PA đã action đến đây' thành công")

# Lớp giao diện chính
class ExternalUpdateWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Data Assortment All in One")

        # Khởi tạo GoogleSheetManager với chuỗi Base64 và thử cả hai OAuth credentials
        self.manager = GoogleSheetManager(
            auth_type='oauth',
            credentials_data=CREDENTIALS_BASE64,
            all_oauth_credentials=ALTERNATIVE_CREDENTIALS
        )

        # Ánh xạ cột
        self.source_column_mapping = SOURCE_COLUMN_MAPPING.copy()
        self.target_column_mapping = TARGET_COLUMN_MAPPING.copy()

        # Cấu hình điều kiện xử lý
        self.process_conditions = PROCESS_CONDITIONS.copy()
        self.column_groups = COLUMN_GROUPS.copy()

        # Load cấu hình từ file nếu có
        self.load_config_from_file()

        # Biến để theo dõi processor hiện tại
        self.processor = None
        self.process_timer = QTimer()
        self.process_timer.timeout.connect(self.check_processor_status)

        # Layout chính
        main_layout = QVBoxLayout(self)

        # Top Bar: Back Button và Context Menu
        top_bar = QHBoxLayout()
        btn_back = QPushButton("⬅️ Trở về giao diện chính")
        btn_back.setFixedSize(150, 40)
        btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(btn_back, alignment=Qt.AlignmentFlag.AlignLeft)

        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Data Handler", lambda: self.goto_other_program("data_handler"))
        menu.addAction("Internal Data", lambda: self.goto_other_program("internal_data"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        menu.addAction("Basket Arrangement", lambda: self.goto_other_program("basket_arrangement"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)

        main_layout.addLayout(top_bar)
        main_layout.addSpacing(10)

        # Layout ngang cho 2 group box
        sheets_layout = QHBoxLayout()
        sheets_layout.setSpacing(20)  # Khoảng cách giữa 2 group box

        # Sheet nguồn (Internal)
        source_group = QGroupBox("Internal Data")
        source_group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        source_layout = QVBoxLayout()
        self.source_link = QLineEdit()
        self.source_link.textChanged.connect(self.auto_parse_source_url)
        self.source_sheet_combo = QComboBox()
        self.start_row_input = QLineEdit()
        self.load_source_button = QPushButton("Load")
        source_layout.addWidget(QLabel("Internal Sheet URL:"))
        source_layout.addWidget(self.source_link)
        source_layout.addWidget(QLabel("Worksheet:"))
        source_layout.addWidget(self.source_sheet_combo)
        source_layout.addWidget(QLabel("Dòng dữ liệu bắt đầu:"))
        source_layout.addWidget(self.start_row_input)
        source_layout.addWidget(self.load_source_button)
        source_group.setLayout(source_layout)

        # Sheet đích (External)
        target_group = QGroupBox("External Data")
        target_group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        target_layout = QVBoxLayout()
        self.target_link = QLineEdit()
        self.target_link.textChanged.connect(self.auto_parse_target_url)
        self.target_sheet_combo = QComboBox()
        self.header_row_input = QLineEdit()
        self.load_target_button = QPushButton("Load")
        target_layout.addWidget(QLabel("External Sheet URL:"))
        target_layout.addWidget(self.target_link)
        target_layout.addWidget(QLabel("Worksheet:"))
        target_layout.addWidget(self.target_sheet_combo)
        target_layout.addWidget(QLabel("Dòng chứa header:"))
        target_layout.addWidget(self.header_row_input)
        target_layout.addWidget(self.load_target_button)
        target_group.setLayout(target_layout)

        # Thêm các group box vào layout ngang
        sheets_layout.addWidget(source_group)
        sheets_layout.addWidget(target_group)

        # Thêm layout ngang vào layout chính
        main_layout.addLayout(sheets_layout)

        # Progress Bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

        # Log Area trong group box
        log_group = QGroupBox("Processing Log")
        log_layout = QVBoxLayout()
        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        log_layout.addWidget(self.log_area)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # Nút chức năng - di chuyển xuống dưới log
        button_layout = QHBoxLayout()
        button_layout.setSpacing(1)  # Khoảng cách giữa các nút

        # Container cho nút Map Cột
        map_container = QWidget()
        map_layout = QVBoxLayout(map_container)
        map_layout.setContentsMargins(0, 0, 0, 0)
        map_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.map_columns_button = QPushButton("Map Cột")
        self.map_columns_button.setFixedSize(150, 50)  # Kích thước cố định cho nút
        self.map_columns_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        map_layout.addWidget(self.map_columns_button)
        button_layout.addWidget(map_container)

        # Container cho nút quản lý điều kiện xử lý
        condition_container = QWidget()
        condition_layout = QVBoxLayout(condition_container)
        condition_layout.setContentsMargins(0, 0, 0, 0)
        condition_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.manage_conditions_button = QPushButton("Quản lý điều kiện")
        self.manage_conditions_button.setFixedSize(150, 50)  # Kích thước cố định cho nút
        self.manage_conditions_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #EF6C00;
            }
        """)
        condition_layout.addWidget(self.manage_conditions_button)
        button_layout.addWidget(condition_container)

        # Container cho nút Xử lý dữ liệu
        process_container = QWidget()
        process_layout = QVBoxLayout(process_container)
        process_layout.setContentsMargins(0, 0, 0, 0)
        process_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.process_button = QPushButton("Xử lý dữ liệu")
        self.process_button.setFixedSize(150, 50)  # Kích thước cố định cho nút
        self.process_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)
        process_layout.addWidget(self.process_button)
        button_layout.addWidget(process_container)

        main_layout.addLayout(button_layout)

        # Kết nối sự kiện
        self.load_source_button.clicked.connect(self.load_source_sheet)
        self.load_target_button.clicked.connect(self.load_target_sheet)
        self.map_columns_button.clicked.connect(self.open_column_mapping)
        self.manage_conditions_button.clicked.connect(self.open_process_conditions)
        self.process_button.clicked.connect(self.process_data)

    def open_process_conditions(self):
        """Mở dialog quản lý điều kiện xử lý"""
        dialog = ProcessConditionDialog(self.process_conditions, self.column_groups, self)
        if dialog.exec():
            # Lưu thay đổi từ dialog
            self.process_conditions = dialog.get_process_conditions()
            # Cập nhật biến toàn cục PROCESS_CONDITIONS
            global PROCESS_CONDITIONS
            PROCESS_CONDITIONS = self.process_conditions.copy()

            # Lưu cấu hình vào file
            if save_config(self.process_conditions, self.column_groups):
                QMessageBox.information(self, "Thành công", "Đã cập nhật và lưu cấu hình điều kiện xử lý.")
            else:
                QMessageBox.warning(self, "Cảnh báo", "Đã cập nhật cấu hình nhưng không thể lưu vào file.\nThay đổi sẽ mất khi khởi động lại chương trình.")

    def load_config_from_file(self):
        """Load cấu hình từ file nếu có"""
        try:
            config = load_config()
            if config:
                # Kiểm tra xem cấu hình có hợp lệ không
                if 'process_conditions' in config and 'column_groups' in config:
                    # Cập nhật process_conditions từ file
                    loaded_process_conditions = config['process_conditions']

                    # Kiểm tra tính hợp lệ của process_conditions
                    valid_process_conditions = {}
                    for condition_name, condition_data in loaded_process_conditions.items():
                        # Kiểm tra cấu trúc dữ liệu
                        if isinstance(condition_data, dict) and 'column_groups' in condition_data and 'description' in condition_data:
                            # Chỉ chấp nhận các nhóm cột hợp lệ
                            valid_column_groups = []
                            for group in condition_data['column_groups']:
                                if group in self.column_groups:
                                    valid_column_groups.append(group)

                            # Thêm điều kiện nếu có ít nhất một nhóm cột hợp lệ
                            if valid_column_groups:
                                valid_process_conditions[condition_name] = {
                                    'column_groups': valid_column_groups,
                                    'description': condition_data['description']
                                }

                    # Cập nhật cấu hình nếu có dữ liệu hợp lệ
                    if valid_process_conditions:
                        self.process_conditions = valid_process_conditions
                        # Cập nhật biến toàn cục
                        global PROCESS_CONDITIONS
                        PROCESS_CONDITIONS = self.process_conditions.copy()

                        logging.info(f"Đã load {len(valid_process_conditions)} điều kiện xử lý từ file cấu hình")

                    # Có thể cập nhật column_groups nếu muốn, nhưng hiện tại giữ nguyên
                    # self.column_groups = config['column_groups']
        except Exception as e:
            logging.error(f"Lỗi khi load cấu hình: {str(e)}")

    def handle_back(self):
        if hasattr(self, 'back_callback'):
            self.back_callback()

    def on_module_activated(self):
        """Được gọi khi module được kích hoạt từ main.py - reset hoàn toàn UI"""
        self.reset_ui()

    def goto_other_program(self, program_name):
        if hasattr(self, 'goto_other_program_callback'):
            self.goto_other_program_callback(program_name)

    def eventFilter(self, obj, event):
        if obj == self.context_button and event.type() == QEvent.Type.Enter:
            self.context_button.showMenu()
        return super().eventFilter(obj, event)

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Backspace:
            self.handle_back()
        else:
            super().keyPressEvent(event)

    def reset_ui(self):
        """Reset the UI state when switching away from this view"""
        # Clear any processing state
        if hasattr(self, 'processor') and self.processor and isinstance(self.processor, QThread):
            if self.processor.isRunning():
                self.processor.quit()
                self.processor.wait()

        # Clear the log
        self.log_area.clear()

        # Re-enable the process button if it was disabled
        self.process_button.setEnabled(True)

    def auto_parse_source_url(self, text):
        """Tự động parse URL nguồn khi người dùng nhập/dán"""
        if not text or (text == self.source_link.text() and '/' not in text and '.' not in text):
            return

        if '/d/' in text or 'spreadsheets/d/' in text or 'key=' in text:
            # Chỉ parse khi có định dạng URL Google Sheets
            spreadsheet_id = self.extract_spreadsheet_id(text)
            if spreadsheet_id != text:  # Chỉ cập nhật nếu ID khác với text đã nhập
                self.source_link.blockSignals(True)  # Ngăn việc gọi lại hàm này khi setText
                self.source_link.setText(spreadsheet_id)
                self.source_link.blockSignals(False)

    def auto_parse_target_url(self, text):
        """Tự động parse URL đích khi người dùng nhập/dán"""
        if not text or (text == self.target_link.text() and '/' not in text and '.' not in text):
            return

        if '/d/' in text or 'spreadsheets/d/' in text or 'key=' in text:
            # Chỉ parse khi có định dạng URL Google Sheets
            spreadsheet_id = self.extract_spreadsheet_id(text)
            if spreadsheet_id != text:  # Chỉ cập nhật nếu ID khác với text đã nhập
                self.target_link.blockSignals(True)  # Ngăn việc gọi lại hàm này khi setText
                self.target_link.setText(spreadsheet_id)
                self.target_link.blockSignals(False)

    def extract_spreadsheet_id(self, link):
        """Trích xuất Spreadsheet ID từ URL Google Sheet"""
        # Xử lý các dạng URL khác nhau
        if not link:
            return ""

        # Nếu link đã là spreadsheet ID (không chứa / hoặc .)
        if '/' not in link and '.' not in link and len(link) >= 25:
            return link

        # Trích xuất từ full URL
        if '/d/' in link:
            # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
            id_part = link.split('/d/')[1]
            return id_part.split('/')[0].split('?')[0].split('#')[0]
        elif 'spreadsheets/d/' in link:
            # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
            id_part = link.split('spreadsheets/d/')[1]
            return id_part.split('/')[0].split('?')[0].split('#')[0]
        elif 'key=' in link:
            # Format: https://docs.google.com/spreadsheets/d/key=SPREADSHEET_ID
            id_part = link.split('key=')[1]
            return id_part.split('&')[0].split('#')[0]

        # Trả về chính link nếu không phù hợp với bất kỳ định dạng nào
        return link

    def load_source_sheet(self):
        link = self.source_link.text()
        try:
            spreadsheet_id = self.extract_spreadsheet_id(link)

            spreadsheet = self.manager.open_by_key(spreadsheet_id)
            sheets = [sheet.title for sheet in spreadsheet.worksheets()]
            self.source_sheet_combo.clear()
            self.source_sheet_combo.addItems(sheets)

            # Tự động chọn sheet phù hợp nếu có
            sheet_found = False
            for sheet in sheets:
                # So sánh không phân biệt hoa thường
                if "update level model" in sheet.lower():
                    self.source_sheet_combo.setCurrentText(sheet)
                    sheet_found = True
                    break
                elif "update model level" in sheet.lower():
                    self.source_sheet_combo.setCurrentText(sheet)
                    sheet_found = True
                    break

            if not sheet_found and sheets:
                # Nếu không tìm thấy sheet phù hợp, chọn sheet đầu tiên
                self.source_sheet_combo.setCurrentText(sheets[0])

            worksheet = spreadsheet.worksheet(self.source_sheet_combo.currentText())
            data = worksheet.get_all_values()
            start_row = self.find_start_row(data)
            self.start_row_input.setText(str(start_row))
        except Exception as e:
            QMessageBox.critical(self, "Error", str(e))

    def load_target_sheet(self):
        link = self.target_link.text()
        try:
            spreadsheet_id = self.extract_spreadsheet_id(link)

            spreadsheet = self.manager.open_by_key(spreadsheet_id)
            sheets = [sheet.title for sheet in spreadsheet.worksheets()]
            self.target_sheet_combo.clear()
            self.target_sheet_combo.addItems(sheets)

            # Tự động chọn sheet có tên liên quan đến "Deal list"
            deal_list_sheet = None
            for sheet in sheets:
                # Ưu tiên theo thứ tự: "Final Deal list", "Deal list", "Pool Deal"
                # Chuyển về chữ thường để so sánh
                sheet_lower = sheet.lower()
                if "final deal list" in sheet_lower:
                    deal_list_sheet = sheet
                    break
                elif "deal list" in sheet_lower:
                    deal_list_sheet = sheet
                elif not deal_list_sheet and "pool deal" in sheet_lower:
                    deal_list_sheet = sheet

            if deal_list_sheet:
                self.target_sheet_combo.setCurrentText(deal_list_sheet)

            self.header_row_input.setText("3")  # Header mặc định ở dòng 3
        except Exception as e:
            QMessageBox.critical(self, "Error", str(e))

    def find_start_row(self, data):
        for i, row in enumerate(data):
            if any(cell and "PA đã action đến đây" in cell for cell in row):
                return i + 2  # Dòng sau "PA đã action đến đây"
        return 3  # Dòng 3 mặc định (index bắt đầu từ 1 trong UI)

    def open_column_mapping(self):
        dialog = ColumnMappingDialog(self.source_column_mapping, self.target_column_mapping, self)
        if dialog.exec():
            self.source_column_mapping, self.target_column_mapping = dialog.get_mappings()
            QMessageBox.information(self, "Info", "Danh sách cột đã được cập nhật.")

    def process_data(self):
        # Kiểm tra xem đã có một tiến trình đang chạy chưa
        if self.processor and self.processor.isRunning():
            QMessageBox.warning(self, "Tiến trình đang chạy", "Vui lòng đợi tiến trình hiện tại hoàn thành")
            return

        # Kiểm tra đã có đủ thông tin cần thiết
        if not self.source_sheet_combo.currentText() or not self.target_sheet_combo.currentText():
            QMessageBox.warning(self, "Thiếu thông tin", "Vui lòng load và chọn cả worksheet nguồn và đích")
            return

        try:
            source_link = self.extract_spreadsheet_id(self.source_link.text())
            target_link = self.extract_spreadsheet_id(self.target_link.text())
            source_sheet = self.source_sheet_combo.currentText()
            target_sheet = self.target_sheet_combo.currentText()

            try:
                start_row = int(self.start_row_input.text()) if self.start_row_input.text() else 3
                header_row = int(self.header_row_input.text()) if self.header_row_input.text() else 1
            except ValueError:
                QMessageBox.warning(self, "Lỗi định dạng", "Vui lòng nhập số hợp lệ cho Start row và Header row")
                return

            # Kiểm tra xem có đầy đủ ánh xạ cột hay không
            if not self.source_column_mapping or not self.target_column_mapping:
                # Nếu chưa map, hiển thị dialog ánh xạ cột
                self.open_column_mapping()
                return  # Chờ người dùng map cột và nhấn Process lại

            # Tạo và hiển thị dialog tiến trình trước khi bắt đầu
            self.progress_dialog = ProgressDialog(self)
            self.progress_dialog.show()

            # Tạo đối tượng processor
            self.processor = DataProcessor(
                source_link=source_link,
                target_link=target_link,
                source_sheet=source_sheet,
                target_sheet=target_sheet,
                start_row=start_row,
                header_row=header_row,
                source_mapping=self.source_column_mapping,
                target_mapping=self.target_column_mapping,
                manager=self.manager,
                process_conditions=self.process_conditions
            )

            # Kết nối các signals
            self.processor.update_progress.connect(self.update_progress)
            self.processor.update_log.connect(self.update_log)
            self.processor.finished.connect(self.on_finished)
            self.processor.error.connect(self.on_error)

            # Kết nối signal mới vào progress dialog
            self.processor.update_processing_detail.connect(self.handle_processing_detail)

            # Cập nhật UI
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.log_area.clear()
            self.progress_dialog.clear_log() # Xóa log cũ trong dialog
            self.update_progress_text("Đang khởi tạo...")

            # Bắt đầu xử lý
            self.processor.start()

            # Bắt đầu timer để kiểm tra trạng thái
            self.process_timer.start(1000)  # Kiểm tra mỗi giây

            # Vô hiệu hóa các nút trong quá trình xử lý
            self.process_button.setEnabled(False)
            self.load_source_button.setEnabled(False)
            self.load_target_button.setEnabled(False)
            self.map_columns_button.setEnabled(False)
            self.manage_conditions_button.setEnabled(False)

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Đã xảy ra lỗi khi khởi tạo tiến trình: {str(e)}")

    # Các hàm xử lý cập nhật và hoàn thành

    def check_processor_status(self):
        """Kiểm tra và cập nhật trạng thái của processor"""
        if self.processor and not self.processor.isRunning():
            self.process_timer.stop()

            # Kích hoạt lại các nút
            self.process_button.setEnabled(True)
            self.load_source_button.setEnabled(True)
            self.load_target_button.setEnabled(True)
            self.map_columns_button.setEnabled(True)
            self.manage_conditions_button.setEnabled(True)

    def update_progress_text(self, text):
        """Thêm text vào log area"""
        self.log_area.append(text)
        # Cuộn xuống dưới cùng
        cursor = self.log_area.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_area.setTextCursor(cursor)

    def on_finished(self):
        """Xử lý khi tiến trình hoàn thành"""
        self.progress_bar.setValue(100)
        # Kiểm tra xem thông báo đã xuất hiện chưa để tránh hiển thị hai lần
        if not self.log_area.toPlainText().endswith("===== Xử lý dữ liệu hoàn tất ====="):
            self.update_progress_text("===== Xử lý dữ liệu hoàn tất =====")

        # Đóng progress dialog trước khi hiển thị thông báo hoàn thành
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        QMessageBox.information(self, "Hoàn thành", "Quá trình cập nhật dữ liệu đã hoàn tất!")

    def on_error(self, message):
        """Xử lý khi tiến trình gặp lỗi"""
        # Đóng progress dialog trước khi xử lý lỗi
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        # Kiểm tra xem thông báo lỗi có chứa lỗi timeout khi xử lý PA không
        if "TimeoutError" in message and "PA" in message:
            # Kiểm tra xem log area có chứa thông báo đã cập nhật PA thành công không
            log_text = self.log_area.toPlainText()
            if "Đã cập nhật 'PA đã action đến đây' thành công" in log_text:
                # Đã thấy thông báo thành công, không hiển thị dialog lỗi
                return

        # Hiển thị thông báo lỗi nếu không phải lỗi timeout khi cập nhật PA
        # hoặc nếu không tìm thấy thông báo cập nhật thành công
        QMessageBox.critical(self, "Lỗi", f"Đã xảy ra lỗi trong quá trình xử lý: {message}")

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def update_log(self, message):
        self.update_progress_text(message)

    def closeEvent(self, event):
        """Xử lý khi cửa sổ đóng"""
        try:
            # Đóng progress dialog nếu đang mở
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.close()
                self.progress_dialog = None

            self.log_area.clear()  # Xóa log area trước để giảm tải

            if self.processor and self.processor.isRunning():
                self.update_log("Đang dừng tiến trình xử lý...")

                # Đánh dấu dừng
                self.processor.abort()

                # Ngắt kết nối tất cả tín hiệu
                try:
                    self.processor.update_progress.disconnect()
                    self.processor.update_log.disconnect()
                    self.processor.finished.disconnect()
                    self.processor.error.disconnect()
                except Exception:
                    pass  # Bỏ qua lỗi nếu có khi ngắt kết nối

                # Chờ thread kết thúc
                if not self.processor.wait(3000):  # Chờ tối đa 3 giây
                    self.processor.terminate()
                    self.processor.wait()

                # Xóa tham chiếu đến processor
                self.processor = None

            # Đảm bảo tất cả các timer được dừng
            if hasattr(self, 'process_timer') and self.process_timer.isActive():
                self.process_timer.stop()

            # Giải phóng bộ nhớ
            logging.shutdown()

            # Xóa các widget con
            self.log_area = None

            # Thu gom rác
            import gc
            gc.collect()

            event.accept()
        except Exception as e:
            print(f"Lỗi khi đóng: {str(e)}")
            event.accept()

    # Thêm phương thức xử lý signal mới
    def handle_processing_detail(self, message, level):
        """Xử lý thông tin chi tiết từ processor và hiển thị trong dialog"""
        if not self.progress_dialog:
            return

        if level == "ERROR":
            self.progress_dialog.update_error(message)
        elif level == "WARNING":
            # Hiển thị cảnh báo với màu vàng
            self.progress_dialog.log_area.setTextColor(QColor(255, 165, 0))  # Orange color
            self.progress_dialog.log_area.append(message)
            self.progress_dialog.log_area.setTextColor(QColor(0, 0, 0))  # Reset to black
        elif level == "SUCCESS":
            self.progress_dialog.update_success(message)
        else:
            # INFO và các level khác
            self.progress_dialog.update_processing_detail(message)

class ProgressDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Processing...")
        self.setModal(True)
        self.setFixedSize(650, 350)  # Tăng kích thước dialog
        layout = QVBoxLayout()

        self.info_label = QLabel("Starting process...")
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.info_label.setStyleSheet("font-weight: bold;")
        layout.addWidget(self.info_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 0)
        layout.addWidget(self.progress_bar)

        # Thêm log area để hiển thị thông tin chi tiết
        label = QLabel("Chi tiết xử lý:")
        layout.addWidget(label)

        self.log_area = QTextEdit()
        self.log_area.setReadOnly(True)
        self.log_area.setFixedHeight(200)  # Đủ cao để hiển thị nhiều dòng
        layout.addWidget(self.log_area)

        self.setLayout(layout)

    def update_info(self, message):
        self.info_label.setText(message)
        QApplication.processEvents()

    def update_error(self, error_message):
        # Hiển thị lỗi với màu đỏ
        self.log_area.setTextColor(QColor(255, 0, 0))
        self.log_area.append(f"❌ LỖI: {error_message}")
        self.log_area.setTextColor(QColor(0, 0, 0))
        self.log_area.ensureCursorVisible()
        QApplication.processEvents()

    def update_success(self, message):
        # Hiển thị thông báo thành công với màu xanh
        self.log_area.setTextColor(QColor(0, 128, 0))
        self.log_area.append(f"✅ {message}")
        self.log_area.setTextColor(QColor(0, 0, 0))
        self.log_area.ensureCursorVisible()
        QApplication.processEvents()

    def update_batch_info(self, batch_info):
        # Hiển thị thông tin batch đang xử lý
        self.update_info(f"Đang xử lý batch {batch_info.get('batch_index', '?')}/{batch_info.get('total_batches', '?')} - {batch_info.get('rows', 0)} dòng")
        # Thêm thông tin chi tiết vào log_area
        if "details" in batch_info:
            self.log_area.append(batch_info["details"])
        QApplication.processEvents()

    def update_processing_detail(self, detail):
        # Hiển thị chi tiết xử lý
        self.log_area.append(detail)
        self.log_area.ensureCursorVisible()
        QApplication.processEvents()

    def clear_log(self):
        self.log_area.clear()
        QApplication.processEvents()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ExternalUpdateWidget()
    window.show()

    try:
        exit_code = app.exec()
        # Dọn dẹp sau khi thoát vòng lặp sự kiện
        logging.shutdown()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Lỗi khi thoát: {str(e)}")
        sys.exit(1)