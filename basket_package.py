import sys
import random
import re
import time
import json
import os
from datetime import datetime, timed<PERSON>ta
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QComboBox, QLineEdit, QMessageBox, QScrollArea, QFrame, QTextEdit,
    QSpinBox, QGroupBox, QSizePolicy, QCheckBox, QColorDialog, QToolButton, QProxyStyle, QStyle,
    QListView, QListWidget, QListWidgetItem, QMenu, QStyledItemDelegate, QAbstractItemView, QDialog, QFormLayout,
    QTableWidget, QTableWidgetItem, QFileDialog, QInputDialog
)
from PyQt6.QtCore import Qt, QPoint, QEvent, QObject, pyqtSignal, QRegularExpression, QTimer
from PyQt6.QtGui import QClipboard, QColor, QIcon, QMouseEvent, QRegularExpressionValidator
from gsheet_manager import GoogleSheetManager

"""
THÔNG TIN THUẬT TOÁN SẮP XẾP MỚI (v2.0)
=====================================
Thuật toán sắp xếp ID có quy tắc ưu tiên như sau:

1. THỨ TỰ NO LÀ "BẤT KHẢ XÂM PHẠM"
   - ID có NO 1 luôn được xếp đầu tiên
   - ID có NO 2 luôn xếp sau NO 1
   - ID có NO 3 luôn xếp sau NO 2
   ... và tiếp tục theo thứ tự

2. QUY TẮC ƯU TIÊN:
   - Trong cùng một NO, ID có review được xếp trước ID không có review
   - Trong cùng một NO và cùng trạng thái review, sắp xếp theo GMV giảm dần

3. ĐẢM BẢO NO VÀ REVIEW:
   - Không bao giờ phá vỡ thứ tự NO dù có bất kỳ input nào
   - ID input có thể chen vào nhưng không được phép phá vỡ thứ tự NO
   - ID exclusive được giữ nguyên vị trí

4. QUY TẮC XỬ LÝ XÓA ID DƯ THỪA:
   - Chỉ xóa những ID không thuộc timeline đang xử lý
   - Ưu tiên xóa những ID có GMV thấp nhất

5. MÃ MÀU:
   - ✓ : Thành công - ID được sắp xếp đúng thứ tự theo NO
   - ⚠️ : Cảnh báo - Phát hiện vấn đề trong sắp xếp ID theo NO
"""

# Thêm các hằng số mới
MAX_UNIQUE_IDS = 500  # Số lượng ID tối đa cho mỗi cột

# Các hằng số cho Basket sheet
DEFAULT_SHEET_NAME = 'Basket'
NUM_ID_COLUMNS = 12    # Số lượng cột ID
COLUMN_STEP = 5        # Khoảng cách giữa các cột ID
TIME_SLOT_ROW = 2      # Khung giờ (14:00-15:00) nằm ở dòng 2
HOUR_NAME_ROW = 2      # Tên giờ (Giờ 3) cũng nằm ở dòng 2
HEADER_ROW = 3         # Header (Shop ID, Item ID, STT) nằm ở dòng 3
DATA_START_ROW = 4     # Dữ liệu bắt đầu từ dòng 4

# Các hằng số cho Deal list sheet
DEAL_LIST_HEADER_ROW = 2      # Header nằm ở dòng 2 (index 1)
DEAL_LIST_DATA_START_ROW = 4  # Dữ liệu bắt đầu từ dòng 4 (index 3)

# Các hằng số cho Smart Random Placement
MIN_DISTANCE_BETWEEN_INPUT_IDS = 2  # Khoảng cách tối thiểu giữa các ID input
PLACEMENT_PRIORITY_WEIGHTS = {
    'EMPTY_SLOT': 0.7,      # 70% ưu tiên vị trí trống
    'BOUNDARY': 0.2,        # 20% ưu tiên vị trí boundary
    'GROUP_EDGE': 0.08,     # 8% ưu tiên vị trí đầu/cuối nhóm
    'GROUP_MIDDLE': 0.02    # 2% ưu tiên vị trí giữa nhóm (cuối cùng)
}

# BASE64 OAuth2 credentials
BASE64_OAUTH = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Column helpers
def col_to_index(col):
    result = 0
    for c in col:
        result = result * 26 + (ord(c.upper()) - ord('A') + 1)
    return result - 1

def index_to_col(index):
    col = ''
    while index >= 0:
        col = chr(index % 26 + ord('A')) + col
        index = index // 26 - 1
    return col

# Thêm hàm chuẩn hóa timeline ngay sau các hàm col_to_index và index_to_col
def normalize_timeline(timeline):
    """Chuẩn hóa timeline thành dạng không có dấu đặc biệt và khoảng trắng
    Ví dụ: '12:00-13:00' -> '********'
    """
    if not timeline:
        return ""
    # Loại bỏ dấu :, dấu - và khoảng trắng
    return timeline.replace(":", "").replace("-", "").replace(" ", "").lower()


class SettingsManager:
    """
    Quản lý việc lưu/tải cài đặt khung giờ, điều kiện và ID input.
    File được lưu trong APPDATA/LOCAL/Data All in One/Basket và tự động xóa sau 7 ngày.
    """

    def __init__(self):
        # Tạo đường dẫn thư mục lưu trữ
        self.settings_dir = os.path.join(
            os.environ.get('LOCALAPPDATA', os.path.expanduser('~')),
            'Data All in One',
            'Basket'
        )

        # Tạo thư mục nếu chưa tồn tại
        os.makedirs(self.settings_dir, exist_ok=True)

        # Tự động xóa file cũ hơn 7 ngày
        self._cleanup_old_files()

    def _cleanup_old_files(self):
        """Xóa các file cài đặt cũ hơn 7 ngày"""
        try:
            cutoff_date = datetime.now() - timedelta(days=7)

            for filename in os.listdir(self.settings_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(self.settings_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))

                    if file_time < cutoff_date:
                        os.remove(file_path)
                        print(f"Đã xóa file cài đặt cũ: {filename}")
        except Exception as e:
            print(f"Lỗi khi xóa file cũ: {e}")

    def save_settings(self, filename, time_slots, conditions_data):
        """
        Lưu cài đặt vào file JSON

        Args:
            filename: Tên file (không cần .json)
            time_slots: List các khung giờ
            conditions_data: List các điều kiện từ condition rows
        """
        try:
            # Đảm bảo filename có đuôi .json
            if not filename.endswith('.json'):
                filename += '.json'

            file_path = os.path.join(self.settings_dir, filename)

            # Tạo dữ liệu JSON
            settings_data = {
                "version": "1.0",
                "timestamp": datetime.now().isoformat(),
                "time_slots": time_slots,
                "conditions": conditions_data
            }

            # Lưu file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=2)

            return True, f"Đã lưu cài đặt vào: {file_path}"

        except Exception as e:
            return False, f"Lỗi khi lưu cài đặt: {e}"

    def load_settings(self, filename):
        """
        Tải cài đặt từ file JSON (hỗ trợ cả định dạng cũ và mới)

        Args:
            filename: Tên file (có thể có hoặc không có .json)

        Returns:
            tuple: (success, data_or_error_message)
        """
        try:
            # Đảm bảo filename có đuôi .json
            if not filename.endswith('.json'):
                filename += '.json'

            file_path = os.path.join(self.settings_dir, filename)

            if not os.path.exists(file_path):
                return False, f"File không tồn tại: {filename}"

            # Đọc file
            with open(file_path, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)

            # Chuyển đổi định dạng cũ sang định dạng mới nếu cần
            settings_data = self._convert_old_format_to_new(raw_data)

            # Kiểm tra cấu trúc dữ liệu sau khi chuyển đổi
            required_keys = ["version", "timestamp", "time_slots", "conditions"]
            for key in required_keys:
                if key not in settings_data:
                    return False, f"File cài đặt không hợp lệ: thiếu key '{key}'"

            return True, settings_data

        except json.JSONDecodeError:
            return False, f"File cài đặt bị lỗi định dạng JSON: {filename}"
        except Exception as e:
            return False, f"Lỗi khi tải cài đặt: {e}"

    def _convert_old_format_to_new(self, raw_data):
        """
        Chuyển đổi định dạng file cũ sang định dạng mới

        Định dạng cũ:
        {
          "metadata": {"name": "...", "created_at": "...", "version": "..."},
          "conditions": [{"top_text": "...", "ids": [...], "is_group": bool, "time_slots": [...]}]
        }

        Định dạng mới:
        {
          "version": "1.0",
          "timestamp": "...",
          "time_slots": [...],
          "conditions": [{"top_text": "...", "ids": [...], "is_group": bool, "selected_times": [...]}]
        }
        """
        # Kiểm tra xem đây có phải định dạng cũ không
        if "metadata" in raw_data and "conditions" in raw_data:
            print("Phát hiện định dạng file cũ, đang chuyển đổi...")

            # Thu thập tất cả time_slots từ các conditions
            all_time_slots = set()
            conditions = raw_data.get("conditions", [])

            for condition in conditions:
                time_slots = condition.get("time_slots", [])
                all_time_slots.update(time_slots)

            # Chuyển đổi sang định dạng mới
            new_data = {
                "version": raw_data.get("metadata", {}).get("version", "1.0"),
                "timestamp": raw_data.get("metadata", {}).get("created_at", datetime.now().isoformat()),
                "time_slots": sorted(list(all_time_slots)),  # Sắp xếp theo thứ tự
                "conditions": []
            }

            # Chuyển đổi từng condition
            for condition in conditions:
                new_condition = {
                    "top_text": condition.get("top_text", "Top 10"),
                    "ids": condition.get("ids", []),
                    "selected_times": condition.get("time_slots", []),  # time_slots -> selected_times
                    "is_group": condition.get("is_group", False)
                }
                new_data["conditions"].append(new_condition)

            print(f"Đã chuyển đổi {len(new_data['conditions'])} điều kiện từ định dạng cũ")
            print(f"Tìm thấy {len(new_data['time_slots'])} khung giờ: {new_data['time_slots']}")

            return new_data

        # Nếu đã là định dạng mới, trả về nguyên bản
        elif "version" in raw_data and "timestamp" in raw_data:
            return raw_data

        # Nếu không phải định dạng nào, thử tạo định dạng mới từ dữ liệu có sẵn
        else:
            print("Định dạng file không xác định, thử tạo định dạng mới...")

            conditions = raw_data.get("conditions", [])
            all_time_slots = set()

            # Thu thập time_slots từ conditions
            for condition in conditions:
                if "time_slots" in condition:
                    all_time_slots.update(condition["time_slots"])
                elif "selected_times" in condition:
                    all_time_slots.update(condition["selected_times"])

            return {
                "version": "1.0",
                "timestamp": datetime.now().isoformat(),
                "time_slots": sorted(list(all_time_slots)),
                "conditions": conditions
            }

    def get_settings_files(self):
        """
        Lấy danh sách các file cài đặt có sẵn

        Returns:
            list: Danh sách tên file (không có đuôi .json)
        """
        try:
            files = []
            for filename in os.listdir(self.settings_dir):
                if filename.endswith('.json'):
                    files.append(filename[:-5])  # Bỏ đuôi .json

            # Sắp xếp theo thời gian sửa đổi (mới nhất trước)
            files.sort(key=lambda f: os.path.getmtime(
                os.path.join(self.settings_dir, f + '.json')
            ), reverse=True)

            return files
        except Exception as e:
            print(f"Lỗi khi lấy danh sách file: {e}")
            return []

    def delete_settings(self, filename):
        """
        Xóa file cài đặt

        Args:
            filename: Tên file (có thể có hoặc không có .json)
        """
        try:
            if not filename.endswith('.json'):
                filename += '.json'

            file_path = os.path.join(self.settings_dir, filename)

            if os.path.exists(file_path):
                os.remove(file_path)
                return True, f"Đã xóa file: {filename}"
            else:
                return False, f"File không tồn tại: {filename}"

        except Exception as e:
            return False, f"Lỗi khi xóa file: {e}"


class SmartPlacementEngine:
    """
    Engine xử lý placement thông minh cho ID input.
    Tôn trọng shop grouping và phân tán ngẫu nhiên một cách thông minh.
    """

    def __init__(self, deal_list_manager, log_function=None):
        self.deal_list_manager = deal_list_manager
        self.log = log_function or print

    def analyze_current_structure(self, current_ids):
        """
        Phân tích cấu trúc hiện tại của danh sách ID để xác định:
        - Vị trí trống
        - Nhóm shop liên tục
        - Vị trí boundary giữa các shop
        """
        structure = {
            'empty_positions': [],
            'shop_groups': {},  # {shop_id: [positions]}
            'boundary_positions': [],
            'group_edge_positions': [],
            'group_middle_positions': []
        }

        # Phân tích từng vị trí
        for pos, id_val in enumerate(current_ids):
            if not id_val or id_val.strip() == "":
                structure['empty_positions'].append(pos)
            else:
                shop_id = self.deal_list_manager.id_to_shop.get(id_val, "unknown")
                if shop_id not in structure['shop_groups']:
                    structure['shop_groups'][shop_id] = []
                structure['shop_groups'][shop_id].append(pos)

        # Tìm các nhóm shop liên tục
        continuous_groups = {}
        for shop_id, positions in structure['shop_groups'].items():
            if len(positions) <= 1:
                continue

            positions.sort()
            continuous_groups[shop_id] = []
            current_group = [positions[0]]

            for i in range(1, len(positions)):
                if positions[i] == positions[i-1] + 1:
                    current_group.append(positions[i])
                else:
                    if len(current_group) > 1:
                        continuous_groups[shop_id].append(current_group)
                    current_group = [positions[i]]

            if len(current_group) > 1:
                continuous_groups[shop_id].append(current_group)

        # Phân tích nhóm NO
        no_groups = self._analyze_no_groups(current_ids)

        # Tìm ranh giới tự nhiên
        natural_boundaries = self._find_natural_boundaries(current_ids, structure['shop_groups'], no_groups)
        structure['natural_boundaries'] = natural_boundaries

        # Xác định boundary positions (giữa 2 shop khác nhau) - logic cũ
        for pos in range(len(current_ids) - 1):
            current_id = current_ids[pos]
            next_id = current_ids[pos + 1]

            if current_id and next_id:
                current_shop = self.deal_list_manager.id_to_shop.get(current_id, "")
                next_shop = self.deal_list_manager.id_to_shop.get(next_id, "")

                if current_shop != next_shop and current_shop and next_shop:
                    structure['boundary_positions'].append(pos + 1)

        # Xác định group edge và middle positions
        for shop_id, groups in continuous_groups.items():
            for group in groups:
                if len(group) >= 2:
                    # Đầu và cuối nhóm
                    structure['group_edge_positions'].extend([group[0], group[-1]])
                    # Giữa nhóm (nếu có >= 3 phần tử)
                    if len(group) >= 3:
                        structure['group_middle_positions'].extend(group[1:-1])

        return structure, continuous_groups

    def _analyze_no_groups(self, current_ids):
        """Phân tích nhóm NO trong danh sách"""
        no_groups = {}
        for pos, id_val in enumerate(current_ids):
            if id_val and id_val.strip():
                no_value = self.deal_list_manager.id_to_no.get(id_val, 999999)
                if no_value not in no_groups:
                    no_groups[no_value] = []
                no_groups[no_value].append(pos)
        return no_groups

    def _find_natural_boundaries(self, current_ids, shop_groups, no_groups):
        """
        Tìm ranh giới tự nhiên giữa các nhóm để đặt Input IDs
        Ưu tiên vị trí không phá vỡ nhóm Shop hoặc nhóm NO
        """
        boundaries = []

        # Tìm ranh giới giữa các shop khác nhau
        for pos in range(len(current_ids) - 1):
            current_id = current_ids[pos]
            next_id = current_ids[pos + 1]

            if current_id and next_id:
                current_shop = self.deal_list_manager.id_to_shop.get(current_id, "")
                next_shop = self.deal_list_manager.id_to_shop.get(next_id, "")
                current_no = self.deal_list_manager.id_to_no.get(current_id, 999999)
                next_no = self.deal_list_manager.id_to_no.get(next_id, 999999)

                # Ranh giới tự nhiên khi cả shop và NO đều khác nhau
                if (current_shop != next_shop and current_shop and next_shop and
                    current_no != next_no):
                    boundaries.append({
                        'position': pos + 1,
                        'type': 'shop_and_no_boundary',
                        'priority': 1,  # Ưu tiên cao nhất
                        'from_shop': current_shop,
                        'to_shop': next_shop,
                        'from_no': current_no,
                        'to_no': next_no
                    })
                # Ranh giới shop (ưu tiên thứ 2)
                elif current_shop != next_shop and current_shop and next_shop:
                    boundaries.append({
                        'position': pos + 1,
                        'type': 'shop_boundary',
                        'priority': 2,
                        'from_shop': current_shop,
                        'to_shop': next_shop
                    })
                # Ranh giới NO (ưu tiên thứ 3)
                elif current_no != next_no:
                    boundaries.append({
                        'position': pos + 1,
                        'type': 'no_boundary',
                        'priority': 3,
                        'from_no': current_no,
                        'to_no': next_no
                    })

        # Sắp xếp theo độ ưu tiên
        boundaries.sort(key=lambda x: x['priority'])

        return boundaries

    def find_optimal_positions(self, current_ids, input_ids, top_limits, is_grouped_flags, condition_groups=None):
        """
        Tìm vị trí tối ưu cho các ID input dựa trên thuật toán smart placement.

        QUAN TRỌNG: ID Input được xử lý RIÊNG BIỆT và KHÔNG được gom với ID cơ sở cùng shopID.
        Ưu tiên: Top limit constraints > ID Input grouping > Shop optimization (chỉ cho ID cơ sở)

        Args:
            current_ids: Danh sách ID hiện tại (ID cơ sở)
            input_ids: Danh sách ID input cần chèn (ID từ UI)
            top_limits: Dict {id: top_limit}
            is_grouped_flags: Dict {id: is_grouped}
            condition_groups: Dict {id: condition_index} - nhóm điều kiện của mỗi ID

        Returns:
            List[(id, position, placement_type)]
        """
        self.log(f"\n=== BẮT ĐẦU SMART PLACEMENT CHO {len(input_ids)} ID INPUT ===")
        self.log(f"QUAN TRỌNG: ID Input sẽ được xử lý RIÊNG BIỆT, KHÔNG gom với ID cơ sở cùng shopID")

        # Phân tích cấu trúc hiện tại (chỉ dựa trên ID cơ sở)
        structure, continuous_groups = self.analyze_current_structure(current_ids)

        self.log(f"Phân tích cấu trúc:")
        self.log(f"  - Vị trí trống: {len(structure['empty_positions'])}")
        self.log(f"  - Vị trí boundary: {len(structure['boundary_positions'])}")
        self.log(f"  - Vị trí group edge: {len(structure['group_edge_positions'])}")
        self.log(f"  - Vị trí group middle: {len(structure['group_middle_positions'])}")
        self.log(f"  - Ranh giới tự nhiên: {len(structure['natural_boundaries'])}")

        # Log chi tiết ranh giới tự nhiên
        for boundary in structure['natural_boundaries'][:5]:  # Chỉ hiển thị 5 ranh giới đầu tiên
            self.log(f"    Vị trí {boundary['position']}: {boundary['type']} (priority: {boundary['priority']})")

        # Phân loại ID input theo điều kiện UI
        grouped_ids = [id_val for id_val in input_ids if is_grouped_flags.get(id_val, False)]
        non_grouped_ids = [id_val for id_val in input_ids if not is_grouped_flags.get(id_val, False)]

        # Phân chia ID nhóm theo điều kiện (condition groups)
        condition_groups_dict = self._group_ids_by_condition(grouped_ids, condition_groups or {})

        self.log(f"Phân loại ID input:")
        self.log(f"  - ID nhóm: {len(grouped_ids)} (chia thành {len(condition_groups_dict)} nhóm điều kiện)")
        for condition_idx, ids in condition_groups_dict.items():
            self.log(f"    Điều kiện {condition_idx}: {len(ids)} IDs - {ids}")
        self.log(f"  - ID không nhóm: {len(non_grouped_ids)}")

        placement_results = []

        # Xử lý tất cả nhóm điều kiện với thuật toán phân tán thông minh
        if condition_groups_dict:
            self.log(f"\nXử lý {len(condition_groups_dict)} nhóm điều kiện với thuật toán phân tán")
            distributed_placements = self._place_multiple_groups_distributed(condition_groups_dict, top_limits, current_ids, structure)
            placement_results.extend(distributed_placements)

        # Xử lý ID không nhóm với smart placement
        if non_grouped_ids:
            self.log(f"\nXử lý {len(non_grouped_ids)} ID không nhóm với Smart Placement")
            smart_results = self._smart_place_non_grouped_ids(
                current_ids, non_grouped_ids, top_limits, structure
            )
            placement_results.extend(smart_results)

        # KIỂM TRA CUỐI CÙNG: Đảm bảo tất cả vị trí đều tuân thủ avoid_position
        violations = []
        for id_val, position, placement_type in placement_results:
            top_limit = top_limits.get(id_val, len(current_ids))
            avoid_limit = self._get_avoid_positions_for_top(top_limit)
            if position < avoid_limit:
                violations.append((id_val, position, avoid_limit, top_limit, placement_type))

        if violations:
            self.log(f"❌ PHÁT HIỆN {len(violations)} VI PHẠM AVOID_POSITION TRONG SMART PLACEMENT:")
            for id_val, position, avoid_limit, top_limit, placement_type in violations:
                self.log(f"  - ID {id_val}: vị trí {position} < avoid_limit {avoid_limit} (top_limit: {top_limit}, type: {placement_type})")
            raise Exception(f"Smart Placement Engine vi phạm avoid_position cho {len(violations)} ID")
        else:
            self.log(f"✅ TẤT CẢ {len(placement_results)} ID TUÂN THỦ AVOID_POSITION")

        self.log(f"=== KẾT THÚC SMART PLACEMENT ===\n")
        return placement_results

    def _group_ids_by_condition(self, grouped_ids, condition_groups):
        """Phân chia ID nhóm theo điều kiện UI (condition groups)"""
        condition_groups_dict = {}

        for id_val in grouped_ids:
            # Lấy condition index từ condition_groups, mặc định là 0 nếu không có
            condition_idx = condition_groups.get(id_val, 0)

            if condition_idx not in condition_groups_dict:
                condition_groups_dict[condition_idx] = []
            condition_groups_dict[condition_idx].append(id_val)

        return condition_groups_dict

    def _place_multiple_groups_distributed(self, condition_groups_dict, top_limits, current_ids, structure):
        """
        Phân tán nhiều nhóm ID input theo avoid_limit để tránh tạo cụm liên hoàn
        """
        placement_results = []

        if not condition_groups_dict:
            return placement_results

        # Phân nhóm theo avoid_limit
        avoid_limit_groups = self._group_by_avoid_limit(condition_groups_dict, top_limits)

        self.log(f"  Phân nhóm theo avoid_limit:")
        for avoid_limit, groups in avoid_limit_groups.items():
            total_ids = sum(len(condition_groups_dict[idx]) for idx in groups)
            self.log(f"    Avoid_limit {avoid_limit}: {len(groups)} nhóm điều kiện, {total_ids} ID")

        # Xử lý từng nhóm avoid_limit riêng biệt
        for avoid_limit, condition_indices in avoid_limit_groups.items():
            self.log(f"\n--- Xử lý nhóm avoid_limit {avoid_limit} ---")

            # Tạo dict con cho nhóm này
            sub_condition_groups = {idx: condition_groups_dict[idx] for idx in condition_indices}

            # Phân tích ranh giới cho nhóm này
            group_boundaries = self._analyze_boundaries_for_avoid_group(sub_condition_groups, top_limits, avoid_limit, structure)

            if not group_boundaries:
                self.log(f"  ⚠️ Không tìm thấy ranh giới cho avoid_limit {avoid_limit}, fallback")
                for condition_idx in condition_indices:
                    condition_ids = condition_groups_dict[condition_idx]
                    fallback_placements = self._place_grouped_ids_consecutively(condition_ids, top_limits, current_ids, structure)
                    placement_results.extend(fallback_placements)
                continue

            # Phân tán các nhóm trong cùng avoid_limit
            distributed_boundaries = self._distribute_groups_to_boundaries(sub_condition_groups, group_boundaries)

            # Đặt từng nhóm
            for condition_idx, boundary_info in distributed_boundaries.items():
                condition_ids = condition_groups_dict[condition_idx]
                self.log(f"  Đặt nhóm {condition_idx} ({len(condition_ids)} ID) tại ranh giới {boundary_info['position']}")

                group_placements = self._place_group_at_boundary(condition_ids, boundary_info, top_limits, current_ids)
                placement_results.extend(group_placements)

        return placement_results

    def _group_by_avoid_limit(self, condition_groups_dict, top_limits):
        """
        Phân nhóm các condition theo avoid_limit
        """
        avoid_limit_groups = {}

        for condition_idx, condition_ids in condition_groups_dict.items():
            # Lấy avoid_limit của ID đầu tiên trong nhóm (giả định cùng điều kiện)
            if condition_ids:
                first_id = condition_ids[0]
                individual_top_limit = top_limits.get(first_id, 500)
                avoid_limit = self._get_avoid_positions_for_top(individual_top_limit)

                if avoid_limit not in avoid_limit_groups:
                    avoid_limit_groups[avoid_limit] = []
                avoid_limit_groups[avoid_limit].append(condition_idx)

        return avoid_limit_groups

    def _analyze_boundaries_for_avoid_group(self, condition_groups_dict, top_limits, avoid_limit, structure):
        """
        Phân tích ranh giới cho một nhóm avoid_limit cụ thể
        """
        # Tính phạm vi cho nhóm này
        all_ids = []
        for condition_ids in condition_groups_dict.values():
            all_ids.extend(condition_ids)

        if not all_ids:
            return []

        # Lấy top_limit thấp nhất trong nhóm
        min_top_limit = min(top_limits.get(id_val, 500) for id_val in all_ids)
        max_pos = min_top_limit - 1

        self.log(f"    Phạm vi cho avoid_limit {avoid_limit}: {avoid_limit}-{max_pos}")

        # Lấy ranh giới trong phạm vi này
        available_boundaries = []
        if 'natural_boundaries' in structure:
            for boundary in structure['natural_boundaries']:
                pos = boundary['position']
                if avoid_limit <= pos <= max_pos:
                    max_group_size = self._calculate_max_group_size_at_boundary(pos, max_pos)

                    boundary_info = {
                        'position': pos,
                        'type': boundary['type'],
                        'priority': boundary['priority'],
                        'max_group_size': max_group_size,
                        'from_shop': boundary.get('from_shop', ''),
                        'to_shop': boundary.get('to_shop', ''),
                        'from_no': boundary.get('from_no', ''),
                        'to_no': boundary.get('to_no', '')
                    }
                    available_boundaries.append(boundary_info)

        # Sắp xếp theo độ ưu tiên
        available_boundaries.sort(key=lambda x: (x['priority'], x['position']))

        self.log(f"    Tìm thấy {len(available_boundaries)} ranh giới cho avoid_limit {avoid_limit}")

        return available_boundaries

    def _analyze_available_boundaries(self, condition_groups_dict, top_limits, structure):
        """
        Phân tích tất cả ranh giới khả dụng cho các nhóm ID input
        """
        # Tính top_limit chung cho tất cả nhóm
        all_ids = []
        for condition_ids in condition_groups_dict.values():
            all_ids.extend(condition_ids)

        if not all_ids:
            return []

        # Tính phạm vi chung dựa trên tất cả ID (để tìm ranh giới có thể)
        min_avoid_limit = float('inf')
        max_top_limit = 0

        for id_val in all_ids:
            individual_top_limit = top_limits.get(id_val, 500)
            individual_avoid_limit = self._get_avoid_positions_for_top(individual_top_limit)

            min_avoid_limit = min(min_avoid_limit, individual_avoid_limit)
            max_top_limit = max(max_top_limit, individual_top_limit)

        # Phạm vi tìm ranh giới: từ avoid_limit thấp nhất đến top_limit cao nhất
        search_min_pos = min_avoid_limit
        search_max_pos = max_top_limit - 1

        self.log(f"  Phân tích phạm vi cho {len(all_ids)} ID:")
        self.log(f"    Min avoid_limit: {min_avoid_limit}")
        self.log(f"    Max top_limit: {max_top_limit}")
        self.log(f"    Phạm vi tìm ranh giới: {search_min_pos}-{search_max_pos}")

        # Log chi tiết từng ID
        for id_val in all_ids:
            individual_top_limit = top_limits.get(id_val, 500)
            individual_avoid_limit = self._get_avoid_positions_for_top(individual_top_limit)
            self.log(f"    ID {id_val}: avoid={individual_avoid_limit}, top={individual_top_limit}")

        # Lấy tất cả ranh giới tự nhiên trong phạm vi tìm kiếm
        available_boundaries = []
        if 'natural_boundaries' in structure:
            for boundary in structure['natural_boundaries']:
                pos = boundary['position']
                if search_min_pos <= pos <= search_max_pos:
                    # Tính toán kích thước nhóm lớn nhất có thể đặt tại ranh giới này
                    max_group_size = self._calculate_max_group_size_at_boundary(pos, search_max_pos)

                    boundary_info = {
                        'position': pos,
                        'type': boundary['type'],
                        'priority': boundary['priority'],
                        'max_group_size': max_group_size,
                        'from_shop': boundary.get('from_shop', ''),
                        'to_shop': boundary.get('to_shop', ''),
                        'from_no': boundary.get('from_no', ''),
                        'to_no': boundary.get('to_no', '')
                    }
                    available_boundaries.append(boundary_info)

        # Sắp xếp theo độ ưu tiên và vị trí
        available_boundaries.sort(key=lambda x: (x['priority'], x['position']))

        self.log(f"  Tìm thấy {len(available_boundaries)} ranh giới khả dụng trong phạm vi {search_min_pos}-{search_max_pos}")
        for boundary in available_boundaries[:5]:  # Log 5 ranh giới đầu tiên
            self.log(f"    Vị trí {boundary['position']}: {boundary['type']} (max_group: {boundary['max_group_size']})")

        return available_boundaries

    def _calculate_max_group_size_at_boundary(self, boundary_pos, max_pos):
        """Tính kích thước nhóm lớn nhất có thể đặt tại ranh giới"""
        return max_pos - boundary_pos + 1

    def _distribute_groups_to_boundaries(self, condition_groups_dict, available_boundaries):
        """
        Phân tán các nhóm ID input vào các ranh giới khác nhau để tối đa hóa khoảng cách
        """
        distributed = {}
        num_groups = len(condition_groups_dict)
        num_boundaries = len(available_boundaries)

        if num_boundaries == 0:
            return distributed

        # Sắp xếp nhóm theo kích thước (nhóm lớn trước)
        sorted_groups = sorted(condition_groups_dict.items(),
                             key=lambda x: len(x[1]), reverse=True)

        if num_groups <= num_boundaries:
            # Đủ ranh giới cho tất cả nhóm - phân tán tối đa
            self.log(f"  Phân tán {num_groups} nhóm vào {num_boundaries} ranh giới (đủ chỗ)")

            # Chọn ranh giới cách xa nhau nhất
            selected_boundaries = self._select_spaced_boundaries(available_boundaries, num_groups)

            for i, (condition_idx, condition_ids) in enumerate(sorted_groups):
                if i < len(selected_boundaries):
                    boundary = selected_boundaries[i]
                    # Kiểm tra xem ranh giới có đủ chỗ cho nhóm không
                    if boundary['max_group_size'] >= len(condition_ids):
                        distributed[condition_idx] = boundary
                        self.log(f"    Nhóm {condition_idx} ({len(condition_ids)} ID) → ranh giới {boundary['position']}")
                    else:
                        self.log(f"    ⚠️ Nhóm {condition_idx} ({len(condition_ids)} ID) quá lớn cho ranh giới {boundary['position']} (max: {boundary['max_group_size']})")
        else:
            # Không đủ ranh giới - ưu tiên nhóm lớn nhất
            self.log(f"  Phân tán {num_groups} nhóm vào {num_boundaries} ranh giới (thiếu chỗ)")

            for i, (condition_idx, condition_ids) in enumerate(sorted_groups):
                if i < num_boundaries:
                    boundary = available_boundaries[i]
                    if boundary['max_group_size'] >= len(condition_ids):
                        distributed[condition_idx] = boundary
                        self.log(f"    Nhóm {condition_idx} ({len(condition_ids)} ID) → ranh giới {boundary['position']}")
                    else:
                        self.log(f"    ⚠️ Nhóm {condition_idx} ({len(condition_ids)} ID) quá lớn cho ranh giới {boundary['position']}")
                else:
                    self.log(f"    ⚠️ Nhóm {condition_idx} ({len(condition_ids)} ID) không có ranh giới phù hợp")

        return distributed

    def _select_spaced_boundaries(self, boundaries, num_needed):
        """
        Chọn các ranh giới cách xa nhau nhất để tối đa hóa khoảng cách
        """
        if num_needed >= len(boundaries):
            return boundaries

        if num_needed == 1:
            return [boundaries[0]]  # Chọn ranh giới ưu tiên cao nhất

        # Thuật toán greedy để chọn ranh giới cách xa nhau
        selected = [boundaries[0]]  # Bắt đầu với ranh giới đầu tiên

        for _ in range(num_needed - 1):
            best_boundary = None
            max_min_distance = -1

            for boundary in boundaries:
                if boundary in selected:
                    continue

                # Tính khoảng cách tối thiểu đến các ranh giới đã chọn
                min_distance = min(abs(boundary['position'] - sel['position']) for sel in selected)

                if min_distance > max_min_distance:
                    max_min_distance = min_distance
                    best_boundary = boundary

            if best_boundary:
                selected.append(best_boundary)

        # Sắp xếp theo vị trí
        selected.sort(key=lambda x: x['position'])

        self.log(f"    Chọn {len(selected)} ranh giới cách xa: {[b['position'] for b in selected]}")
        return selected

    def _place_group_at_boundary(self, condition_ids, boundary_info, top_limits, current_ids):
        """
        Đặt một nhóm ID tại ranh giới cụ thể với validation avoid_position
        """
        placement_results = []
        boundary_pos = boundary_info['position']
        group_size = len(condition_ids)

        # Kiểm tra xem có đủ chỗ không
        if boundary_info['max_group_size'] < group_size:
            self.log(f"    ❌ Không đủ chỗ tại ranh giới {boundary_pos} cho {group_size} ID")
            return placement_results

        # Đặt các ID liền kề nhau bắt đầu từ ranh giới
        for i, id_val in enumerate(condition_ids):
            position = boundary_pos + i

            # Kiểm tra avoid_position cá nhân
            individual_top_limit = top_limits.get(id_val, len(current_ids))
            individual_avoid_limit = self._get_avoid_positions_for_top(individual_top_limit)

            if position < individual_avoid_limit:
                self.log(f"    ❌ ID {id_val} vi phạm avoid_position ({position + 1} < {individual_avoid_limit}) - bỏ qua")
                continue

            # Kiểm tra top_limit cá nhân
            if position >= individual_top_limit:
                self.log(f"    ❌ ID {id_val} vượt top_limit cá nhân ({position + 1} >= {individual_top_limit}) - bỏ qua")
                continue

            placement_results.append((id_val, position, 'GROUPED_DISTRIBUTED'))
            self.log(f"    ✓ Đặt ID {id_val} tại vị trí {position + 1} (avoid_limit: {individual_avoid_limit}, top_limit: {individual_top_limit})")

        return placement_results

    def _place_grouped_ids_consecutively(self, grouped_ids, top_limits, current_ids, structure):
        """Đặt các ID nhóm liền kề nhau - BẮT BUỘC tìm đủ vị trí liền kề"""
        placement_results = []

        if not grouped_ids:
            return placement_results

        # Tìm top_limit chung (lấy min để đảm bảo tất cả ID đều tuân thủ)
        min_top_limit = min(top_limits.get(id_val, len(current_ids)) for id_val in grouped_ids)
        avoid_positions_limit = self._get_avoid_positions_for_top(min_top_limit)
        max_pos = min_top_limit - 1  # TUYỆT ĐỐI không vượt top_limit

        # Tính số vị trí cần thiết
        group_size = len(grouped_ids)

        self.log(f"  Tìm {group_size} vị trí liền kề trong phạm vi {avoid_positions_limit}-{max_pos} (top_limit: {min_top_limit})")

        # BẮT BUỘC tìm vùng liền kề có thể chứa toàn bộ nhóm
        best_start_pos = self._find_consecutive_positions_with_boundaries(
            avoid_positions_limit, max_pos, group_size, structure
        )

        if best_start_pos is not None:
            # Đặt các ID liền kề nhau
            for i, id_val in enumerate(grouped_ids):
                position = best_start_pos + i
                # KIỂM TRA CUỐI: Đảm bảo không vượt top_limit
                individual_top_limit = top_limits.get(id_val, len(current_ids))
                if position >= individual_top_limit:
                    raise Exception(f"CRITICAL ERROR: ID {id_val} được đặt ở vị trí {position+1} vượt top_limit {individual_top_limit}")

                placement_results.append((id_val, position, 'GROUPED'))
                self.log(f"  ID nhóm {id_val}: vị trí {position+1} (nhóm liền kề, top_limit: {individual_top_limit})")
        else:
            # KHÔNG CÓ FALLBACK - Báo lỗi nghiêm trọng
            available_space = max_pos - avoid_positions_limit + 1
            error_msg = f"KHÔNG THỂ tìm {group_size} vị trí liền kề trong phạm vi {avoid_positions_limit}-{max_pos} (chỉ có {available_space} vị trí)"
            self.log(f"  ❌ {error_msg}")
            raise Exception(error_msg)

        return placement_results

    def _find_consecutive_positions_strict(self, min_pos, max_pos, group_size):
        """Tìm vị trí bắt đầu cho nhóm liền kề - STRICT mode"""
        available_space = max_pos - min_pos + 1

        self.log(f"    Kiểm tra: cần {group_size} vị trí, có {available_space} vị trí khả dụng ({min_pos}-{max_pos})")

        if available_space < group_size:
            self.log(f"    ❌ Không đủ chỗ: cần {group_size}, chỉ có {available_space}")
            return None  # Không đủ chỗ

        # Tìm tất cả vị trí bắt đầu hợp lệ
        possible_starts = []
        for start in range(min_pos, max_pos - group_size + 2):
            end_pos = start + group_size - 1
            if end_pos <= max_pos:  # Đảm bảo không vượt max_pos
                possible_starts.append(start)
                self.log(f"    ✓ Vị trí hợp lệ: {start}-{end_pos}")

        if not possible_starts:
            self.log(f"    ❌ Không tìm được vị trí bắt đầu hợp lệ")
            return None

        # Chọn ngẫu nhiên một vị trí bắt đầu hợp lệ
        selected_start = random.choice(possible_starts)
        self.log(f"    ✓ Chọn vị trí bắt đầu: {selected_start} (nhóm: {selected_start}-{selected_start + group_size - 1})")
        return selected_start

    def _find_consecutive_positions_with_boundaries(self, min_pos, max_pos, group_size, structure):
        """
        Tìm vị trí bắt đầu cho nhóm liền kề, ưu tiên ranh giới tự nhiên
        """
        available_space = max_pos - min_pos + 1

        self.log(f"    Kiểm tra: cần {group_size} vị trí, có {available_space} vị trí khả dụng ({min_pos}-{max_pos})")

        if available_space < group_size:
            self.log(f"    ❌ Không đủ chỗ: cần {group_size}, chỉ có {available_space}")
            return None

        # Tìm tất cả vị trí bắt đầu hợp lệ
        possible_starts = []
        boundary_starts = []  # Vị trí bắt đầu tại ranh giới tự nhiên

        # Lấy danh sách ranh giới tự nhiên trong phạm vi cho phép
        natural_boundaries = []
        if 'natural_boundaries' in structure:
            for boundary in structure['natural_boundaries']:
                pos = boundary['position']
                if min_pos <= pos <= max_pos - group_size + 1:
                    natural_boundaries.append(pos)

        # Kiểm tra tất cả vị trí bắt đầu có thể
        for start in range(min_pos, max_pos - group_size + 2):
            end_pos = start + group_size - 1
            if end_pos <= max_pos:
                possible_starts.append(start)

                # Kiểm tra xem có phải ranh giới tự nhiên không
                if start in natural_boundaries:
                    boundary_starts.append(start)
                    self.log(f"    ✓ Vị trí ranh giới tự nhiên: {start}-{end_pos}")
                else:
                    self.log(f"    ✓ Vị trí thông thường: {start}-{end_pos}")

        if not possible_starts:
            self.log(f"    ❌ Không tìm được vị trí bắt đầu hợp lệ")
            return None

        # Ưu tiên ranh giới tự nhiên nếu có
        if boundary_starts:
            selected_start = random.choice(boundary_starts)
            self.log(f"    ✓ Chọn vị trí ranh giới tự nhiên: {selected_start} (nhóm: {selected_start}-{selected_start + group_size - 1})")
        else:
            selected_start = random.choice(possible_starts)
            self.log(f"    ✓ Chọn vị trí thông thường: {selected_start} (nhóm: {selected_start}-{selected_start + group_size - 1})")

        return selected_start

    def _smart_place_non_grouped_ids(self, current_ids, input_ids, top_limits, structure):
        """
        Xử lý placement cho ID không nhóm với thuật toán thông minh
        QUAN TRỌNG: ID lẻ cũng phải hoạt động như ID nhóm - KHÔNG chèn ngang các nhóm NO/ShopID
        """

        # Nhóm ID theo top_limit để xử lý bottleneck
        top_limit_groups = {}
        for id_val in input_ids:
            top_limit = top_limits.get(id_val, len(current_ids))
            if top_limit not in top_limit_groups:
                top_limit_groups[top_limit] = []
            top_limit_groups[top_limit].append(id_val)

        placement_results = []

        for top_limit, ids_in_group in top_limit_groups.items():
            self.log(f"\nXử lý nhóm {len(ids_in_group)} ID LẺ với top limit {top_limit} (sử dụng intelligent insertion)")

            # Tạo pool vị trí ưu tiên ranh giới (KHÔNG bao gồm GROUP_MIDDLE)
            priority_pools = self._create_priority_pools_for_single_ids(structure, top_limit)

            # Sử dụng intelligent insertion thay vì random placement
            group_placements = self._intelligent_place_single_ids(
                ids_in_group, priority_pools, top_limit, current_ids, structure
            )

            placement_results.extend(group_placements)

        return placement_results

    def _create_priority_pools_for_single_ids(self, structure, top_limit):
        """
        Tạo pool vị trí cho ID lẻ - CHỈ ưu tiên ranh giới, KHÔNG bao gồm GROUP_MIDDLE
        """
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)
        max_pos = top_limit - 1

        pools = {
            'NATURAL_BOUNDARIES': [],  # Ưu tiên cao nhất: ranh giới tự nhiên
            'EMPTY_SLOT': [],          # Vị trí trống
            'BOUNDARY': [],            # Ranh giới shop
            'GROUP_EDGE': []           # Đầu/cuối nhóm (KHÔNG bao gồm GROUP_MIDDLE)
        }

        # Pool 1: Ranh giới tự nhiên (ưu tiên cao nhất)
        if 'natural_boundaries' in structure:
            for boundary in structure['natural_boundaries']:
                pos = boundary['position']
                if avoid_positions_limit <= pos <= max_pos:
                    pools['NATURAL_BOUNDARIES'].append(pos)

        # Pool 2: Vị trí trống
        pools['EMPTY_SLOT'] = [pos for pos in structure.get('empty_positions', [])
                              if avoid_positions_limit <= pos <= max_pos]

        # Pool 3: Boundary positions
        pools['BOUNDARY'] = [pos for pos in structure.get('boundary_positions', [])
                            if avoid_positions_limit <= pos <= max_pos]

        # Pool 4: Group edge positions (KHÔNG bao gồm GROUP_MIDDLE)
        pools['GROUP_EDGE'] = [pos for pos in structure.get('group_edge_positions', [])
                              if avoid_positions_limit <= pos <= max_pos]

        self.log(f"  Pool cho ID lẻ (tránh chèn ngang nhóm):")
        self.log(f"    Phạm vi cho phép: {avoid_positions_limit}-{max_pos}")
        for pool_name, positions in pools.items():
            self.log(f"    Pool {pool_name}: {len(positions)} vị trí")

        return pools

    def _intelligent_place_single_ids(self, input_ids, priority_pools, top_limit, current_ids, structure):
        """
        Đặt ID lẻ sử dụng intelligent insertion để bảo vệ các nhóm hiện có
        """
        placement_results = []
        remaining_ids = input_ids.copy()
        used_positions = set()

        # Thứ tự ưu tiên: ranh giới trước, tránh chèn vào giữa nhóm
        pool_order = ['NATURAL_BOUNDARIES', 'EMPTY_SLOT', 'BOUNDARY', 'GROUP_EDGE']

        for pool_name in pool_order:
            if not remaining_ids:
                break

            available_positions = [pos for pos in priority_pools[pool_name]
                                 if pos not in used_positions]
            if not available_positions:
                continue

            # Sắp xếp vị trí theo mức độ an toàn (ít phá vỡ nhóm nhất)
            safe_positions = self._rank_positions_by_safety(available_positions, current_ids, structure)

            # Đặt từng ID vào vị trí an toàn nhất
            for id_val in remaining_ids[:]:
                if not safe_positions:
                    break

                # Chọn vị trí an toàn nhất
                position = safe_positions.pop(0)
                placement_results.append((id_val, position, f"SINGLE_{pool_name}"))
                used_positions.add(position)
                remaining_ids.remove(id_val)

                self.log(f"    ID lẻ {id_val} -> vị trí {position + 1} (pool: {pool_name}, intelligent insertion)")

        # Fallback cho ID còn lại
        if remaining_ids:
            self.log(f"  Fallback cho {len(remaining_ids)} ID lẻ còn lại")
            fallback_results = self._fallback_placement_with_used_positions(
                remaining_ids, top_limit, used_positions
            )
            placement_results.extend(fallback_results)

        return placement_results

    def _rank_positions_by_safety(self, positions, current_ids, structure):
        """
        Xếp hạng vị trí theo mức độ an toàn (ít phá vỡ nhóm nhất)
        """
        position_scores = []

        for pos in positions:
            safety_score = self._calculate_position_safety_score(pos, current_ids, structure)
            position_scores.append((pos, safety_score))

        # Sắp xếp theo safety_score giảm dần (an toàn nhất trước)
        position_scores.sort(key=lambda x: x[1], reverse=True)

        return [pos for pos, score in position_scores]

    def _calculate_position_safety_score(self, position, current_ids, structure):
        """
        Tính điểm an toàn cho vị trí (càng cao càng ít phá vỡ nhóm)
        """
        score = 0

        # Điểm cao nếu là ranh giới tự nhiên
        if 'natural_boundaries' in structure:
            for boundary in structure['natural_boundaries']:
                if boundary['position'] == position:
                    score += 100  # Điểm cao nhất
                    break

        # Điểm cao nếu là boundary position
        if position in structure.get('boundary_positions', []):
            score += 50

        # Điểm thấp nếu ở giữa nhóm liên tục
        if position in structure.get('group_middle_positions', []):
            score -= 50  # Tránh vị trí này

        # Điểm cao nếu là group edge
        if position in structure.get('group_edge_positions', []):
            score += 25

        return score

    def _create_priority_pools(self, structure, top_limit, current_length):
        """Tạo các pool vị trí theo mức độ ưu tiên, loại trừ avoid_positions"""
        # Tính avoid_positions để loại trừ các vị trí bị cấm
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)

        # QUAN TRỌNG: max_pos phải là top_limit - 1, không phải current_length
        max_pos = top_limit - 1

        pools = {
            'NATURAL_BOUNDARIES': [],  # Ưu tiên cao nhất: ranh giới tự nhiên
            'EMPTY_SLOT': [],
            'BOUNDARY': [],
            'GROUP_EDGE': [],
            'GROUP_MIDDLE': []
        }

        # Pool 0: Ranh giới tự nhiên (ưu tiên cao nhất)
        if 'natural_boundaries' in structure:
            for boundary in structure['natural_boundaries']:
                pos = boundary['position']
                if avoid_positions_limit <= pos <= max_pos:
                    pools['NATURAL_BOUNDARIES'].append(pos)

        # Pool A: Vị trí trống trong phạm vi top_limit, loại trừ avoid_positions
        pools['EMPTY_SLOT'] = [pos for pos in structure['empty_positions']
                              if avoid_positions_limit <= pos <= max_pos]

        # Pool B: Vị trí boundary trong phạm vi top_limit, loại trừ avoid_positions
        pools['BOUNDARY'] = [pos for pos in structure['boundary_positions']
                            if avoid_positions_limit <= pos <= max_pos]

        # Pool C: Vị trí group edge trong phạm vi top_limit, loại trừ avoid_positions
        pools['GROUP_EDGE'] = [pos for pos in structure['group_edge_positions']
                              if avoid_positions_limit <= pos <= max_pos]

        # Pool D: Vị trí group middle trong phạm vi top_limit, loại trừ avoid_positions
        pools['GROUP_MIDDLE'] = [pos for pos in structure['group_middle_positions']
                                if avoid_positions_limit <= pos <= max_pos]

        # Log thông tin pools
        self.log(f"  Avoid positions: 0-{avoid_positions_limit-1} (bị loại trừ)")
        self.log(f"  Phạm vi cho phép: {avoid_positions_limit}-{max_pos}")
        for pool_name, positions in pools.items():
            self.log(f"  Pool {pool_name}: {len(positions)} vị trí")

        return pools

    def _get_avoid_positions_for_top(self, top_limit):
        """Tính avoid_positions dựa trên top_limit (logic giống TimeSlotProcessor.get_avoid_positions)"""
        # Kiểm tra nếu là ATC (top_limit = 350 và cần avoid 0-149)
        if top_limit == 350: return 149  # ATC bắt đầu từ vị trí 150
        elif top_limit == 10: return 3
        elif top_limit == 20: return 7
        elif top_limit == 30: return 10
        elif top_limit == 50: return 20
        elif top_limit == 100: return 30
        elif top_limit == 150: return 70
        elif top_limit == 200: return 100
        else: return top_limit // 5

    def _distribute_ids_to_pools(self, input_ids, priority_pools, top_limit):
        """Phân bổ ID vào các pool theo thứ tự ưu tiên"""
        placement_results = []
        remaining_ids = input_ids.copy()
        used_positions = set()  # Theo dõi các vị trí đã sử dụng

        # Thứ tự ưu tiên pools (NATURAL_BOUNDARIES có ưu tiên cao nhất)
        pool_order = ['NATURAL_BOUNDARIES', 'EMPTY_SLOT', 'BOUNDARY', 'GROUP_EDGE', 'GROUP_MIDDLE']

        for pool_name in pool_order:
            if not remaining_ids:
                break

            # Lọc ra các vị trí chưa được sử dụng
            available_positions = [pos for pos in priority_pools[pool_name]
                                 if pos not in used_positions]
            if not available_positions:
                continue

            # Số lượng ID có thể đặt vào pool này
            ids_to_place = min(len(remaining_ids), len(available_positions))

            if ids_to_place > 0:
                # Random chọn vị trí KHÁC NHAU
                selected_positions = random.sample(available_positions, ids_to_place)
                selected_ids = remaining_ids[:ids_to_place]

                # Tạo assignments trực tiếp (không dùng adaptive randomness để tránh lỗi)
                final_assignments = []
                for i, id_val in enumerate(selected_ids):
                    if i < len(selected_positions):
                        position = selected_positions[i]
                        final_assignments.append((id_val, position, pool_name))
                        used_positions.add(position)  # Đánh dấu vị trí đã sử dụng
                        self.log(f"    Gán {id_val} -> vị trí {position} (pool: {pool_name})")

                placement_results.extend(final_assignments)
                remaining_ids = remaining_ids[ids_to_place:]

                self.log(f"  Đặt {len(final_assignments)} ID vào pool {pool_name}")

        # Xử lý ID còn lại (nếu có) - buộc phải chèn
        if remaining_ids:
            self.log(f"  Còn {len(remaining_ids)} ID cần xử lý bằng fallback")
            fallback_results = self._fallback_placement_with_used_positions(
                remaining_ids, top_limit, used_positions
            )
            placement_results.extend(fallback_results)

        return placement_results

    def _apply_adaptive_randomness(self, ids, positions, pool_type):
        """Áp dụng randomness thông minh để tránh tạo pattern"""
        if len(ids) <= 1:
            return [(ids[0], positions[0], pool_type)] if ids and positions else []

        # Sắp xếp vị trí để kiểm tra khoảng cách
        sorted_positions = sorted(positions)

        # Áp dụng khoảng cách tối thiểu nếu có thể
        filtered_positions = []
        last_selected = -MIN_DISTANCE_BETWEEN_INPUT_IDS - 1

        for pos in sorted_positions:
            if pos - last_selected >= MIN_DISTANCE_BETWEEN_INPUT_IDS:
                filtered_positions.append(pos)
                last_selected = pos

        # Nếu không đủ vị trí với khoảng cách tối thiểu, sử dụng tất cả
        if len(filtered_positions) < len(ids):
            filtered_positions = positions

        # Random shuffle để tránh pattern
        random.shuffle(filtered_positions)
        random.shuffle(ids)

        # Tạo kết quả
        results = []
        for i, id_val in enumerate(ids):
            if i < len(filtered_positions):
                results.append((id_val, filtered_positions[i], pool_type))

        return results

    def _fallback_placement(self, remaining_ids, top_limit):
        """Xử lý fallback cho ID không thể đặt vào pool nào - PHẢI tuân thủ avoid_position"""
        results = []
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)

        for i, id_val in enumerate(remaining_ids):
            # QUAN TRỌNG: Đảm bảo không đặt vào vùng avoid_position
            position = max(avoid_positions_limit, top_limit - len(remaining_ids) + i)
            position = min(position, top_limit - 1)
            results.append((id_val, position, 'FALLBACK'))
            self.log(f"    Fallback (tuân thủ avoid): {id_val} -> vị trí {position} (avoid_limit: {avoid_positions_limit})")
        return results

    def _fallback_placement_with_used_positions(self, remaining_ids, top_limit, used_positions):
        """Xử lý fallback cho ID còn lại, tránh các vị trí đã sử dụng và tôn trọng avoid_positions"""
        results = []

        # Tính avoid_positions để loại trừ các vị trí bị cấm
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)
        max_pos = top_limit - 1

        # Tìm các vị trí chưa được sử dụng trong phạm vi cho phép (từ avoid_positions_limit đến max_pos)
        available_positions = []
        for pos in range(avoid_positions_limit, max_pos + 1):
            if pos not in used_positions:
                available_positions.append(pos)

        # Nếu không đủ vị trí trong phạm vi top_limit, mở rộng ra ngoài top_limit
        while len(available_positions) < len(remaining_ids):
            max_pos += 1
            if max_pos not in used_positions:
                available_positions.append(max_pos)

        # Random chọn vị trí cho các ID còn lại
        if len(available_positions) >= len(remaining_ids):
            selected_positions = random.sample(available_positions, len(remaining_ids))
        else:
            # Trường hợp khẩn cấp: không đủ vị trí
            selected_positions = available_positions + list(range(max_pos + 1, max_pos + 1 + len(remaining_ids) - len(available_positions)))

        for i, id_val in enumerate(remaining_ids):
            position = selected_positions[i]
            results.append((id_val, position, 'FALLBACK'))
            self.log(f"    Fallback: {id_val} -> vị trí {position} (avoid_limit: {avoid_positions_limit})")

        return results

    def find_optimal_insertion_for_review_id(self, current_ids, input_id, target_position, top_limit):
        """
        Tìm vị trí chèn tối ưu cho ID input khi vị trí target có ID review cùng shop.
        Tôn trọng shop grouping bằng cách tìm vị trí không phá vỡ nhóm shop.

        Args:
            current_ids: Danh sách ID hiện tại
            input_id: ID input cần chèn
            target_position: Vị trí ban đầu muốn chèn
            top_limit: Giới hạn top

        Returns:
            int: Vị trí tối ưu để chèn
        """
        if target_position >= len(current_ids):
            return target_position

        target_id = current_ids[target_position]
        if not target_id or not self.deal_list_manager.has_review(target_id):
            return target_position  # Không phải ID review, chèn bình thường

        target_shop = self.deal_list_manager.id_to_shop.get(target_id, "")
        if not target_shop:
            return target_position

        self.log(f"Phát hiện xung đột với ID review {target_id} (shop: {target_shop}) tại vị trí {target_position}")

        # Tìm ranh giới nhóm shop hiện tại
        group_start, group_end = self._find_shop_group_boundaries(current_ids, target_position, target_shop)

        self.log(f"Nhóm shop {target_shop} từ vị trí {group_start} đến {group_end}")

        # Tìm vị trí chèn tối ưu
        max_search_pos = min(top_limit - 1, len(current_ids) - 1)
        avoid_positions_limit = self._get_avoid_positions_for_top(top_limit)

        # Ưu tiên 1: Vị trí trước nhóm shop (phải >= avoid_positions_limit)
        if group_start > 0:
            candidate_pos = group_start
            if candidate_pos <= max_search_pos and candidate_pos >= avoid_positions_limit:
                prev_id = current_ids[candidate_pos - 1] if candidate_pos > 0 else None
                if not prev_id or self.deal_list_manager.id_to_shop.get(prev_id, "") != target_shop:
                    self.log(f"Tìm thấy vị trí tối ưu trước nhóm: {candidate_pos}")
                    return candidate_pos

        # Ưu tiên 2: Vị trí sau nhóm shop (phải >= avoid_positions_limit)
        if group_end < len(current_ids) - 1:
            candidate_pos = group_end + 1
            if candidate_pos <= max_search_pos and candidate_pos >= avoid_positions_limit:
                next_id = current_ids[candidate_pos] if candidate_pos < len(current_ids) else None
                if not next_id or self.deal_list_manager.id_to_shop.get(next_id, "") != target_shop:
                    self.log(f"Tìm thấy vị trí tối ưu sau nhóm: {candidate_pos}")
                    return candidate_pos

        # Ưu tiên 3: Tìm vị trí boundary gần nhất trong phạm vi top_limit (phải >= avoid_positions_limit)
        for distance in range(1, max_search_pos + 1):
            # Tìm về phía trước
            check_pos = target_position - distance
            if (check_pos >= avoid_positions_limit and
                check_pos >= 0 and
                self._is_boundary_position(current_ids, check_pos)):
                self.log(f"Tìm thấy boundary position phía trước: {check_pos}")
                return check_pos

            # Tìm về phía sau
            check_pos = target_position + distance
            if (check_pos >= avoid_positions_limit and
                check_pos <= max_search_pos and
                check_pos < len(current_ids) and
                self._is_boundary_position(current_ids, check_pos)):
                self.log(f"Tìm thấy boundary position phía sau: {check_pos}")
                return check_pos

        # Fallback: Chèn vào vị trí gốc (chen ngang)
        self.log(f"Không tìm được vị trí tối ưu, chen ngang vào vị trí gốc: {target_position}")
        return target_position

    def _find_shop_group_boundaries(self, current_ids, position, shop_id):
        """Tìm ranh giới của nhóm shop liên tục chứa vị trí position"""
        start = position
        end = position

        # Tìm về phía trước
        while start > 0:
            prev_id = current_ids[start - 1]
            if prev_id and self.deal_list_manager.id_to_shop.get(prev_id, "") == shop_id:
                start -= 1
            else:
                break

        # Tìm về phía sau
        while end < len(current_ids) - 1:
            next_id = current_ids[end + 1]
            if next_id and self.deal_list_manager.id_to_shop.get(next_id, "") == shop_id:
                end += 1
            else:
                break

        return start, end

    def _is_boundary_position(self, current_ids, position):
        """Kiểm tra xem vị trí có phải là boundary giữa 2 shop khác nhau không"""
        if position <= 0 or position >= len(current_ids):
            return False

        prev_id = current_ids[position - 1]
        curr_id = current_ids[position]

        if not prev_id or not curr_id:
            return True  # Có vị trí trống

        prev_shop = self.deal_list_manager.id_to_shop.get(prev_id, "")
        curr_shop = self.deal_list_manager.id_to_shop.get(curr_id, "")

        return prev_shop != curr_shop and prev_shop and curr_shop

# Lớp PopupListWidget hiển thị danh sách lựa chọn
class PopupListWidget(QListWidget):
    selected = pyqtSignal(int)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.installEventFilter(self)

        # Thiết lập style
        self.setStyleSheet("""
            QListWidget {
                background-color: #2D2D2D;
                border: 1px solid #555;
                color: white;
                outline: none;
            }
            QListWidget::item {
                padding: 5px;
                min-height: 25px;
            }
            QListWidget::item:selected {
                background-color: #3D3D3D;
            }
            QListWidget::item:hover {
                background-color: #454545;
            }
        """)

        self.itemClicked.connect(self.on_item_clicked)

    def on_item_clicked(self, item):
        self.selected.emit(self.row(item))
        self.hide()

    def eventFilter(self, watched, event):
        # Đóng popup khi click bên ngoài
        if event.type() == QEvent.Type.MouseButtonPress:
            pos = event.globalPosition().toPoint()
            if not self.geometry().contains(pos):
                self.hide()
                return True
        return super().eventFilter(watched, event)

# ComboBox tùy chỉnh với popup widget riêng biệt
class CustomComboBox(QComboBox):
    def __init__(self, parent=None):
        super().__init__(parent)

        # Tạo popup widget và kết nối sự kiện
        self.popup_list = PopupListWidget()
        self.popup_list.selected.connect(self.set_selected_item)

        # Thêm style để hiển thị rõ là có thể click
        self.setStyleSheet("""
            QComboBox {
                padding: 3px;
                border: 1px solid #555;
                border-radius: 3px;
                background-color: #2D2D2D;
            }
            QComboBox:hover {
                background-color: #3D3D3D;
                border: 1px solid #777;
            }
        """)

        # Tắt popup mặc định của QComboBox
        self.view().setMinimumWidth(0)
        self.view().setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)

    def showPopup(self):
        # Không hiển thị popup mặc định
        # super().showPopup()  # Comment out để tắt popup mặc định

        # Cập nhật các item trong popup_list
        self.popup_list.clear()
        for i in range(self.count()):
            self.popup_list.addItem(self.itemText(i))

        # Đánh dấu item được chọn
        current_index = self.currentIndex()
        if current_index >= 0:
            self.popup_list.setCurrentRow(current_index)

        # Thiết lập kích thước và vị trí cho popup_list
        self.popup_list.setMinimumWidth(self.width())
        self.popup_list.setMaximumWidth(int(self.width() * 1.2))
        self.popup_list.setMinimumHeight(30 * min(10, self.count()))
        self.popup_list.setMaximumHeight(300)  # Giới hạn chiều cao tối đa

        # Hiển thị popup_list ở vị trí dưới ComboBox
        pos = self.mapToGlobal(QPoint(0, self.height()))
        self.popup_list.move(pos)
        self.popup_list.show()
        self.popup_list.raise_()  # Đảm bảo hiển thị trên cùng

    def set_selected_item(self, index):
        if 0 <= index < self.count():
            self.setCurrentIndex(index)

# Lớp IDInputField nâng cao để xử lý paste nhiều ID với nhiều định dạng
class IDInputField(QLineEdit):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setPlaceholderText("Nhập hoặc dán các ID (tự động nhận diện định dạng)")

        # Thiết lập tooltip hướng dẫn
        self.setToolTip("Hỗ trợ paste nhiều ID từ nhiều nguồn:\n"
                       "- Phân cách bởi dấu phẩy, chấm phẩy, tab, xuống dòng, khoảng trắng\n"
                       "- Tự động dọn dẹp ký tự thừa\n"
                       "- Tự động định dạng lại thành danh sách phân cách bởi dấu phẩy")

        # ID count hiển thị bên ngoài input field
        self.id_count_label = None

        # Kết nối sự kiện paste
        self.textChanged.connect(self.on_text_changed)

        # Biến để theo dõi trạng thái đang xử lý để tránh đệ quy
        self.is_processing = False

        # Thêm nút xóa
        self.clear_button = QPushButton("×", self)
        self.clear_button.setToolTip("Xóa tất cả ID")
        self.clear_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.clear_button.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #777;
                font-size: 16px;
                font-weight: bold;
                padding: 0 5px;
                margin-right: 2px;
            }
            QPushButton:hover {
                color: #aaa;
            }
        """)
        self.clear_button.clicked.connect(self.clear)

        # Ẩn nút xóa khi không có dữ liệu
        self.clear_button.setVisible(False)

        # Cập nhật vị trí các phần tử khi resize
        self.resizeEvent = self.onResize

    def set_id_count_label(self, label):
        """Thiết lập label bên ngoài để hiển thị số lượng ID"""
        self.id_count_label = label
        self.update_id_count()

    def onResize(self, event):
        """Cập nhật vị trí các phần tử khi resize"""
        # Chiều rộng của nút xóa
        clear_width = self.clear_button.sizeHint().width()

        # Tính vị trí cho nút xóa
        clear_x = self.width() - clear_width - 5
        self.clear_button.move(clear_x, (self.height() - self.clear_button.height()) // 2)

        # Đặt padding bên phải cho text input
        self.setTextMargins(0, 0, clear_width + 10, 0)

    def insertFromMimeData(self, source):
        """Override phương thức này để xử lý paste từ clipboard"""
        if source.hasText():
            # Lấy text từ clipboard
            text = source.text()

            # Ghi đè text với phiên bản đã được định dạng
            source.setText(text)  # Giữ nguyên text gốc để QLineEdit xử lý

            # Xử lý định dạng sau khi QLineEdit đã chèn text vào
            # Chúng ta sẽ xử lý trong textChanged để tránh xung đột

        # Gọi phương thức gốc để QLineEdit xử lý paste
        super().insertFromMimeData(source)

    def keyPressEvent(self, event):
        """Override để bắt sự kiện paste từ phím tắt Ctrl+V"""
        # Kiểm tra nếu là Ctrl+V (paste)
        if event.key() == Qt.Key.Key_V and event.modifiers() & Qt.KeyboardModifier.ControlModifier:
            # Lấy text từ clipboard
            clipboard = QApplication.clipboard()
            text = clipboard.text()

            # Định dạng text
            formatted_text = self.format_id_text(text)

            # Đặt text vào clipboard tạm thời
            temp_clipboard = QApplication.clipboard()
            temp_clipboard.setText(formatted_text)

            # Gọi phương thức mặc định để paste
            super().keyPressEvent(event)

            # Khôi phục clipboard
            temp_clipboard.setText(text)
            return

        # Xử lý các phím khác bình thường
        super().keyPressEvent(event)

    def on_text_changed(self, text):
        """Được gọi khi nội dung text thay đổi"""
        if not self.is_processing and text:
            self.is_processing = True
            cleaned_text = self.format_id_text(text)
            if cleaned_text != text:
                self.setText(cleaned_text)
            self.is_processing = False

        # Hiển thị hoặc ẩn nút xóa
        self.clear_button.setVisible(bool(text))

        # Cập nhật số lượng ID
        self.update_id_count()

    def update_id_count(self):
        """Cập nhật số lượng ID"""
        if self.id_count_label:
            id_count = len(self.get_id_list())
            self.id_count_label.setText(f"{id_count} ID")

    def format_id_text(self, text):
        """Xử lý và định dạng text thành danh sách ID phân cách bởi dấu phẩy"""
        if not text:
            return ""

        # 1. Thay thế những ký tự phân tách phổ biến bằng dấu phẩy
        # Thay thế tab, xuống dòng, chấm phẩy bằng dấu phẩy
        text = re.sub(r'[\t\r\n;]+', ',', text)

        # 2. Xử lý trường hợp ID được phân cách bởi khoảng trắng (nhiều space)
        # Thay thế nhiều khoảng trắng liên tiếp bằng một dấu phẩy
        text = re.sub(r'\s{2,}', ',', text)

        # 3. Xử lý trường hợp ID được phân cách bởi khoảng trắng đơn
        # Đây là trường hợp phức tạp hơn vì khoảng trắng đơn có thể là phần của ID
        # Giả sử ID không chứa khoảng trắng, nếu có nhiều từ phân cách bởi khoảng trắng đơn,
        # chúng ta sẽ coi mỗi từ là một ID
        text = re.sub(r'(\S+)\s+(\S+)', r'\1,\2', text)

        # 4. Xử lý các dấu phẩy liên tiếp
        text = re.sub(r',+', ',', text)

        # 5. Loại bỏ dấu phẩy ở đầu và cuối
        text = text.strip(',')

        # 6. Loại bỏ khoảng trắng thừa xung quanh dấu phẩy
        text = re.sub(r'\s*,\s*', ',', text)

        # 7. Tách thành mảng các ID
        ids = text.split(',')

        # 8. Làm sạch từng ID và loại bỏ ID trống
        clean_ids = []
        for id_val in ids:
            id_val = id_val.strip()
            if id_val:  # Chỉ thêm vào nếu không phải chuỗi trống
                clean_ids.append(id_val)

        # 9. Kết hợp lại các ID bằng dấu phẩy
        return ','.join(clean_ids)

    def focusInEvent(self, event):
        """Override phương thức này để chọn toàn bộ text khi focus vào field"""
        super().focusInEvent(event)
        self.selectAll()

    # Phương thức để lấy danh sách ID đã nhập
    def get_id_list(self):
        """Trả về danh sách các ID đã nhập"""
        text = self.text().strip()
        if not text:
            return []
        return [id_val.strip() for id_val in text.split(',') if id_val.strip()]

    def set_id_list(self, id_list):
        """Thiết lập danh sách ID từ list"""
        if not id_list:
            self.setText("")
        else:
            # Chuyển đổi tất cả thành string và nối bằng dấu phẩy
            id_strings = [str(id_val).strip() for id_val in id_list if str(id_val).strip()]
            self.setText(','.join(id_strings))

    def remove_duplicates(self):
        """Loại bỏ các ID trùng lặp"""
        id_list = self.get_id_list()
        # Sử dụng dict.fromkeys() để loại bỏ trùng lặp nhưng giữ nguyên thứ tự
        unique_ids = list(dict.fromkeys(id_list))
        # Cập nhật text với danh sách ID không trùng lặp
        if len(unique_ids) != len(id_list):
            self.setText(','.join(unique_ids))
            return len(id_list) - len(unique_ids)  # Trả về số lượng ID đã xóa
        return 0  # Không có ID nào bị xóa

    def sort_ids(self):
        """Sắp xếp các ID theo thứ tự"""
        id_list = self.get_id_list()
        if not id_list:
            return
        # Sắp xếp ID
        sorted_ids = sorted(id_list)
        # Cập nhật text với danh sách ID đã sắp xếp
        self.setText(','.join(sorted_ids))

class ConditionRow(QFrame):
    def __init__(self, time_slots, remove_callback):
        super().__init__()
        self.layout = QHBoxLayout()
        self.layout.setContentsMargins(5, 2, 5, 0)  # Giảm khoảng cách

        # Sử dụng ComboBox tùy chỉnh với popup widget riêng biệt
        self.combo_top = CustomComboBox()
        self.combo_top.addItems(["Top 10", "Top 20", "Top 30", "Top 50", "Top 100", "Top 150", "Top 200", "ATC"])
        self.combo_top.setFixedWidth(100)  # Tăng chiều rộng cho combobox top

        # Tạo container cho input_ids và các nút liên quan
        self.id_container = QWidget()
        self.id_layout = QHBoxLayout(self.id_container)
        self.id_layout.setContentsMargins(0, 0, 0, 0)
        self.id_layout.setSpacing(5)

        # Thêm input field ID với khả năng tự nhận diện định dạng
        self.input_ids = IDInputField()
        self.input_ids.setMinimumWidth(350)  # Giảm chiều rộng tối thiểu để nhường chỗ cho nút
        self.input_ids.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)  # Ưu tiên mở rộng theo chiều ngang

        # Tạo label đếm ID
        self.id_count_label = QLabel("0 ID", self)
        self.id_count_label.setStyleSheet("""
            QLabel {
                color: #777;
                background-color: transparent;
                padding: 2px 5px;
                margin: 0;
                border-radius: 3px;
            }
        """)

        # Kết nối IDInputField với label đếm ID
        self.input_ids.set_id_count_label(self.id_count_label)

        # Thanh công cụ cho ID
        self.tool_buttons = QWidget()
        self.tool_layout = QHBoxLayout(self.tool_buttons)
        self.tool_layout.setContentsMargins(0, 0, 0, 0)
        self.tool_layout.setSpacing(2)

        # Nút style chung cho các nút công cụ
        button_style = """
            QPushButton {
                background-color: #414141;
                border: 1px solid #555;
                border-radius: 3px;
                padding: 3px;
                color: #DDD;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
        """

        # Tạo nút để dán trực tiếp từ clipboard
        self.paste_button = QPushButton("📋")
        self.paste_button.setToolTip("Dán từ clipboard")
        self.paste_button.setFixedWidth(30)
        self.paste_button.clicked.connect(self.paste_from_clipboard)
        self.paste_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.paste_button.setStyleSheet(button_style)

        # Nút để loại bỏ ID trùng lặp
        self.dedup_button = QPushButton("🔄")
        self.dedup_button.setToolTip("Loại bỏ ID trùng lặp")
        self.dedup_button.setFixedWidth(30)
        self.dedup_button.clicked.connect(self.remove_duplicate_ids)
        self.dedup_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.dedup_button.setStyleSheet(button_style)

        # Thêm các nút vào thanh công cụ
        self.tool_layout.addWidget(self.paste_button)
        self.tool_layout.addWidget(self.dedup_button)

        # Thêm các phần tử vào id_layout
        self.id_layout.addWidget(self.input_ids, 1)  # Stretch factor = 1
        self.id_layout.addWidget(self.id_count_label)
        self.id_layout.addWidget(self.tool_buttons)

        self.is_group = QCheckBox("Nhóm")
        self.is_group.setToolTip("Đánh dấu nếu các ID này thuộc cùng một thương hiệu và nên được xếp gần nhau")
        self.is_group.setFixedWidth(60)  # Cố định chiều rộng cho checkbox

        # Thay thế combo_time bằng TimeSelectionButton
        self.time_selector = TimeSelectionButton(time_slots)
        self.time_selector.setFixedWidth(150)  # Cố định chiều rộng cho phần chọn khung giờ

        self.remove_button = QPushButton("x")
        self.remove_button.setFixedWidth(25)
        self.remove_button.clicked.connect(remove_callback)

        self.layout.addWidget(self.combo_top)
        self.layout.addWidget(self.id_container, 1)  # Gán stretch factor = 1
        self.layout.addWidget(self.is_group)
        self.layout.addWidget(self.time_selector)
        self.layout.addWidget(self.remove_button)

        self.setLayout(self.layout)

        # Thêm style để thấy rõ ranh giới giữa các dòng
        self.setStyleSheet("""
            QFrame {
                border: none;
                background-color: transparent;
            }
        """)

    def paste_from_clipboard(self):
        """Dán nội dung từ clipboard vào input_ids"""
        clipboard = QApplication.clipboard()
        mime_data = clipboard.mimeData()

        if mime_data.hasText():
            # Lấy text từ clipboard
            text = mime_data.text()
            # Tự động xử lý và định dạng text
            formatted_text = self.input_ids.format_id_text(text)
            # Đặt text đã được xử lý vào input_ids
            self.input_ids.setText(formatted_text)
            # Focus vào input_ids nhưng không select all
            self.input_ids.setFocus()
            self.input_ids.setCursorPosition(len(formatted_text))

    def remove_duplicate_ids(self):
        """Loại bỏ các ID trùng lặp trong input field"""
        removed_count = self.input_ids.remove_duplicates()
        if removed_count > 0:
            QMessageBox.information(self, "Loại bỏ trùng lặp", f"Đã loại bỏ {removed_count} ID trùng lặp")

    def get_values(self):
        return (
            self.combo_top.currentText(),
            self.input_ids.get_id_list(),
            self.time_selector.get_selected_times(),
            self.is_group.isChecked()
        )

    def update_time_slots(self, time_slots):
        self.time_selector.set_time_slots(time_slots)

# Thêm lớp mới cho widget chọn nhiều khung giờ
class PopupTimeSelectionWidget(QWidget):
    timeSelectionChanged = pyqtSignal(list)

    def __init__(self, time_slots=None, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.Popup | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_ShowWithoutActivating)
        self.installEventFilter(self)

        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(5, 5, 5, 5)
        self.layout.setSpacing(2)

        self.checkboxes = []
        self.selected_times = []

        if time_slots:
            for slot in time_slots:
                checkbox = QCheckBox(slot)
                checkbox.stateChanged.connect(self.update_selection)
                self.checkboxes.append(checkbox)
                self.layout.addWidget(checkbox)

        # Button layout
        button_layout = QHBoxLayout()

        self.clear_all_button = QPushButton("Bỏ chọn tất cả")
        self.clear_all_button.clicked.connect(self.clear_all)

        button_layout.addWidget(self.clear_all_button)
        button_layout.addStretch(1)  # Đẩy nút về bên trái

        self.layout.addLayout(button_layout)
        self.setLayout(self.layout)

        # Style
        self.setStyleSheet("""
            QWidget {
                background-color: #2D2D2D;
                color: white;
            }
            QCheckBox {
                padding: 5px;
            }
            QCheckBox:hover {
                background-color: #3D3D3D;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 4px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)

    def update_selection(self):
        self.selected_times = []
        for checkbox in self.checkboxes:
            if checkbox.isChecked():
                self.selected_times.append(checkbox.text())
        # Thêm log để kiểm tra
        print(f"Cập nhật danh sách khung giờ đã chọn: {self.selected_times}")
        self.timeSelectionChanged.emit(self.selected_times)

    def select_all(self):
        for checkbox in self.checkboxes:
            checkbox.setChecked(True)

    def clear_all(self):
        for checkbox in self.checkboxes:
            checkbox.setChecked(False)

    def get_selected_times(self):
        return self.selected_times

    def set_time_slots(self, time_slots):
        # Lưu lại trạng thái đã chọn trước đó
        previous_selected = self.selected_times.copy()

        # Debug: In ra trạng thái đã chọn trước đó
        print(f"Debug - Trạng thái đã chọn trước đó: {previous_selected}")

        # Xóa tất cả checkboxes hiện tại
        for checkbox in self.checkboxes:
            self.layout.removeWidget(checkbox)
            checkbox.deleteLater()

        self.checkboxes = []

        # Thêm checkboxes mới
        for slot in time_slots:
            checkbox = QCheckBox(slot)
            checkbox.stateChanged.connect(self.update_selection)
            # Giữ lại trạng thái đã chọn nếu khung giờ vẫn còn tồn tại
            if slot in previous_selected:
                checkbox.setChecked(True)
                print(f"Debug - Giữ lại trạng thái đã chọn cho khung giờ: {slot}")
            self.checkboxes.append(checkbox)
            self.layout.insertWidget(self.layout.count() - 1, checkbox)  # Thêm trên các nút

        # Cập nhật lại selected_times sau khi tạo lại các checkbox
        self.update_selection()

    def eventFilter(self, watched, event):
        if event.type() == QEvent.Type.MouseButtonPress:
            pos = event.globalPosition().toPoint()
            if not self.geometry().contains(pos):
                self.hide()
                return True
        return super().eventFilter(watched, event)

# Lớp TimeSelectionButton để thay thế ComboBox
class TimeSelectionButton(QPushButton):
    def __init__(self, time_slots=None, parent=None):
        super().__init__("Chọn khung giờ", parent)
        self.popup = PopupTimeSelectionWidget(time_slots, self)
        self.popup.timeSelectionChanged.connect(self.update_text)
        self.clicked.connect(self.show_popup)

        # Style
        self.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 3px;
                border: 1px solid #555;
                border-radius: 3px;
                background-color: #2D2D2D;
            }
            QPushButton:hover {
                background-color: #3D3D3D;
                border: 1px solid #777;
            }
        """)

    def show_popup(self):
        # Hiển thị popup dưới button
        pos = self.mapToGlobal(QPoint(0, self.height()))
        self.popup.move(pos)
        self.popup.setMinimumWidth(self.width())
        self.popup.setMaximumWidth(int(self.width() * 1.5))
        self.popup.resize(self.width(), min(400, 30 * len(self.popup.checkboxes) + 50))
        self.popup.show()

    def set_time_slots(self, time_slots):
        self.popup.set_time_slots(time_slots)
        self.update_text()

    def update_text(self, selected_times=None):
        if selected_times is None:
            selected_times = self.popup.get_selected_times()

        if not selected_times:
            self.setText("Chọn khung giờ")
        elif len(selected_times) == 1:
            self.setText(selected_times[0])
        else:
            self.setText(f"{len(selected_times)} khung giờ đã chọn")

    def get_selected_times(self):
        selected_times = self.popup.get_selected_times()
        # Thêm log để kiểm tra
        print(f"Khung giờ đã chọn: {selected_times}")
        return selected_times

    def set_selected_times(self, selected_times):
        """Thiết lập các khung giờ được chọn"""
        if not selected_times:
            selected_times = []

        # Cập nhật trạng thái checkbox trong popup
        for checkbox in self.popup.checkboxes:
            checkbox.setChecked(checkbox.text() in selected_times)

        # Cập nhật selected_times trong popup
        self.popup.selected_times = selected_times.copy()

        # Cập nhật text hiển thị
        self.update_text(selected_times)


class SettingsFileDialog(QDialog):
    """Dialog để chọn file cài đặt để tải"""

    def __init__(self, available_files, settings_manager, parent=None):
        super().__init__(parent)
        self.available_files = available_files
        self.settings_manager = settings_manager
        self.selected_file = None

        self.setWindowTitle("Tải cài đặt")
        self.setModal(True)
        self.resize(500, 400)

        self.setup_ui()
        self.load_file_list()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Label hướng dẫn
        info_label = QLabel("Chọn file cài đặt để tải:")
        info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # Danh sách file
        self.file_list = QListWidget()
        self.file_list.itemDoubleClicked.connect(self.on_file_double_clicked)
        layout.addWidget(self.file_list)

        # Thông tin file được chọn
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        self.info_text.setPlaceholderText("Chọn file để xem thông tin...")
        layout.addWidget(self.info_text)

        # Nút điều khiển
        button_layout = QHBoxLayout()

        self.delete_button = QPushButton("🗑️ Xóa file")
        self.delete_button.clicked.connect(self.delete_selected_file)
        self.delete_button.setEnabled(False)

        self.load_button = QPushButton("📁 Tải cài đặt")
        self.load_button.clicked.connect(self.accept)
        self.load_button.setEnabled(False)

        self.cancel_button = QPushButton("Hủy")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        button_layout.addWidget(self.load_button)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

        # Kết nối sự kiện
        self.file_list.itemSelectionChanged.connect(self.on_selection_changed)

    def load_file_list(self):
        """Tải danh sách file vào list widget"""
        self.file_list.clear()

        for filename in self.available_files:
            item = QListWidgetItem(filename)

            # Thêm thông tin thời gian
            try:
                file_path = os.path.join(self.settings_manager.settings_dir, filename + '.json')
                if os.path.exists(file_path):
                    mtime = os.path.getmtime(file_path)
                    time_str = datetime.fromtimestamp(mtime).strftime('%d/%m/%Y %H:%M')
                    item.setToolTip(f"Tạo lúc: {time_str}")
            except:
                pass

            self.file_list.addItem(item)

    def on_selection_changed(self):
        """Xử lý khi chọn file khác"""
        current_item = self.file_list.currentItem()
        if current_item:
            filename = current_item.text()
            self.selected_file = filename
            self.load_button.setEnabled(True)
            self.delete_button.setEnabled(True)
            self.show_file_info(filename)
        else:
            self.selected_file = None
            self.load_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            self.info_text.clear()

    def show_file_info(self, filename):
        """Hiển thị thông tin file được chọn"""
        try:
            success, data = self.settings_manager.load_settings(filename)
            if success:
                info_lines = []
                info_lines.append(f"📄 File: {filename}")
                info_lines.append(f"📅 Thời gian: {data.get('timestamp', 'N/A')}")
                info_lines.append(f"🕐 Khung giờ: {len(data.get('time_slots', []))} khung")
                info_lines.append(f"⚙️ Điều kiện: {len(data.get('conditions', []))} điều kiện")

                # Hiển thị danh sách khung giờ
                time_slots = data.get('time_slots', [])
                if time_slots:
                    info_lines.append(f"📋 Danh sách khung giờ: {', '.join(time_slots)}")

                self.info_text.setText('\n'.join(info_lines))
            else:
                self.info_text.setText(f"❌ Lỗi đọc file: {data}")
        except Exception as e:
            self.info_text.setText(f"❌ Lỗi: {e}")

    def on_file_double_clicked(self, item):
        """Xử lý double click để tải file"""
        self.selected_file = item.text()
        self.accept()

    def delete_selected_file(self):
        """Xóa file được chọn"""
        if not self.selected_file:
            return

        reply = QMessageBox.question(
            self,
            "Xác nhận xóa",
            f"Bạn có chắc chắn muốn xóa file cài đặt '{self.selected_file}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            success, message = self.settings_manager.delete_settings(self.selected_file)
            if success:
                QMessageBox.information(self, "Thành công", message)
                # Cập nhật lại danh sách
                self.available_files = self.settings_manager.get_settings_files()
                self.load_file_list()
                self.selected_file = None
                self.load_button.setEnabled(False)
                self.delete_button.setEnabled(False)
                self.info_text.clear()
            else:
                QMessageBox.warning(self, "Lỗi", message)

    def get_selected_file(self):
        """Trả về file được chọn"""
        return self.selected_file


class ProcessingResultsDialog(QDialog):
    """Dialog tổng hợp: xem, chỉnh sửa và upload kết quả xử lý"""

    def __init__(self, parent_app, processed_data):
        super().__init__()
        self.parent_app = parent_app
        self.processed_data = processed_data
        self.current_time_slot = None
        self.current_slot_data = None
        self.current_processor = None

        # Tạo bản copy để edit
        self.current_ids = []

        # Undo/Redo system
        self.history_stack = []
        self.redo_stack = []
        self.max_history = 50

        # Store original data for filtering
        self.original_data = []
        self.filtered_indices = []

        self.setWindowTitle("Processing Results - Edit & Upload")
        self.setModal(False)
        self.resize(1300, 700)  # Optimized width cho layout mới

        # Style cho dark theme
        self.setStyleSheet("""
            QDialog {
                background-color: #222;
                color: #EEE;
            }
            QTableWidget {
                background-color: #333;
                border: 1px solid #555;
                gridline-color: #555;
                selection-background-color: #4A90E2;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #444;
            }
            QTableWidget::item:selected {
                background-color: #4A90E2;
                border: 2px solid #2E7BD6;
                color: white;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #444;
                border: 1px solid #666;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 8px 16px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #555;
            }
            QLineEdit, QSpinBox, QComboBox {
                background-color: #333;
                border: 1px solid #555;
                padding: 5px;
                border-radius: 3px;
            }
            QLabel {
                color: #EEE;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        self.setup_ui()
        self.load_first_time_slot()

        # Save initial state after UI is ready
        if self.current_ids:
            self.save_state("Initial state")

    def setup_ui(self):
        layout = QVBoxLayout()

        # Info label (compact)
        self.info_label = QLabel("")
        self.info_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #4A90E2; margin: 5px;")
        layout.addWidget(self.info_label)

        # Search and Filter controls (compact layout)
        search_group = QGroupBox("🔍 Search & Filter")
        search_layout = QVBoxLayout()

        # First row: Time slot + Search
        first_row = QHBoxLayout()
        first_row.addWidget(QLabel("Khung giờ:"))
        self.time_slot_combo = QComboBox()
        self.time_slot_combo.currentTextChanged.connect(self.on_time_slot_changed)
        self.time_slot_combo.setFixedWidth(120)
        first_row.addWidget(self.time_slot_combo)

        # Populate time slots
        for time_slot in sorted(self.processed_data.keys()):
            self.time_slot_combo.addItem(time_slot)

        first_row.addWidget(QLabel("Search:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Tìm theo Item ID, Shop ID, Brand...")
        self.search_input.textChanged.connect(self.filter_table)
        first_row.addWidget(self.search_input)

        self.clear_search_btn = QPushButton("❌")
        self.clear_search_btn.setFixedSize(25, 25)
        self.clear_search_btn.clicked.connect(self.clear_search)
        first_row.addWidget(self.clear_search_btn)

        search_layout.addLayout(first_row)

        # Second row: Filters + Quick buttons + Upload buttons
        second_row = QHBoxLayout()
        second_row.addWidget(QLabel("Filter:"))

        self.status_filter = QComboBox()
        self.status_filter.addItems(["All Status", "🔒 Exclusive", "⭐ Review", "📦 Base"])
        self.status_filter.currentTextChanged.connect(self.filter_table)
        self.status_filter.setFixedWidth(100)
        second_row.addWidget(self.status_filter)

        self.shop_filter = QComboBox()
        self.shop_filter.addItem("All Shops")
        self.shop_filter.currentTextChanged.connect(self.filter_table)
        self.shop_filter.setFixedWidth(100)
        second_row.addWidget(self.shop_filter)

        self.brand_filter = QComboBox()
        self.brand_filter.addItem("All Brands")
        self.brand_filter.currentTextChanged.connect(self.filter_table)
        self.brand_filter.setFixedWidth(100)
        second_row.addWidget(self.brand_filter)

        # Quick filter buttons (wider for clear text)
        self.quick_exclusive_btn = QPushButton("🔒 Exclusive")
        self.quick_exclusive_btn.setCheckable(True)
        self.quick_exclusive_btn.clicked.connect(self.toggle_exclusive_filter)
        self.quick_exclusive_btn.setFixedWidth(110)  # Tăng từ 90px
        second_row.addWidget(self.quick_exclusive_btn)

        self.quick_review_btn = QPushButton("⭐ Review")
        self.quick_review_btn.setCheckable(True)
        self.quick_review_btn.clicked.connect(self.toggle_review_filter)
        self.quick_review_btn.setFixedWidth(100)  # Tăng từ 80px
        second_row.addWidget(self.quick_review_btn)

        # Upload buttons (wider for clear text)
        self.upload_current_btn = QPushButton("📤 Upload This Slot")
        self.upload_current_btn.clicked.connect(self.upload_current_slot)
        self.upload_current_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: 1px solid #1976D2;
                color: white;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        self.upload_current_btn.setFixedWidth(140)  # Tăng thêm để chứa "Slot"
        second_row.addWidget(self.upload_current_btn)

        self.upload_all_btn = QPushButton("📤 Upload All Slots")
        self.upload_all_btn.clicked.connect(self.upload_all_slots)
        self.upload_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: 1px solid #45a049;
                color: white;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.upload_all_btn.setFixedWidth(125)  # Tăng từ 95px
        second_row.addWidget(self.upload_all_btn)

        second_row.addStretch()

        # Results info
        self.results_label = QLabel("Showing all items")
        self.results_label.setStyleSheet("color: #888; font-style: italic; font-size: 11px;")
        second_row.addWidget(self.results_label)

        search_layout.addLayout(second_row)
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)

        # Position Controls (compact)
        controls_group = QGroupBox("🔄 Position Controls")
        controls_layout = QHBoxLayout()

        controls_layout.addWidget(QLabel("Pos 1:"))
        self.pos1_input = QSpinBox()
        self.pos1_input.setRange(1, 500)
        self.pos1_input.setValue(1)
        self.pos1_input.setFixedWidth(60)
        controls_layout.addWidget(self.pos1_input)

        controls_layout.addWidget(QLabel("⇄"))

        controls_layout.addWidget(QLabel("Pos 2:"))
        self.pos2_input = QSpinBox()
        self.pos2_input.setRange(1, 500)
        self.pos2_input.setValue(2)
        self.pos2_input.setFixedWidth(60)
        controls_layout.addWidget(self.pos2_input)

        self.swap_button = QPushButton("🔄 Swap")
        self.swap_button.clicked.connect(self.swap_positions)
        self.swap_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                border: 1px solid #F57C00;
                color: white;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        self.swap_button.setFixedWidth(100)  # Tăng từ 80px
        controls_layout.addWidget(self.swap_button)

        # Combined Move button (Up/Down toggle) - wider for clear text
        self.move_direction_up = True  # Track current direction
        self.move_btn = QPushButton("⬆️ Move Up")
        self.move_btn.clicked.connect(self.toggle_move)
        self.move_btn.setFixedWidth(120)  # Tăng từ 100px
        controls_layout.addWidget(self.move_btn)

        # Combined Undo/Redo button - wider for clear text
        self.undo_redo_btn = QPushButton("↶ Undo")
        self.undo_redo_btn.clicked.connect(self.toggle_undo_redo)
        self.undo_redo_btn.setEnabled(False)
        self.undo_redo_btn.setFixedWidth(110)  # Tăng từ 90px
        controls_layout.addWidget(self.undo_redo_btn)

        # Combined Batch Move button (Up/Down toggle) - wider for clear text
        self.batch_direction_up = True  # Track batch direction
        self.batch_move_btn = QPushButton("⬆️ Batch Move Up")
        self.batch_move_btn.clicked.connect(self.toggle_batch_move)
        self.batch_move_btn.setFixedWidth(150)  # Tăng từ 130px
        controls_layout.addWidget(self.batch_move_btn)

        controls_layout.addStretch()

        # Selection info (compact)
        self.selection_label = QLabel("No items selected")
        self.selection_label.setStyleSheet("color: #888; font-style: italic; font-size: 11px;")
        controls_layout.addWidget(self.selection_label)

        controls_group.setLayout(controls_layout)
        layout.addWidget(controls_group)

        # Data table
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "Vị trí", "Item ID", "Shop ID", "Brand", "Timeline", "Điều kiện"
        ])

        # Thiết lập độ rộng cột
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        self.table.setColumnWidth(0, 80)
        self.table.setColumnWidth(1, 120)
        self.table.setColumnWidth(2, 100)
        self.table.setColumnWidth(3, 150)
        self.table.setColumnWidth(4, 120)

        # Enable multi-selection và double click
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.table.itemSelectionChanged.connect(self.update_selection_label)

        layout.addWidget(self.table)

        # Bottom buttons (compact)
        button_layout = QHBoxLayout()

        self.reset_button = QPushButton("🔄 Reset")
        self.reset_button.clicked.connect(self.reset_changes)
        self.reset_button.setFixedWidth(100)  # Tăng từ 80px
        button_layout.addWidget(self.reset_button)

        # Additional batch operations (moved here from Position Controls)
        self.group_shop_btn = QPushButton("🏪 Group by Shop")
        self.group_shop_btn.clicked.connect(self.group_selected_by_shop)
        self.group_shop_btn.setFixedWidth(140)  # Wider for clear text
        button_layout.addWidget(self.group_shop_btn)

        self.batch_top_btn = QPushButton("🔝 Move to Top")
        self.batch_top_btn.clicked.connect(self.batch_move_to_top)
        self.batch_top_btn.setFixedWidth(130)  # Wider for clear text
        button_layout.addWidget(self.batch_top_btn)

        button_layout.addStretch()

        self.close_button = QPushButton("❌ Close")
        self.close_button.clicked.connect(self.close)
        self.close_button.setFixedWidth(100)  # Tăng từ 80px
        button_layout.addWidget(self.close_button)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def load_first_time_slot(self):
        """Load khung giờ đầu tiên"""
        if self.processed_data:
            first_slot = sorted(self.processed_data.keys())[0]
            self.time_slot_combo.setCurrentText(first_slot)
            self.on_time_slot_changed(first_slot)

    def on_time_slot_changed(self, time_slot):
        """Xử lý khi thay đổi khung giờ"""
        if not time_slot or time_slot not in self.processed_data:
            return

        self.current_time_slot = time_slot
        self.current_slot_data = self.processed_data[time_slot]
        self.current_processor = self.current_slot_data['processor']

        # Tạo bản copy để edit
        self.current_ids = self.current_processor.current_ids.copy()

        # Reset undo/redo
        self.history_stack = []
        self.redo_stack = []

        # Save initial state (only if UI is ready)
        if hasattr(self, 'undo_btn'):
            self.save_state("Initial state")

        # Update info (only if UI is ready)
        if hasattr(self, 'info_label'):
            self.info_label.setText(f"Khung giờ: {time_slot} | Cột: {self.current_processor.column} | IDs: {len(self.current_ids)}")

        # Update spinbox ranges (only if UI is ready)
        if hasattr(self, 'pos1_input') and hasattr(self, 'pos2_input'):
            self.pos1_input.setRange(1, len(self.current_ids))
            self.pos2_input.setRange(1, len(self.current_ids))

        # Load data (only if UI is ready)
        if hasattr(self, 'table'):
            self.load_data()

    def upload_current_slot(self):
        """Upload khung giờ hiện tại"""
        if not self.current_time_slot:
            QMessageBox.warning(self, "Lỗi", "Chưa chọn khung giờ!")
            return

        reply = QMessageBox.question(
            self, "Confirm Upload",
            f"Upload khung giờ {self.current_time_slot}?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.upload_time_slot(self.current_time_slot)

    def upload_all_slots(self):
        """Upload tất cả khung giờ"""
        reply = QMessageBox.question(
            self, "Confirm Upload All",
            f"Upload tất cả {len(self.processed_data)} khung giờ?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            for time_slot in self.processed_data.keys():
                self.upload_time_slot(time_slot)

            # Tạo sheet Check quyền lợi
            self.create_check_sheet()

            QMessageBox.information(self, "Hoàn thành", "Đã upload tất cả khung giờ!")

    def upload_time_slot(self, time_slot):
        """Upload một khung giờ cụ thể lên Google Sheets"""
        try:
            # Update current slot data nếu đang edit slot này
            if time_slot == self.current_time_slot:
                self.apply_current_changes()

            data = self.processed_data[time_slot]
            processor = data['processor']
            final_column_data = data['final_column_data']
            cell_range = data['cell_range']

            # Get worksheet
            worksheet = self.parent_app.spreadsheet.worksheet(self.parent_app.sheet_combo.currentText())

            # Upload data
            worksheet.update(cell_range, [[val] for val in final_column_data])

            self.parent_app.log(f"✅ Đã upload khung giờ {time_slot} (cột {processor.column})")

        except Exception as e:
            self.parent_app.log(f"❌ Lỗi upload khung giờ {time_slot}: {e}")
            QMessageBox.critical(self, "Lỗi Upload", f"Không thể upload khung giờ {time_slot}:\n{e}")

    def apply_current_changes(self):
        """Apply changes cho khung giờ hiện tại"""
        if self.current_time_slot and self.current_slot_data:
            # Cập nhật processor
            self.current_processor.current_ids = self.current_ids.copy()

            # Cập nhật final_column_data
            max_len = self.current_slot_data['max_len']
            final_column_data = [''] * max_len

            for i, id_val in enumerate(self.current_ids):
                if i < max_len:
                    final_column_data[i] = id_val

            self.current_slot_data['final_column_data'] = final_column_data

    def create_check_sheet(self):
        """Tạo sheet Check quyền lợi cho tất cả khung giờ đã upload"""
        try:
            # Tạo time_slot_processors từ processed_data
            time_slot_processors = {}
            for time_slot, data in self.processed_data.items():
                time_slot_processors[time_slot] = data['processor']

            # Gọi hàm tạo check sheet từ parent app
            self.parent_app.create_check_quyen_loi_sheet(time_slot_processors)

        except Exception as e:
            self.parent_app.log(f"❌ Lỗi tạo sheet Check quyền lợi: {e}")

    # Copy tất cả methods từ EditPositionsDialog
    def load_data(self):
        """Load dữ liệu vào bảng và populate filters"""
        if not self.current_ids:
            return

        # Prepare data for filtering
        self.original_data = []
        shops = set()
        brands = set()

        for i, item_id in enumerate(self.current_ids):
            # Get all data for this item
            shop_id = self.parent_app.deal_list_manager.id_to_shop.get(item_id, "")
            brand = self.parent_app.deal_list_manager.id_to_brand.get(item_id, "")
            timeline = self.parent_app.deal_list_manager.id_to_timeline.get(item_id, "")

            # Xác định điều kiện xử lý cho ID này
            condition_text = self.get_condition_for_id(item_id)

            # Store data
            row_data = {
                'position': i + 1,
                'item_id': str(item_id),
                'shop_id': str(shop_id) if shop_id else "",
                'brand': str(brand),
                'timeline': str(timeline) if timeline else "",
                'status': condition_text,
                'original_index': i
            }
            self.original_data.append(row_data)

            # Collect unique values for filters
            if shop_id:
                shops.add(str(shop_id))
            if brand:
                brands.add(str(brand))

        # Populate filter dropdowns
        self.populate_filters(shops, brands)

        # Show all data initially and apply any existing filters
        self.filter_table()

    def get_condition_for_id(self, item_id):
        """Xác định điều kiện xử lý cho ID này"""
        if not self.current_processor:
            return ""

        # Kiểm tra trong các conditions của processor
        for i, condition in enumerate(self.current_processor.conditions):
            if item_id in condition['ids']:
                if condition.get('is_atc', False):
                    return "🎯 ATC"
                else:
                    top_n = condition.get('top_n', 0)
                    if condition.get('is_group', False):
                        # Hiển thị Group với số thứ tự để phân biệt
                        group_id = chr(65 + i)  # A, B, C, D...
                        return f"📦 Top {top_n} (G{group_id})"
                    else:
                        return f"🔝 Top {top_n}"

        # Kiểm tra exclusive
        if item_id in self.current_processor.exclusive_ids:
            return "🔒 Exclusive"

        # Không hiển thị Base - trả về empty string
        return ""

    def populate_filters(self, shops, brands):
        """Populate filter dropdowns with unique values"""
        # Only populate if UI elements exist
        if not hasattr(self, 'shop_filter') or not hasattr(self, 'brand_filter'):
            return

        # Shop filter
        current_shop = self.shop_filter.currentText()
        self.shop_filter.clear()
        self.shop_filter.addItem("All Shops")
        for shop in sorted(shops):
            self.shop_filter.addItem(shop)

        # Restore selection if possible
        shop_index = self.shop_filter.findText(current_shop)
        if shop_index >= 0:
            self.shop_filter.setCurrentIndex(shop_index)

        # Brand filter
        current_brand = self.brand_filter.currentText()
        self.brand_filter.clear()
        self.brand_filter.addItem("All Brands")
        for brand in sorted(brands):
            self.brand_filter.addItem(brand)

        # Restore selection if possible
        brand_index = self.brand_filter.findText(current_brand)
        if brand_index >= 0:
            self.brand_filter.setCurrentIndex(brand_index)

    def update_table_display(self):
        """Update table with filtered data"""
        self.table.setRowCount(len(self.filtered_indices))

        for display_row, original_index in enumerate(self.filtered_indices):
            row_data = self.original_data[original_index]

            # Vị trí (1-based)
            pos_item = QTableWidgetItem(str(row_data['position']))
            pos_item.setFlags(pos_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(display_row, 0, pos_item)

            # Item ID
            id_item = QTableWidgetItem(row_data['item_id'])
            id_item.setFlags(id_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(display_row, 1, id_item)

            # Shop ID
            shop_item = QTableWidgetItem(row_data['shop_id'])
            shop_item.setFlags(shop_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(display_row, 2, shop_item)

            # Brand
            brand_item = QTableWidgetItem(row_data['brand'])
            brand_item.setFlags(brand_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(display_row, 3, brand_item)

            # Timeline
            timeline_item = QTableWidgetItem(row_data['timeline'])
            timeline_item.setFlags(timeline_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.table.setItem(display_row, 4, timeline_item)

            # Trạng thái với styling cho groups
            status_item = QTableWidgetItem(row_data['status'])
            status_item.setFlags(status_item.flags() & ~Qt.ItemFlag.ItemIsEditable)

            # Thêm màu sắc cho các group khác nhau
            if "G" in row_data['status']:  # Nếu là group
                group_id = row_data['status'][-2:]  # Lấy GA, GB, GC...
                if group_id == "GA":
                    status_item.setBackground(QColor(255, 182, 193))  # Light pink
                elif group_id == "GB":
                    status_item.setBackground(QColor(173, 216, 230))  # Light blue
                elif group_id == "GC":
                    status_item.setBackground(QColor(144, 238, 144))  # Light green
                elif group_id == "GD":
                    status_item.setBackground(QColor(255, 218, 185))  # Peach
                elif group_id == "GE":
                    status_item.setBackground(QColor(221, 160, 221))  # Plum
                else:
                    status_item.setBackground(QColor(211, 211, 211))  # Light gray
                status_item.setForeground(QColor(0, 0, 0))  # Black text for readability

            self.table.setItem(display_row, 5, status_item)

        # Update results label
        total_items = len(self.original_data)
        filtered_items = len(self.filtered_indices)
        if filtered_items == total_items:
            self.results_label.setText(f"Showing all {total_items} items")
        else:
            self.results_label.setText(f"Showing {filtered_items} of {total_items} items")

    # Thêm các methods cơ bản để dialog hoạt động
    def filter_table(self):
        """Apply all filters to the table"""
        if not self.original_data:
            return

        search_text = self.search_input.text().lower().strip()
        status_filter = self.status_filter.currentText()
        shop_filter = self.shop_filter.currentText()
        brand_filter = self.brand_filter.currentText()

        # Apply quick filters
        exclusive_only = self.quick_exclusive_btn.isChecked()
        review_only = self.quick_review_btn.isChecked()

        self.filtered_indices = []

        for i, row_data in enumerate(self.original_data):
            # Search filter
            if search_text:
                searchable_text = f"{row_data['item_id']} {row_data['shop_id']} {row_data['brand']}".lower()
                if search_text not in searchable_text:
                    continue

            # Status filter
            if status_filter != "All Status" and row_data['status'] != status_filter:
                continue

            # Shop filter
            if shop_filter != "All Shops" and row_data['shop_id'] != shop_filter:
                continue

            # Brand filter
            if brand_filter != "All Brands" and row_data['brand'] != brand_filter:
                continue

            # Quick filters
            if exclusive_only and row_data['status'] != "🔒 Exclusive":
                continue

            if review_only and row_data['status'] != "⭐ Review":
                continue

            # If all filters pass, include this row
            self.filtered_indices.append(i)

        # Update table display
        self.update_table_display()

    def clear_search(self):
        """Clear search and reset filters"""
        self.search_input.clear()
        self.status_filter.setCurrentIndex(0)
        self.shop_filter.setCurrentIndex(0)
        self.brand_filter.setCurrentIndex(0)
        self.quick_exclusive_btn.setChecked(False)
        self.quick_review_btn.setChecked(False)
        self.filter_table()

    def toggle_exclusive_filter(self):
        """Toggle exclusive filter"""
        if self.quick_exclusive_btn.isChecked():
            self.quick_review_btn.setChecked(False)
        self.filter_table()

    def toggle_review_filter(self):
        """Toggle review filter"""
        if self.quick_review_btn.isChecked():
            self.quick_exclusive_btn.setChecked(False)
        self.filter_table()

    def save_state(self, action_description):
        """Save current state to history for undo/redo"""
        if not self.current_ids:
            return

        state = {
            'current_ids': self.current_ids.copy(),
            'action': action_description,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }

        # Add to history stack
        self.history_stack.append(state)

        # Limit history size
        if len(self.history_stack) > self.max_history:
            self.history_stack.pop(0)

        # Clear redo stack when new action is performed
        self.redo_stack.clear()

        # Update button states
        self.update_undo_redo_buttons()

    def update_undo_redo_buttons(self):
        """Update undo/redo button states"""
        # Handle combined undo/redo button
        if hasattr(self, 'undo_redo_btn'):
            can_undo = len(self.history_stack) > 1
            can_redo = len(self.redo_stack) > 0

            # Enable if either undo or redo is available
            self.undo_redo_btn.setEnabled(can_undo or can_redo)

            # Set text based on priority (undo has priority)
            if can_undo:
                self.undo_redo_btn.setText("↶ Undo")
            elif can_redo:
                self.undo_redo_btn.setText("↷ Redo")
            else:
                self.undo_redo_btn.setText("↶ Undo")

        # Handle separate buttons (for backward compatibility)
        elif hasattr(self, 'undo_btn') and hasattr(self, 'redo_btn'):
            # Undo available if we have more than 1 state in history
            self.undo_btn.setEnabled(len(self.history_stack) > 1)

            # Redo available if we have states in redo stack
            self.redo_btn.setEnabled(len(self.redo_stack) > 0)

    def undo_action(self):
        """Undo last action"""
        if len(self.history_stack) < 2:
            return

        # Move current state to redo stack
        current_state = self.history_stack.pop()
        self.redo_stack.append(current_state)

        # Restore previous state
        previous_state = self.history_stack[-1]
        self.current_ids = previous_state['current_ids'].copy()

        # Reload data and update display
        self.load_data()
        self.update_undo_redo_buttons()

    def redo_action(self):
        """Redo last undone action"""
        if not self.redo_stack:
            return

        # Move state from redo stack back to history
        state_to_redo = self.redo_stack.pop()
        self.history_stack.append(state_to_redo)

        # Restore state
        self.current_ids = state_to_redo['current_ids'].copy()

        # Reload data and update display
        self.load_data()
        self.update_undo_redo_buttons()

    def swap_positions(self):
        """Thực hiện true swap giữa 2 vị trí"""
        if not self.current_ids:
            return

        pos1 = self.pos1_input.value() - 1  # Convert to 0-based
        pos2 = self.pos2_input.value() - 1  # Convert to 0-based

        if pos1 == pos2:
            QMessageBox.warning(self, "Lỗi", "Hai vị trí phải khác nhau!")
            return

        if pos1 < 0 or pos1 >= len(self.current_ids) or pos2 < 0 or pos2 >= len(self.current_ids):
            QMessageBox.warning(self, "Lỗi", "Vị trí không hợp lệ!")
            return

        # True swap: hoán đổi hoàn toàn 2 ID
        id1 = self.current_ids[pos1]
        id2 = self.current_ids[pos2]
        self.current_ids[pos1], self.current_ids[pos2] = self.current_ids[pos2], self.current_ids[pos1]

        # Save state for undo
        self.save_state(f"Swap {id1} (pos {pos1+1}) ⇄ {id2} (pos {pos2+1})")

        # Cập nhật lại bảng
        self.load_data()

        # Auto increment positions for next swap
        if self.pos2_input.value() < len(self.current_ids):
            self.pos1_input.setValue(self.pos2_input.value())
            self.pos2_input.setValue(self.pos2_input.value() + 1)

    def toggle_move(self):
        """Toggle between move up and move down, then execute"""
        if self.move_direction_up:
            self.move_up()
            # Switch to down for next click
            self.move_direction_up = False
            self.move_btn.setText("⬇️ Move Down")
        else:
            self.move_down()
            # Switch to up for next click
            self.move_direction_up = True
            self.move_btn.setText("⬆️ Move Up")

    def move_up(self):
        """Di chuyển ID được chọn lên 1 vị trí"""
        current_row = self.table.currentRow()
        if current_row <= 0 or not self.current_ids:
            return

        # Get original index
        if current_row < len(self.filtered_indices):
            original_index = self.filtered_indices[current_row]
            if original_index > 0:
                # Swap with item above
                id_moved = self.current_ids[original_index]
                self.current_ids[original_index], self.current_ids[original_index - 1] = \
                    self.current_ids[original_index - 1], self.current_ids[original_index]

                # Save state for undo
                self.save_state(f"Move up {id_moved} from pos {original_index+1} to {original_index}")

                # Reload data
                self.load_data()

    def move_down(self):
        """Di chuyển ID được chọn xuống 1 vị trí"""
        current_row = self.table.currentRow()
        if current_row < 0 or not self.current_ids:
            return

        # Get original index
        if current_row < len(self.filtered_indices):
            original_index = self.filtered_indices[current_row]
            if original_index < len(self.current_ids) - 1:
                # Swap with item below
                id_moved = self.current_ids[original_index]
                self.current_ids[original_index], self.current_ids[original_index + 1] = \
                    self.current_ids[original_index + 1], self.current_ids[original_index]

                # Save state for undo
                self.save_state(f"Move down {id_moved} from pos {original_index+1} to {original_index+2}")

                # Reload data
                self.load_data()

    def toggle_undo_redo(self):
        """Smart toggle between undo and redo based on availability"""
        if len(self.history_stack) > 1:  # Can undo
            self.undo_action()
            # Update button to show redo if available
            if len(self.redo_stack) > 0:
                self.undo_redo_btn.setText("↷ Redo")
        elif len(self.redo_stack) > 0:  # Can redo
            self.redo_action()
            # Update button to show undo if available
            if len(self.history_stack) > 1:
                self.undo_redo_btn.setText("↶ Undo")

    def toggle_batch_move(self):
        """Toggle between batch move up and batch move down, then execute"""
        if self.batch_direction_up:
            self.batch_move_up()
            # Switch to down for next click
            self.batch_direction_up = False
            self.batch_move_btn.setText("⬇️ Batch Move Down")
        else:
            self.batch_move_down()
            # Switch to up for next click
            self.batch_direction_up = True
            self.batch_move_btn.setText("⬆️ Batch Move Up")

    def reset_changes(self):
        """Reset về trạng thái ban đầu"""
        if not self.current_processor:
            return

        reply = QMessageBox.question(
            self, "Confirm Reset",
            "Bạn có chắc muốn reset tất cả thay đổi?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Reset về dữ liệu gốc
            self.current_ids = self.current_processor.current_ids.copy()

            # Reset undo/redo
            self.history_stack = []
            self.redo_stack = []
            self.save_state("Reset to original")

            # Reload data
            self.load_data()

    # Placeholder methods cho batch operations
    def on_item_double_clicked(self, item):
        """Xử lý double click để set position 1"""
        display_row = item.row()
        if display_row < len(self.filtered_indices):
            original_index = self.filtered_indices[display_row]
            original_position = self.original_data[original_index]['position']
            self.pos1_input.setValue(original_position)
            self.table.selectRow(display_row)

    def update_selection_label(self):
        """Update selection label with selected items info"""
        selected_rows = self.table.selectionModel().selectedRows()
        count = len(selected_rows)

        if count == 0:
            self.selection_label.setText("No items selected")
        elif count == 1:
            self.selection_label.setText("1 item selected")
        else:
            self.selection_label.setText(f"{count} items selected")

    # Batch operations implementation
    def get_selected_original_indices(self):
        """Get original indices of selected items"""
        selected_rows = self.table.selectionModel().selectedRows()
        original_indices = []

        for index in selected_rows:
            display_row = index.row()
            if display_row < len(self.filtered_indices):
                original_index = self.filtered_indices[display_row]
                original_indices.append(original_index)

        return sorted(original_indices)

    def batch_move_up(self):
        """Move all selected items up by 1 position"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        moved_items = []
        # Process from top to bottom to avoid index conflicts
        for original_index in original_indices:
            if original_index > 0:  # Can move up
                id_moved = self.current_ids[original_index]
                # Swap with item above
                self.current_ids[original_index], self.current_ids[original_index - 1] = \
                    self.current_ids[original_index - 1], self.current_ids[original_index]
                moved_items.append(id_moved)

        if moved_items:
            self.save_state(f"Batch move up {len(moved_items)} items")
            self.load_data()

    def batch_move_down(self):
        """Move all selected items down by 1 position"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        moved_items = []
        # Process from bottom to top to avoid index conflicts
        for original_index in reversed(original_indices):
            if original_index < len(self.current_ids) - 1:  # Can move down
                id_moved = self.current_ids[original_index]
                # Swap with item below
                self.current_ids[original_index], self.current_ids[original_index + 1] = \
                    self.current_ids[original_index + 1], self.current_ids[original_index]
                moved_items.append(id_moved)

        if moved_items:
            self.save_state(f"Batch move down {len(moved_items)} items")
            self.load_data()

    def batch_move_to_top(self):
        """Move selected items to top of list"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        # Get selected items
        selected_items = [self.current_ids[i] for i in original_indices]

        # Remove selected items from current positions
        for index in reversed(original_indices):
            self.current_ids.pop(index)

        # Insert at top
        for i, item in enumerate(selected_items):
            self.current_ids.insert(i, item)

        self.save_state(f"Batch move to top {len(selected_items)} items")
        self.load_data()

    def group_selected_by_shop(self):
        """Group selected items by shop"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        # Get selected items with their shop info
        selected_items = []
        for index in original_indices:
            item_id = self.current_ids[index]
            shop_id = self.parent_app.deal_list_manager.id_to_shop.get(item_id, "")
            selected_items.append((item_id, shop_id))

        # Sort by shop ID
        selected_items.sort(key=lambda x: x[1])

        # Remove selected items from current positions
        for index in reversed(original_indices):
            self.current_ids.pop(index)

        # Insert grouped items at the first selected position
        insert_pos = min(original_indices)
        for i, (item_id, shop_id) in enumerate(selected_items):
            self.current_ids.insert(insert_pos + i, item_id)

        self.save_state(f"Group {len(selected_items)} items by shop")
        self.load_data()


class EditPositionsDialog(QDialog):
    """Dialog để chỉnh sửa vị trí ID với true swap functionality"""

    def __init__(self, parent_window, time_slot, slot_data):
        super().__init__()
        self.parent_window = parent_window
        self.time_slot = time_slot
        self.slot_data = slot_data
        self.processor = slot_data['processor']

        # Tạo bản copy để edit (không ảnh hưởng dữ liệu gốc cho đến khi Accept)
        self.current_ids = self.processor.current_ids.copy()

        self.setWindowTitle(f"Edit Positions - {time_slot}")
        self.setModal(True)
        self.resize(1000, 700)

        # Style cho dark theme
        self.setStyleSheet("""
            QDialog {
                background-color: #222;
                color: #EEE;
            }
            QTableWidget {
                background-color: #333;
                border: 1px solid #555;
                gridline-color: #555;
                selection-background-color: #4A90E2;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #444;
            }
            QTableWidget::item:selected {
                background-color: #4A90E2;
                border: 2px solid #2E7BD6;
                color: white;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #444;
                border: 1px solid #666;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 8px 16px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #555;
            }
            QLineEdit, QSpinBox {
                background-color: #333;
                border: 1px solid #555;
                padding: 5px;
                border-radius: 3px;
            }
            QLabel {
                color: #EEE;
            }
        """)

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Header với thông tin
        header_layout = QHBoxLayout()
        self.info_label = QLabel(f"Chỉnh sửa vị trí cho khung giờ: {self.time_slot}")
        self.info_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #4A90E2;")
        header_layout.addWidget(self.info_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Search and Filter controls
        search_group = QGroupBox("🔍 Search & Filter")
        search_layout = QVBoxLayout()

        # Search row
        search_row = QHBoxLayout()
        search_row.addWidget(QLabel("Search:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Tìm theo Item ID, Shop ID, Brand...")
        self.search_input.textChanged.connect(self.filter_table)
        search_row.addWidget(self.search_input)

        self.clear_search_btn = QPushButton("❌")
        self.clear_search_btn.setFixedSize(30, 30)
        self.clear_search_btn.clicked.connect(self.clear_search)
        self.clear_search_btn.setToolTip("Clear search")
        search_row.addWidget(self.clear_search_btn)

        search_layout.addLayout(search_row)

        # Filter row
        filter_row = QHBoxLayout()
        filter_row.addWidget(QLabel("Filter:"))

        self.status_filter = QComboBox()
        self.status_filter.addItems(["All Status", "🔒 Exclusive", "⭐ Review", "📦 Base"])
        self.status_filter.currentTextChanged.connect(self.filter_table)
        filter_row.addWidget(self.status_filter)

        self.shop_filter = QComboBox()
        self.shop_filter.addItem("All Shops")
        self.shop_filter.currentTextChanged.connect(self.filter_table)
        filter_row.addWidget(self.shop_filter)

        self.brand_filter = QComboBox()
        self.brand_filter.addItem("All Brands")
        self.brand_filter.currentTextChanged.connect(self.filter_table)
        filter_row.addWidget(self.brand_filter)

        # Quick filter buttons
        self.quick_exclusive_btn = QPushButton("🔒 Show Exclusive")
        self.quick_exclusive_btn.setCheckable(True)
        self.quick_exclusive_btn.clicked.connect(self.toggle_exclusive_filter)
        filter_row.addWidget(self.quick_exclusive_btn)

        self.quick_review_btn = QPushButton("⭐ Show Review")
        self.quick_review_btn.setCheckable(True)
        self.quick_review_btn.clicked.connect(self.toggle_review_filter)
        filter_row.addWidget(self.quick_review_btn)

        filter_row.addStretch()

        # Results info
        self.results_label = QLabel("Showing all items")
        self.results_label.setStyleSheet("color: #888; font-style: italic;")
        filter_row.addWidget(self.results_label)

        search_layout.addLayout(filter_row)
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)

        # Swap controls
        swap_group = QGroupBox("Swap Positions (True Swap - hoán đổi hoàn toàn)")
        swap_layout = QHBoxLayout()

        swap_layout.addWidget(QLabel("Vị trí 1:"))
        self.pos1_input = QSpinBox()
        self.pos1_input.setRange(1, len(self.current_ids))
        self.pos1_input.setValue(1)
        swap_layout.addWidget(self.pos1_input)

        swap_layout.addWidget(QLabel("⇄"))

        swap_layout.addWidget(QLabel("Vị trí 2:"))
        self.pos2_input = QSpinBox()
        self.pos2_input.setRange(1, len(self.current_ids))
        self.pos2_input.setValue(2)
        swap_layout.addWidget(self.pos2_input)

        self.swap_button = QPushButton("🔄 Swap Positions")
        self.swap_button.clicked.connect(self.swap_positions)
        self.swap_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                border: 1px solid #F57C00;
                color: white;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        swap_layout.addWidget(self.swap_button)

        swap_layout.addStretch()

        # Quick swap buttons
        self.quick_up_btn = QPushButton("⬆️ Move Up")
        self.quick_up_btn.clicked.connect(self.move_up)
        self.quick_up_btn.setToolTip("Di chuyển ID được chọn lên 1 vị trí")
        swap_layout.addWidget(self.quick_up_btn)

        self.quick_down_btn = QPushButton("⬇️ Move Down")
        self.quick_down_btn.clicked.connect(self.move_down)
        self.quick_down_btn.setToolTip("Di chuyển ID được chọn xuống 1 vị trí")
        swap_layout.addWidget(self.quick_down_btn)

        # Undo/Redo buttons
        self.undo_btn = QPushButton("↶ Undo")
        self.undo_btn.clicked.connect(self.undo_action)
        self.undo_btn.setEnabled(False)
        self.undo_btn.setToolTip("Hoàn tác thao tác cuối (Ctrl+Z)")
        swap_layout.addWidget(self.undo_btn)

        self.redo_btn = QPushButton("↷ Redo")
        self.redo_btn.clicked.connect(self.redo_action)
        self.redo_btn.setEnabled(False)
        self.redo_btn.setToolTip("Làm lại thao tác (Ctrl+Y)")
        swap_layout.addWidget(self.redo_btn)

        # Quick navigation
        self.quick_nav_label = QLabel("Quick: Double-click để chọn, Arrow buttons để di chuyển")
        self.quick_nav_label.setStyleSheet("color: #888; font-style: italic;")
        swap_layout.addWidget(self.quick_nav_label)

        swap_group.setLayout(swap_layout)
        layout.addWidget(swap_group)

        # Batch Operations group
        batch_group = QGroupBox("📦 Batch Operations (Multi-select với Ctrl/Shift)")
        batch_layout = QHBoxLayout()

        self.batch_move_up_btn = QPushButton("⬆️ Move Selected Up")
        self.batch_move_up_btn.clicked.connect(self.batch_move_up)
        self.batch_move_up_btn.setToolTip("Di chuyển tất cả IDs được chọn lên 1 vị trí")
        batch_layout.addWidget(self.batch_move_up_btn)

        self.batch_move_down_btn = QPushButton("⬇️ Move Selected Down")
        self.batch_move_down_btn.clicked.connect(self.batch_move_down)
        self.batch_move_down_btn.setToolTip("Di chuyển tất cả IDs được chọn xuống 1 vị trí")
        batch_layout.addWidget(self.batch_move_down_btn)

        self.group_by_shop_btn = QPushButton("🏪 Group by Shop")
        self.group_by_shop_btn.clicked.connect(self.group_selected_by_shop)
        self.group_by_shop_btn.setToolTip("Nhóm IDs được chọn theo Shop")
        batch_layout.addWidget(self.group_by_shop_btn)

        self.batch_to_top_btn = QPushButton("🔝 Move to Top")
        self.batch_to_top_btn.clicked.connect(self.batch_move_to_top)
        self.batch_to_top_btn.setToolTip("Di chuyển IDs được chọn lên đầu")
        batch_layout.addWidget(self.batch_to_top_btn)

        batch_layout.addStretch()

        # Selection info
        self.selection_label = QLabel("No items selected")
        self.selection_label.setStyleSheet("color: #888; font-style: italic;")
        batch_layout.addWidget(self.selection_label)

        batch_group.setLayout(batch_layout)
        layout.addWidget(batch_group)

        # Bảng hiển thị dữ liệu
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "Vị trí", "Item ID", "Shop ID", "Brand", "Timeline", "Điều kiện"
        ])

        # Thiết lập độ rộng cột
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        self.table.setColumnWidth(0, 80)   # Vị trí
        self.table.setColumnWidth(1, 120)  # Item ID
        self.table.setColumnWidth(2, 100)  # Shop ID
        self.table.setColumnWidth(3, 150)  # Brand
        self.table.setColumnWidth(4, 120)  # Timeline

        # Enable double click để set position 1
        self.table.itemDoubleClicked.connect(self.on_item_double_clicked)

        # Enable multi-selection
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        # Connect selection change to update label
        self.table.itemSelectionChanged.connect(self.update_selection_label)

        layout.addWidget(self.table)

        # Buttons
        button_layout = QHBoxLayout()

        self.reset_button = QPushButton("🔄 Reset Changes")
        self.reset_button.clicked.connect(self.reset_changes)

        self.cancel_button = QPushButton("❌ Cancel")
        self.cancel_button.clicked.connect(self.reject)

        self.apply_button = QPushButton("✅ Apply Changes")
        self.apply_button.clicked.connect(self.accept)
        self.apply_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: 1px solid #45a049;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.apply_button)

        layout.addLayout(button_layout)

        self.setLayout(layout)

        # Store original data for filtering
        self.original_data = []
        self.filtered_indices = []  # Indices of visible rows in original data

        # Undo/Redo system
        self.history_stack = []  # Stack of states for undo
        self.redo_stack = []     # Stack of states for redo
        self.max_history = 50    # Maximum history entries

        # Save initial state
        self.save_state("Initial state")

    def load_data(self):
        """Load dữ liệu vào bảng và populate filters"""
        # Prepare data for filtering
        self.original_data = []
        shops = set()
        brands = set()

        for i, item_id in enumerate(self.current_ids):
            # Get all data for this item
            shop_id = self.parent_window.parent_app.deal_list_manager.id_to_shop.get(item_id, "")
            # Sử dụng brand thật từ cột U thay vì cluster
            brand = self.parent_window.parent_app.deal_list_manager.id_to_brand.get(item_id, "")
            timeline = self.parent_window.parent_app.deal_list_manager.id_to_timeline.get(item_id, "")

            # Xác định điều kiện xử lý cho ID này
            condition_text = self.get_condition_for_id(item_id)

            # Store data
            row_data = {
                'position': i + 1,
                'item_id': str(item_id),
                'shop_id': str(shop_id) if shop_id else "",
                'brand': str(brand),
                'timeline': str(timeline) if timeline else "",
                'status': condition_text,
                'original_index': i
            }
            self.original_data.append(row_data)

            # Collect unique values for filters
            if shop_id:
                shops.add(str(shop_id))
            if brand:
                brands.add(str(brand))

        # Populate filter dropdowns
        self.populate_filters(shops, brands)

        # Show all data initially and apply any existing filters
        self.filter_table()

    def get_condition_for_id(self, item_id):
        """Xác định điều kiện xử lý cho ID này"""
        # Kiểm tra trong các conditions của processor
        for i, condition in enumerate(self.processor.conditions):
            if item_id in condition['ids']:
                if condition.get('is_atc', False):
                    return "🎯 ATC"
                else:
                    top_n = condition.get('top_n', 0)
                    if condition.get('is_group', False):
                        # Hiển thị Group với số thứ tự để phân biệt
                        group_id = chr(65 + i)  # A, B, C, D...
                        return f"📦 Top {top_n} (G{group_id})"
                    else:
                        return f"🔝 Top {top_n}"

        # Kiểm tra exclusive
        if item_id in self.processor.exclusive_ids:
            return "🔒 Exclusive"

        # Không hiển thị Base - trả về empty string
        return ""

    def populate_filters(self, shops, brands):
        """Populate filter dropdowns with unique values"""
        # Shop filter
        current_shop = self.shop_filter.currentText()
        self.shop_filter.clear()
        self.shop_filter.addItem("All Shops")
        for shop in sorted(shops):
            self.shop_filter.addItem(shop)

        # Restore selection if possible
        shop_index = self.shop_filter.findText(current_shop)
        if shop_index >= 0:
            self.shop_filter.setCurrentIndex(shop_index)

        # Brand filter
        current_brand = self.brand_filter.currentText()
        self.brand_filter.clear()
        self.brand_filter.addItem("All Brands")
        for brand in sorted(brands):
            self.brand_filter.addItem(brand)

        # Restore selection if possible
        brand_index = self.brand_filter.findText(current_brand)
        if brand_index >= 0:
            self.brand_filter.setCurrentIndex(brand_index)

    def update_table_display(self):
        """Update table with filtered data"""
        self.table.setRowCount(len(self.filtered_indices))

        for display_row, original_index in enumerate(self.filtered_indices):
            row_data = self.original_data[original_index]

            # Vị trí (1-based)
            pos_item = QTableWidgetItem(str(row_data['position']))
            pos_item.setFlags(pos_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            self.table.setItem(display_row, 0, pos_item)

            # Item ID
            id_item = QTableWidgetItem(row_data['item_id'])
            id_item.setFlags(id_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            self.table.setItem(display_row, 1, id_item)

            # Shop ID
            shop_item = QTableWidgetItem(row_data['shop_id'])
            shop_item.setFlags(shop_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            self.table.setItem(display_row, 2, shop_item)

            # Brand
            brand_item = QTableWidgetItem(row_data['brand'])
            brand_item.setFlags(brand_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            self.table.setItem(display_row, 3, brand_item)

            # Timeline
            timeline_item = QTableWidgetItem(row_data['timeline'])
            timeline_item.setFlags(timeline_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only
            self.table.setItem(display_row, 4, timeline_item)

            # Trạng thái với styling cho groups
            status_item = QTableWidgetItem(row_data['status'])
            status_item.setFlags(status_item.flags() & ~Qt.ItemFlag.ItemIsEditable)  # Read-only

            # Thêm màu sắc cho các group khác nhau
            if "G" in row_data['status']:  # Nếu là group
                group_id = row_data['status'][-2:]  # Lấy GA, GB, GC...
                if group_id == "GA":
                    status_item.setBackground(QColor(255, 182, 193))  # Light pink
                elif group_id == "GB":
                    status_item.setBackground(QColor(173, 216, 230))  # Light blue
                elif group_id == "GC":
                    status_item.setBackground(QColor(144, 238, 144))  # Light green
                elif group_id == "GD":
                    status_item.setBackground(QColor(255, 218, 185))  # Peach
                elif group_id == "GE":
                    status_item.setBackground(QColor(221, 160, 221))  # Plum
                else:
                    status_item.setBackground(QColor(211, 211, 211))  # Light gray
                status_item.setForeground(QColor(0, 0, 0))  # Black text for readability

            self.table.setItem(display_row, 5, status_item)

        # Update results label
        total_items = len(self.original_data)
        filtered_items = len(self.filtered_indices)
        if filtered_items == total_items:
            self.results_label.setText(f"Showing all {total_items} items")
        else:
            self.results_label.setText(f"Showing {filtered_items} of {total_items} items")

    def filter_table(self):
        """Apply all filters to the table"""
        search_text = self.search_input.text().lower().strip()
        status_filter = self.status_filter.currentText()
        shop_filter = self.shop_filter.currentText()
        brand_filter = self.brand_filter.currentText()

        # Apply quick filters
        exclusive_only = self.quick_exclusive_btn.isChecked()
        review_only = self.quick_review_btn.isChecked()

        self.filtered_indices = []

        for i, row_data in enumerate(self.original_data):
            # Search filter
            if search_text:
                searchable_text = f"{row_data['item_id']} {row_data['shop_id']} {row_data['brand']}".lower()
                if search_text not in searchable_text:
                    continue

            # Status filter
            if status_filter != "All Status" and row_data['status'] != status_filter:
                continue

            # Shop filter
            if shop_filter != "All Shops" and row_data['shop_id'] != shop_filter:
                continue

            # Brand filter
            if brand_filter != "All Brands" and row_data['brand'] != brand_filter:
                continue

            # Quick filters
            if exclusive_only and row_data['status'] != "🔒 Exclusive":
                continue

            if review_only and row_data['status'] != "⭐ Review":
                continue

            # If all filters pass, include this row
            self.filtered_indices.append(i)

        # Update table display
        self.update_table_display()

    def clear_search(self):
        """Clear search and reset filters"""
        self.search_input.clear()
        self.status_filter.setCurrentIndex(0)
        self.shop_filter.setCurrentIndex(0)
        self.brand_filter.setCurrentIndex(0)
        self.quick_exclusive_btn.setChecked(False)
        self.quick_review_btn.setChecked(False)
        self.filter_table()

    def toggle_exclusive_filter(self):
        """Toggle exclusive filter"""
        if self.quick_exclusive_btn.isChecked():
            self.quick_review_btn.setChecked(False)  # Mutual exclusive
        self.filter_table()

    def toggle_review_filter(self):
        """Toggle review filter"""
        if self.quick_review_btn.isChecked():
            self.quick_exclusive_btn.setChecked(False)  # Mutual exclusive
        self.filter_table()

    def save_state(self, action_description):
        """Save current state to history for undo/redo"""
        state = {
            'current_ids': self.current_ids.copy(),
            'action': action_description,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }

        # Add to history stack
        self.history_stack.append(state)

        # Limit history size
        if len(self.history_stack) > self.max_history:
            self.history_stack.pop(0)

        # Clear redo stack when new action is performed
        self.redo_stack.clear()

        # Update button states
        self.update_undo_redo_buttons()

    def undo_action(self):
        """Undo last action"""
        if len(self.history_stack) < 2:  # Need at least 2 states (current + previous)
            return

        # Move current state to redo stack
        current_state = self.history_stack.pop()
        self.redo_stack.append(current_state)

        # Restore previous state
        previous_state = self.history_stack[-1]
        self.current_ids = previous_state['current_ids'].copy()

        # Reload data and update display
        self.load_data()

        # Update button states
        self.update_undo_redo_buttons()

        print(f"Undid: {current_state['action']}")

    def redo_action(self):
        """Redo last undone action"""
        if not self.redo_stack:
            return

        # Move state from redo stack back to history
        state_to_redo = self.redo_stack.pop()
        self.history_stack.append(state_to_redo)

        # Restore state
        self.current_ids = state_to_redo['current_ids'].copy()

        # Reload data and update display
        self.load_data()

        # Update button states
        self.update_undo_redo_buttons()

        print(f"Redid: {state_to_redo['action']}")

    def update_undo_redo_buttons(self):
        """Update undo/redo button states"""
        # Undo available if we have more than 1 state in history
        self.undo_btn.setEnabled(len(self.history_stack) > 1)

        # Redo available if we have states in redo stack
        self.redo_btn.setEnabled(len(self.redo_stack) > 0)

        # Update tooltips with action descriptions
        if len(self.history_stack) > 1:
            last_action = self.history_stack[-1]['action']
            self.undo_btn.setToolTip(f"Undo: {last_action} (Ctrl+Z)")
        else:
            self.undo_btn.setToolTip("No actions to undo")

        if self.redo_stack:
            next_action = self.redo_stack[-1]['action']
            self.redo_btn.setToolTip(f"Redo: {next_action} (Ctrl+Y)")
        else:
            self.redo_btn.setToolTip("No actions to redo")

    def update_selection_label(self):
        """Update selection label with selected items info"""
        selected_rows = self.table.selectionModel().selectedRows()
        count = len(selected_rows)

        if count == 0:
            self.selection_label.setText("No items selected")
        elif count == 1:
            self.selection_label.setText("1 item selected")
        else:
            self.selection_label.setText(f"{count} items selected")

    def get_selected_original_indices(self):
        """Get original indices of selected rows"""
        selected_rows = self.table.selectionModel().selectedRows()
        original_indices = []

        for model_index in selected_rows:
            display_row = model_index.row()
            if display_row < len(self.filtered_indices):
                original_index = self.filtered_indices[display_row]
                original_indices.append(original_index)

        return sorted(original_indices)

    def batch_move_up(self):
        """Move all selected items up by 1 position"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        # Check if any item is already at top
        if 0 in original_indices:
            QMessageBox.information(self, "Thông báo", "Một số items đã ở vị trí đầu tiên!")
            return

        # Move each item up (process from top to bottom to avoid conflicts)
        moved_items = []
        for original_index in original_indices:
            if original_index > 0:
                # Swap with item above
                item_id = self.current_ids[original_index]
                self.current_ids[original_index], self.current_ids[original_index - 1] = \
                    self.current_ids[original_index - 1], self.current_ids[original_index]
                moved_items.append(item_id)

        if moved_items:
            self.save_state(f"Batch move up {len(moved_items)} items")
            self.load_data()
            print(f"Batch moved up: {', '.join(moved_items)}")

    def batch_move_down(self):
        """Move all selected items down by 1 position"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        # Check if any item is already at bottom
        max_index = len(self.current_ids) - 1
        if max_index in original_indices:
            QMessageBox.information(self, "Thông báo", "Một số items đã ở vị trí cuối cùng!")
            return

        # Move each item down (process from bottom to top to avoid conflicts)
        moved_items = []
        for original_index in reversed(original_indices):
            if original_index < max_index:
                # Swap with item below
                item_id = self.current_ids[original_index]
                self.current_ids[original_index], self.current_ids[original_index + 1] = \
                    self.current_ids[original_index + 1], self.current_ids[original_index]
                moved_items.append(item_id)

        if moved_items:
            self.save_state(f"Batch move down {len(moved_items)} items")
            self.load_data()
            print(f"Batch moved down: {', '.join(moved_items)}")

    def batch_move_to_top(self):
        """Move all selected items to the top"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        # Get selected items
        selected_items = [self.current_ids[i] for i in original_indices]

        # Remove selected items from their current positions (from back to front)
        for original_index in reversed(original_indices):
            self.current_ids.pop(original_index)

        # Insert at the beginning
        for i, item in enumerate(selected_items):
            self.current_ids.insert(i, item)

        self.save_state(f"Batch move to top {len(selected_items)} items")
        self.load_data()
        print(f"Batch moved to top: {', '.join(selected_items)}")

    def group_selected_by_shop(self):
        """Group selected items by shop"""
        original_indices = self.get_selected_original_indices()
        if not original_indices:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn ít nhất 1 item!")
            return

        # Get selected items with their shop info
        selected_items_with_shop = []
        for original_index in original_indices:
            item_id = self.current_ids[original_index]
            shop_id = self.parent_window.parent_app.deal_list_manager.id_to_shop.get(item_id, "")
            selected_items_with_shop.append((item_id, shop_id, original_index))

        # Sort by shop
        selected_items_with_shop.sort(key=lambda x: x[1])  # Sort by shop_id

        # Remove items from original positions (from back to front)
        for _, _, original_index in reversed(selected_items_with_shop):
            self.current_ids.pop(original_index)

        # Find insertion point (first selected position)
        insertion_point = min(original_indices)

        # Insert grouped items
        for i, (item_id, shop_id, _) in enumerate(selected_items_with_shop):
            self.current_ids.insert(insertion_point + i, item_id)

        self.save_state(f"Group by shop {len(selected_items_with_shop)} items")
        self.load_data()
        print(f"Grouped by shop: {len(selected_items_with_shop)} items")

    def on_item_double_clicked(self, item):
        """Xử lý double click để set position 1"""
        display_row = item.row()
        if display_row < len(self.filtered_indices):
            original_index = self.filtered_indices[display_row]
            original_position = self.original_data[original_index]['position']
            self.pos1_input.setValue(original_position)

            # Highlight dòng được chọn
            self.table.selectRow(display_row)

    def swap_positions(self):
        """Thực hiện true swap giữa 2 vị trí"""
        pos1 = self.pos1_input.value() - 1  # Convert to 0-based
        pos2 = self.pos2_input.value() - 1  # Convert to 0-based

        if pos1 == pos2:
            QMessageBox.warning(self, "Lỗi", "Hai vị trí phải khác nhau!")
            return

        if pos1 < 0 or pos1 >= len(self.current_ids) or pos2 < 0 or pos2 >= len(self.current_ids):
            QMessageBox.warning(self, "Lỗi", "Vị trí không hợp lệ!")
            return

        # True swap: hoán đổi hoàn toàn 2 ID
        id1 = self.current_ids[pos1]
        id2 = self.current_ids[pos2]
        self.current_ids[pos1], self.current_ids[pos2] = self.current_ids[pos2], self.current_ids[pos1]

        # Save state for undo
        self.save_state(f"Swap {id1} (pos {pos1+1}) ⇄ {id2} (pos {pos2+1})")

        # Cập nhật lại bảng
        self.load_data()

        # Highlight 2 dòng đã swap
        self.table.selectRow(pos1)
        self.table.selectRow(pos2)

        # Log swap action
        id1 = self.current_ids[pos1]
        id2 = self.current_ids[pos2]
        print(f"Swapped: Position {pos1+1} ({id1}) ⇄ Position {pos2+1} ({id2})")

        # Auto increment positions for next swap
        if self.pos2_input.value() < len(self.current_ids):
            self.pos1_input.setValue(self.pos2_input.value())
            self.pos2_input.setValue(self.pos2_input.value() + 1)

    def move_up(self):
        """Di chuyển ID được chọn lên 1 vị trí"""
        current_row = self.table.currentRow()
        if current_row <= 0:
            QMessageBox.information(self, "Thông báo", "ID đã ở vị trí đầu tiên!")
            return

        # Swap với vị trí trên
        pos1 = current_row  # 0-based
        pos2 = current_row - 1  # 0-based

        # True swap
        id_moved = self.current_ids[pos1]
        self.current_ids[pos1], self.current_ids[pos2] = self.current_ids[pos2], self.current_ids[pos1]

        # Save state for undo
        self.save_state(f"Move up {id_moved} from pos {pos1+1} to {pos2+1}")

        # Cập nhật bảng
        self.load_data()

        # Giữ selection ở vị trí mới
        self.table.selectRow(pos2)

        # Log action
        id_moved = self.current_ids[pos2]
        print(f"Moved up: {id_moved} from position {pos1+1} to {pos2+1}")

    def move_down(self):
        """Di chuyển ID được chọn xuống 1 vị trí"""
        current_row = self.table.currentRow()
        if current_row < 0:
            QMessageBox.information(self, "Thông báo", "Vui lòng chọn một dòng!")
            return

        if current_row >= len(self.current_ids) - 1:
            QMessageBox.information(self, "Thông báo", "ID đã ở vị trí cuối cùng!")
            return

        # Swap với vị trí dưới
        pos1 = current_row  # 0-based
        pos2 = current_row + 1  # 0-based

        # True swap
        id_moved = self.current_ids[pos1]
        self.current_ids[pos1], self.current_ids[pos2] = self.current_ids[pos2], self.current_ids[pos1]

        # Save state for undo
        self.save_state(f"Move down {id_moved} from pos {pos1+1} to {pos2+1}")

        # Cập nhật bảng
        self.load_data()

        # Giữ selection ở vị trí mới
        self.table.selectRow(pos2)

        # Log action
        id_moved = self.current_ids[pos2]
        print(f"Moved down: {id_moved} from position {pos1+1} to {pos2+1}")

    def reset_changes(self):
        """Reset về trạng thái ban đầu"""
        reply = QMessageBox.question(
            self, "Confirm Reset",
            "Bạn có chắc muốn reset tất cả thay đổi?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # Reset về dữ liệu gốc
            self.current_ids = self.processor.current_ids.copy()
            self.load_data()

            # Reset input positions
            self.pos1_input.setValue(1)
            self.pos2_input.setValue(2)

            print("Reset all changes to original state")

    def accept(self):
        """Apply changes và đóng dialog"""
        # Cập nhật dữ liệu gốc
        self.processor.current_ids = self.current_ids.copy()

        # Cập nhật final_column_data
        max_len = self.slot_data['max_len']
        final_column_data = [''] * max_len

        for i, id_val in enumerate(self.current_ids):
            if i < max_len:
                final_column_data[i] = id_val

        self.slot_data['final_column_data'] = final_column_data

        print(f"Applied changes to {self.time_slot}: {len(self.current_ids)} IDs")

        super().accept()


class ApprovalWindow(QDialog):
    """Cửa sổ Preview & Approval cho manual approval system"""

    def __init__(self, parent_app):
        super().__init__()
        self.parent_app = parent_app
        self.processed_data = {}
        self.approved_slots = set()  # Lưu các khung giờ đã được approve

        self.setWindowTitle("Preview & Approve - Manual Upload")
        self.setModal(False)  # Cho phép tương tác với cửa sổ chính
        self.resize(1200, 800)

        # Style cho dark theme
        self.setStyleSheet("""
            QDialog {
                background-color: #222;
                color: #EEE;
            }
            QTableWidget {
                background-color: #333;
                border: 1px solid #555;
                gridline-color: #555;
                selection-background-color: #4A90E2;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #444;
            }
            QHeaderView::section {
                background-color: #444;
                border: 1px solid #666;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 8px 16px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #555;
            }
            QPushButton:disabled {
                background-color: #333;
                color: #666;
            }
            QComboBox {
                background-color: #333;
                border: 1px solid #555;
                padding: 5px;
                border-radius: 3px;
            }
            QLabel {
                color: #EEE;
            }
        """)

        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Header với thông tin tổng quan
        header_layout = QHBoxLayout()
        self.info_label = QLabel("Chọn khung giờ để xem và chỉnh sửa trước khi upload")
        self.info_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #4A90E2;")
        header_layout.addWidget(self.info_label)
        header_layout.addStretch()

        # Nút Upload All Approved
        self.upload_all_btn = QPushButton("📤 Upload All Approved")
        self.upload_all_btn.clicked.connect(self.upload_all_approved)
        self.upload_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: 1px solid #45a049;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        header_layout.addWidget(self.upload_all_btn)

        layout.addLayout(header_layout)

        # Combo box chọn khung giờ
        slot_layout = QHBoxLayout()
        slot_layout.addWidget(QLabel("Khung giờ:"))
        self.time_slot_combo = QComboBox()
        self.time_slot_combo.currentTextChanged.connect(self.load_time_slot_data)
        slot_layout.addWidget(self.time_slot_combo)
        slot_layout.addStretch()

        # Thông tin khung giờ hiện tại
        self.slot_info_label = QLabel("")
        slot_layout.addWidget(self.slot_info_label)

        layout.addLayout(slot_layout)

        # Bảng hiển thị dữ liệu
        self.table = QTableWidget()
        self.table.setColumnCount(6)
        self.table.setHorizontalHeaderLabels([
            "Vị trí", "Item ID", "Shop ID", "Brand", "Timeline", "Điều kiện"
        ])

        # Thiết lập độ rộng cột
        header = self.table.horizontalHeader()
        header.setStretchLastSection(True)
        self.table.setColumnWidth(0, 80)   # Vị trí
        self.table.setColumnWidth(1, 120)  # Item ID
        self.table.setColumnWidth(2, 100)  # Shop ID
        self.table.setColumnWidth(3, 150)  # Brand
        self.table.setColumnWidth(4, 120)  # Timeline

        layout.addWidget(self.table)

        # Buttons cho actions
        button_layout = QHBoxLayout()

        self.approve_btn = QPushButton("✅ Approve & Ready to Upload")
        self.approve_btn.clicked.connect(self.approve_current_slot)
        self.approve_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                border: 1px solid #45a049;
                color: white;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)

        self.upload_btn = QPushButton("📤 Upload This Slot")
        self.upload_btn.clicked.connect(self.upload_current_slot)
        self.upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: 1px solid #1976D2;
                color: white;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)

        self.edit_btn = QPushButton("✏️ Edit Positions")
        self.edit_btn.clicked.connect(self.edit_positions)
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                border: 1px solid #F57C00;
                color: white;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)

        button_layout.addWidget(self.approve_btn)
        button_layout.addWidget(self.upload_btn)
        button_layout.addWidget(self.edit_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def load_processed_data(self, processed_data):
        """Load dữ liệu đã xử lý vào window"""
        self.processed_data = processed_data

        # Cập nhật combo box
        self.time_slot_combo.clear()
        for time_slot in sorted(processed_data.keys()):
            status = "✅ Approved" if time_slot in self.approved_slots else "⏳ Pending"
            self.time_slot_combo.addItem(f"{time_slot} - {status}")

        # Load khung giờ đầu tiên
        if processed_data:
            first_slot = sorted(processed_data.keys())[0]
            self.load_time_slot_data(f"{first_slot} - ⏳ Pending")

    def load_time_slot_data(self, combo_text):
        """Load dữ liệu cho khung giờ được chọn"""
        if not combo_text:
            return

        # Extract time slot từ combo text
        time_slot = combo_text.split(" - ")[0]

        if time_slot not in self.processed_data:
            return

        data = self.processed_data[time_slot]
        processor = data['processor']

        # Cập nhật thông tin khung giờ
        status = "✅ Approved" if time_slot in self.approved_slots else "⏳ Pending"
        self.slot_info_label.setText(f"Cột: {processor.column} | IDs: {len(processor.current_ids)} | {status}")

        # Load dữ liệu vào bảng
        self.table.setRowCount(len(processor.current_ids))

        for i, item_id in enumerate(processor.current_ids):
            # Vị trí (1-based)
            self.table.setItem(i, 0, QTableWidgetItem(str(i + 1)))

            # Item ID
            self.table.setItem(i, 1, QTableWidgetItem(str(item_id)))

            # Shop ID (từ deal list manager)
            shop_id = self.parent_app.deal_list_manager.id_to_shop.get(item_id, "")
            self.table.setItem(i, 2, QTableWidgetItem(str(shop_id) if shop_id else ""))

            # Brand thật (từ cột U - Nhãn hàng)
            brand = self.parent_app.deal_list_manager.id_to_brand.get(item_id, "")
            self.table.setItem(i, 3, QTableWidgetItem(str(brand)))

            # Timeline
            timeline = self.parent_app.deal_list_manager.id_to_timeline.get(item_id, "")
            self.table.setItem(i, 4, QTableWidgetItem(str(timeline) if timeline else ""))

            # Điều kiện xử lý với styling cho groups
            condition_text = self.get_condition_for_id(item_id, processor)
            condition_item = QTableWidgetItem(condition_text)

            # Thêm màu sắc cho các group khác nhau
            if "G" in condition_text:  # Nếu là group
                group_id = condition_text[-2:]  # Lấy GA, GB, GC...
                if group_id == "GA":
                    condition_item.setBackground(QColor(255, 182, 193))  # Light pink
                elif group_id == "GB":
                    condition_item.setBackground(QColor(173, 216, 230))  # Light blue
                elif group_id == "GC":
                    condition_item.setBackground(QColor(144, 238, 144))  # Light green
                elif group_id == "GD":
                    condition_item.setBackground(QColor(255, 218, 185))  # Peach
                elif group_id == "GE":
                    condition_item.setBackground(QColor(221, 160, 221))  # Plum
                else:
                    condition_item.setBackground(QColor(211, 211, 211))  # Light gray
                condition_item.setForeground(QColor(0, 0, 0))  # Black text for readability

            self.table.setItem(i, 5, condition_item)

    def get_condition_for_id(self, item_id, processor):
        """Xác định điều kiện xử lý cho ID này"""
        # Kiểm tra trong các conditions của processor
        for i, condition in enumerate(processor.conditions):
            if item_id in condition['ids']:
                if condition.get('is_atc', False):
                    return "🎯 ATC"
                else:
                    top_n = condition.get('top_n', 0)
                    if condition.get('is_group', False):
                        # Hiển thị Group với số thứ tự để phân biệt
                        group_id = chr(65 + i)  # A, B, C, D...
                        return f"📦 Top {top_n} (G{group_id})"
                    else:
                        return f"🔝 Top {top_n}"

        # Kiểm tra exclusive
        if item_id in processor.exclusive_ids:
            return "🔒 Exclusive"

        # Không hiển thị Base - trả về empty string
        return ""

    def approve_current_slot(self):
        """Approve khung giờ hiện tại"""
        combo_text = self.time_slot_combo.currentText()
        if not combo_text:
            return

        time_slot = combo_text.split(" - ")[0]
        self.approved_slots.add(time_slot)

        # Cập nhật combo box
        current_index = self.time_slot_combo.currentIndex()
        self.time_slot_combo.setItemText(current_index, f"{time_slot} - ✅ Approved")

        # Cập nhật label
        self.load_time_slot_data(self.time_slot_combo.currentText())

        QMessageBox.information(self, "Approved", f"Khung giờ {time_slot} đã được approve!")

    def upload_current_slot(self):
        """Upload khung giờ hiện tại lên Google Sheets"""
        combo_text = self.time_slot_combo.currentText()
        if not combo_text:
            return

        time_slot = combo_text.split(" - ")[0]

        if time_slot not in self.approved_slots:
            reply = QMessageBox.question(
                self, "Confirm Upload",
                f"Khung giờ {time_slot} chưa được approve. Bạn có muốn upload không?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply != QMessageBox.StandardButton.Yes:
                return

        self.upload_time_slot(time_slot)

    def upload_all_approved(self):
        """Upload tất cả khung giờ đã được approve"""
        if not self.approved_slots:
            QMessageBox.warning(self, "Lỗi", "Chưa có khung giờ nào được approve!")
            return

        reply = QMessageBox.question(
            self, "Confirm Upload All",
            f"Upload {len(self.approved_slots)} khung giờ đã approve?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            for time_slot in self.approved_slots:
                self.upload_time_slot(time_slot)

            # Tạo sheet Check quyền lợi
            self.create_check_sheet()

            QMessageBox.information(self, "Hoàn thành", "Đã upload tất cả khung giờ được approve!")

    def upload_time_slot(self, time_slot):
        """Upload một khung giờ cụ thể lên Google Sheets"""
        try:
            data = self.processed_data[time_slot]
            processor = data['processor']
            final_column_data = data['final_column_data']
            cell_range = data['cell_range']

            # Get worksheet
            worksheet = self.parent_app.spreadsheet.worksheet(self.parent_app.sheet_combo.currentText())

            # Upload data
            worksheet.update(cell_range, [[val] for val in final_column_data])

            self.parent_app.log(f"✅ Đã upload khung giờ {time_slot} (cột {processor.column})")

        except Exception as e:
            self.parent_app.log(f"❌ Lỗi upload khung giờ {time_slot}: {e}")
            QMessageBox.critical(self, "Lỗi Upload", f"Không thể upload khung giờ {time_slot}:\n{e}")

    def edit_positions(self):
        """Mở dialog chỉnh sửa vị trí với true swap functionality"""
        combo_text = self.time_slot_combo.currentText()
        if not combo_text:
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn khung giờ để chỉnh sửa.")
            return

        time_slot = combo_text.split(" - ")[0]

        if time_slot not in self.processed_data:
            QMessageBox.warning(self, "Lỗi", "Không tìm thấy dữ liệu cho khung giờ này.")
            return

        # Mở dialog edit positions
        edit_dialog = EditPositionsDialog(self, time_slot, self.processed_data[time_slot])
        if edit_dialog.exec() == QDialog.DialogCode.Accepted:
            # Cập nhật lại dữ liệu sau khi edit
            self.load_time_slot_data(self.time_slot_combo.currentText())

    def create_check_sheet(self):
        """Tạo sheet Check quyền lợi cho các khung giờ đã upload"""
        try:
            # Tạo time_slot_processors từ approved slots
            time_slot_processors = {}
            for time_slot in self.approved_slots:
                if time_slot in self.processed_data:
                    time_slot_processors[time_slot] = self.processed_data[time_slot]['processor']

            # Gọi hàm tạo check sheet từ parent app
            self.parent_app.create_check_quyen_loi_sheet(time_slot_processors)

        except Exception as e:
            self.parent_app.log(f"❌ Lỗi tạo sheet Check quyền lợi: {e}")


class MainApp(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Random ID to Google Sheet")
        self.resize(1000, 650)

        # Hiển thị thông báo về các vấn đề đã sửa
        print("===== CÁC VẤN ĐỀ ĐÃ ĐƯỢC SỬA =====")
        print("1. Đã sửa cấu trúc cột cho mỗi khung giờ: Shop ID (A/F/K...), Item ID (B/G/L...), STT (C/H/M...), Brand (D/I/N...)")
        print("2. Đã sửa cơ chế đọc dữ liệu Shop ID từ cùng cột với khung giờ nhưng bắt đầu từ dòng 4")
        print("3. Đã nâng cao độ chính xác khi ánh xạ khung giờ với các cột liên quan")
        print("===============================")

        # Thiết lập style chung cho các thành phần giao diện
        self.setStyleSheet("""
            QWidget {
                background-color: #222;
                color: #EEE;
            }
            QLineEdit, QTextEdit {
                background-color: #333;
                border: 1px solid #555;
                padding: 3px;
                border-radius: 3px;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 4px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #555;
            }
            QGroupBox {
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
            }
        """)

        self.layout = QVBoxLayout()
        self.rows = []

        self.setup_group = QGroupBox("Google Sheet Informations")
        self.setup_layout = QVBoxLayout()

        # Hàng 1: URL và nút Load Sheet
        self.row1_layout = QHBoxLayout()
        self.sheet_url_input = QLineEdit()
        self.sheet_url_input.setPlaceholderText("Nhập Google Spreadsheet URL")
        self.sheet_url_input.textChanged.connect(self.auto_parse_spreadsheet_id)

        self.load_button = QPushButton("Load Sheet")
        self.load_button.clicked.connect(self.load_spreadsheet)
        # Cài đặt size policy cho nút Load Sheet
        self.load_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.load_button.setMinimumWidth(100)

        self.row1_layout.addWidget(QLabel("Spreadsheet URL:"))
        self.row1_layout.addWidget(self.sheet_url_input, 1)  # Cho phép mở rộng theo chiều ngang
        self.row1_layout.addWidget(self.load_button)

        # Hàng 2: Sheet, Cột ID đầu, Số cột, Map cột
        self.row2_layout = QHBoxLayout()

        self.sheet_combo = QComboBox()
        self.sheet_combo.setEnabled(False)
        self.sheet_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        self.first_col_input = QLineEdit()
        self.first_col_input.setPlaceholderText("Cột ID đầu tiên")
        self.first_col_input.setMaxLength(3)
        self.first_col_input.setFixedWidth(100)

        self.num_columns_input = QSpinBox()
        self.num_columns_input.setMinimum(1)
        self.num_columns_input.setMaximum(50)
        self.num_columns_input.setValue(NUM_ID_COLUMNS)
        self.num_columns_input.setToolTip("Số lượng cột ID/khung giờ")
        self.num_columns_input.setFixedWidth(100)

        # Nút Map cột nằm ở cuối dòng 2
        self.map_columns_button = QPushButton("Map cột")
        self.map_columns_button.setEnabled(False)  # Chỉ bật sau khi load sheet
        self.map_columns_button.clicked.connect(self.show_column_mapping_dialog)
        self.map_columns_button.setToolTip("Thiết lập ánh xạ vị trí cột trong sheet Deal list")
        # Đảm bảo nút Map cột có cùng kích thước với nút Load Sheet
        self.map_columns_button.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.map_columns_button.setMinimumWidth(100)

        self.row2_layout.addWidget(QLabel("Sheet:"))
        self.row2_layout.addWidget(self.sheet_combo, 1)  # Stretch factor 1

        # Thêm nút Exclusive Mode ngay sau sheet_combo và trước "Cột ID đầu"
        self.exclusive_button = QPushButton("Exclusive Mode")
        self.exclusive_button.setToolTip("Thiết lập các ID đặc biệt được giữ nguyên vị trí trong khung giờ được chọn")
        self.exclusive_button.clicked.connect(self.show_exclusive_dialog)
        # Chỉnh style cho nút Exclusive mode với màu tím
        self.exclusive_button.setStyleSheet("""
            QPushButton {
                background-color: #553366;
                border: 1px solid #775588;
                padding: 6px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #664477;
            }
        """)

        self.row2_layout.addWidget(self.exclusive_button)

        self.row2_layout.addWidget(QLabel("Cột ID đầu:"))
        self.row2_layout.addWidget(self.first_col_input)
        self.row2_layout.addWidget(QLabel("Số cột:"))
        self.row2_layout.addWidget(self.num_columns_input)
        self.row2_layout.addWidget(self.map_columns_button)  # Nút Map cột ở cuối

        self.setup_layout.addLayout(self.row1_layout)
        self.setup_layout.addLayout(self.row2_layout)
        self.setup_group.setLayout(self.setup_layout)

        self.condition_group = QGroupBox("Thiết lập điều kiện")
        self.condition_layout = QVBoxLayout()

        # Phần scroll area cho các điều kiện
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.row_container = QWidget()
        self.row_layout = QVBoxLayout()
        self.row_layout.setSpacing(0)  # Giảm khoảng cách giữa các dòng
        self.row_layout.setContentsMargins(0, 0, 0, 0)  # Bỏ margin
        self.row_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # Căn các item lên trên cùng
        self.row_container.setLayout(self.row_layout)
        self.scroll_area.setWidget(self.row_container)

        # Tạo container cho nút thêm dòng
        self.add_button_container = QHBoxLayout()
        self.add_button_container.setContentsMargins(0, 5, 0, 0)

        # Tạo nút thêm dòng lớn hơn
        self.add_button = QToolButton()
        self.add_button.setText("+")
        self.add_button.setFixedSize(40, 25)  # Kích thước lớn hơn
        self.add_button.clicked.connect(self.add_condition_row)

        # Tạo nút Save Settings
        self.save_settings_button = QPushButton("💾 Lưu cài đặt")
        self.save_settings_button.setFixedHeight(25)
        self.save_settings_button.setToolTip("Lưu khung giờ, điều kiện và ID input vào file JSON")
        self.save_settings_button.clicked.connect(self.save_settings)
        self.save_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #2E7D32;
                border: 1px solid #4CAF50;
                border-radius: 3px;
                padding: 3px 8px;
                color: white;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
        """)

        # Tạo nút Load Settings
        self.load_settings_button = QPushButton("📁 Tải cài đặt")
        self.load_settings_button.setFixedHeight(25)
        self.load_settings_button.setToolTip("Tải khung giờ, điều kiện và ID input từ file JSON")
        self.load_settings_button.clicked.connect(self.load_settings)
        self.load_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #1976D2;
                border: 1px solid #2196F3;
                border-radius: 3px;
                padding: 3px 8px;
                color: white;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #1E88E5;
            }
        """)

        # Thêm một layout để căn chỉnh nút về bên trái (dưới cột Top)
        button_alignment_layout = QHBoxLayout()
        button_alignment_layout.addWidget(self.add_button)
        button_alignment_layout.addWidget(self.save_settings_button)
        button_alignment_layout.addWidget(self.load_settings_button)
        button_alignment_layout.addStretch(1)  # Đẩy nút về bên trái
        self.add_button_container.addLayout(button_alignment_layout)

        # Thêm scroll area vào layout điều kiện
        self.condition_layout.addWidget(self.scroll_area)
        self.condition_layout.addLayout(self.add_button_container)
        self.condition_group.setLayout(self.condition_layout)

        self.execution_group = QGroupBox("Log Processing")
        self.execution_layout = QVBoxLayout()

        # Layout nút đặc biệt (giữ lại nhưng không thêm nút Exclusive)
        self.special_buttons_layout = QHBoxLayout()
        self.special_buttons_layout.addStretch(1)  # Giữ lại layout cho tương thích

        self.log_box = QTextEdit()
        self.log_box.setReadOnly(True)
        self.log_box.setPlaceholderText("Log quá trình xử lý...")

        # Xóa phần style ở đây vì đã thêm ở trên

        # Modern Start Processing button fit với window width
        self.submit_button = QPushButton("🚀 Start Processing")
        self.submit_button.clicked.connect(self.process_rows)
        self.submit_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                border: 1px solid #1976D2;
                border-radius: 4px;
                padding: 8px 16px;
                color: white;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
                border: 1px solid #1565C0;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """)

        # Bỏ thêm special_buttons_layout vì không còn cần thiết
        self.execution_layout.addWidget(self.log_box)

        # Layout cho nút Start Processing fit với window width
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(self.submit_button)  # Full width button

        self.execution_layout.addLayout(buttons_layout)
        self.execution_group.setLayout(self.execution_layout)

        self.layout.addWidget(self.setup_group)
        self.layout.addWidget(self.condition_group)
        self.layout.addWidget(self.execution_group)
        self.setLayout(self.layout)

        self.sheet = None
        self.spreadsheet = None
        self.id_columns = []
        self.time_slot_map = {}
        self.timeslot_columns_map = {}  # Lưu ánh xạ khung giờ -> tất cả cột liên quan
        self.deal_list_manager = DealListManager()  # Khởi tạo Deal list manager
        self.fixed_positions = {}  # Dictionary lưu trữ vị trí cố định của ID: {time_slot: {id: position}}

        # Dictionary lưu trữ ID Exclusive: {time_slot: [id1, id2, ...]}
        self.exclusive_ids = {}

        # Lưu trữ dữ liệu từ các cột
        self.col_data = {}         # Dữ liệu Item ID
        self.col_shop_data = {}    # Dữ liệu Shop ID
        self.col_stt_data = {}     # Dữ liệu STT
        self.col_brand_data = {}   # Dữ liệu tên Brand

        # Khởi tạo Settings Manager
        self.settings_manager = SettingsManager()

        # Thêm biến để lưu trữ ánh xạ cột
        self.column_mapping = {
            "id_column": "C",
            "cluster_column": "I",
            "nmv_column": "AZ",
            "timeline_column": "R",  # Cột Timeline
            "shop_id_column": "F",   # Cột Shop ID
            "review_m_column": "M",  # Cột Review M
            "review_n_column": "N",  # Cột Review N
            "no_column": "S",        # Cột NO (thứ tự ưu tiên)
            "brand_column": "U"      # Cột U - Nhãn hàng (Brand thật)
        }

        # THÊM SAU DÒNG 1217, sau class ClusterSelectionDialog
        # Dialog để quản lý ID Exclusive
        self.exclusive_ids_dialog = ExclusiveIDsDialog(self.time_slot_map, self.fixed_positions, self)

        # Thuộc tính cho manual approval system
        self.processed_data = {}  # Lưu kết quả xử lý: {time_slot: processor}
        self.approval_window = None

    def auto_parse_spreadsheet_id(self, text):
        """Tự động phân tích spreadsheet ID từ URL khi dán vào"""
        # Tìm spreadsheet ID từ URL
        match = re.search(r"/d/([a-zA-Z0-9-_]+)", text)
        if match:
            spreadsheet_id = match.group(1)
            # Tự động cập nhật text thành spreadsheet ID
            if text != spreadsheet_id:
                self.sheet_url_input.blockSignals(True)
                self.sheet_url_input.setText(spreadsheet_id)
                self.sheet_url_input.blockSignals(False)

    def log(self, text):
        self.log_box.append(text)

    def generate_id_columns(self, start_col):
        base_index = col_to_index(start_col)
        num_columns = self.num_columns_input.value()
        return [index_to_col(base_index + i * COLUMN_STEP) for i in range(num_columns)]

    def get_time_column(self, id_col):
        """Lấy cột header khung giờ từ cột ID (cột trước cột ID)"""
        id_col_index = col_to_index(id_col)
        time_col_index = id_col_index - 1
        return index_to_col(time_col_index)

    def detect_columns_by_headers(self, all_values):
        """Phát hiện cột dựa vào header"""
        headers = {}
        if len(all_values) >= HEADER_ROW:
            header_row = all_values[HEADER_ROW - 1]
            for col_idx, header_text in enumerate(header_row):
                header_text = header_text.strip()
                if header_text:
                    headers[header_text] = index_to_col(col_idx)
            self.log(f"Đã phát hiện các header: {headers}")
        return headers

    def map_timeslots_to_columns(self, timeslots_with_positions):
        """
        Ánh xạ khung giờ với nhóm cột dựa trên vị trí thực tế trên sheet.
        Tôn trọng vị trí cột gốc thay vì sắp xếp lại theo thời gian.
        Mỗi khung giờ có một nhóm cột liên quan:
        - Cột khung giờ (ví dụ: A dòng 2)
        - Cột Shop ID (ví dụ: A từ dòng 4)
        - Cột Item ID (ví dụ: B)
        - Cột STT (ví dụ: C)
        - Cột tên Brand (ví dụ: D)
        """
        self.log("\n=== BẮT ĐẦU ÁNH XẠ KHUNG GIỜ VỚI CỘT (TÔN TRỌNG VỊ TRÍ GỐC) ===")

        # Sắp xếp theo vị trí cột thay vì theo thời gian
        sorted_by_position = sorted(timeslots_with_positions, key=lambda x: x[0])
        self.log(f"Đã tìm thấy {len(timeslots_with_positions)} khung giờ để ánh xạ theo vị trí gốc:")
        for col_idx, timeslot in sorted_by_position:
            self.log(f"  - '{timeslot}' ở cột {index_to_col(col_idx)}")

        # Tính vị trí cột dựa trên vị trí thực tế
        timeslot_to_columns = {}
        for col_idx, timeslot in sorted_by_position:
            # Tính toán vị trí cột dựa trên vị trí thực tế của khung giờ
            # Giả sử khung giờ nằm ở cột đầu tiên của nhóm (time_column)
            # Các cột khác sẽ theo sau với khoảng cách cố định

            # Lưu thông tin tất cả các cột liên quan dựa trên vị trí thực tế
            column_info = {
                "time_column": index_to_col(col_idx),           # cột khung giờ (vị trí thực tế) dòng 2
                "shop_id_column": index_to_col(col_idx),        # cột Shop ID (cùng vị trí) từ dòng 4
                "item_id_column": index_to_col(col_idx + 1),    # cột Item ID (cột tiếp theo)
                "stt_column": index_to_col(col_idx + 2),        # cột STT (cột thứ 3)
                "brand_column": index_to_col(col_idx + 3)       # cột tên Brand (cột thứ 4)
            }

            # Lưu thông tin nhóm cột
            timeslot_to_columns[timeslot] = column_info
            self.log(f"Ánh xạ khung giờ '{timeslot}' (vị trí gốc {index_to_col(col_idx)}) với nhóm cột:")
            self.log(f"  + Khung giờ (dòng 2): {column_info['time_column']}")
            self.log(f"  + Shop ID (từ dòng 4): {column_info['shop_id_column']}")
            self.log(f"  + Item ID: {column_info['item_id_column']}")
            self.log(f"  + STT: {column_info['stt_column']}")
            self.log(f"  + Brand: {column_info['brand_column']}")

        self.log("=== KẾT THÚC ÁNH XẠ KHUNG GIỜ (ĐÃ TÔN TRỌNG VỊ TRÍ GỐC) ===\n")
        return timeslot_to_columns

    def load_spreadsheet(self):
        url = self.sheet_url_input.text()

        # Kiểm tra xem có phải là URL không để parse
        if "/" in url:
            match = re.search(r"/d/([a-zA-Z0-9-_]+)", url)
            if match:
                spreadsheet_id = match.group(1)
                # Cập nhật text thành ID
                self.sheet_url_input.setText(spreadsheet_id)
                url = spreadsheet_id

        match = re.search(r"/d/([a-zA-Z0-9-_]+)", url)
        if not match:
            if re.match(r"^[a-zA-Z0-9-_]+$", url):
                spreadsheet_id = url
            else:
                QMessageBox.warning(self, "Lỗi", "Không tìm thấy Spreadsheet ID từ URL")
                return
        else:
            spreadsheet_id = match.group(1)

        try:
            self.manager = GoogleSheetManager(auth_type='oauth', credentials_data=BASE64_OAUTH)
            self.spreadsheet = self.manager.open_by_key(spreadsheet_id)

            self.sheet_combo.clear()
            default_sheet_index = 0  # Mặc định chọn sheet đầu tiên
            deal_list_index = -1  # Để lưu vị trí sheet "Deal list"

            worksheets = self.spreadsheet.worksheets()
            for i, worksheet in enumerate(worksheets):
                self.sheet_combo.addItem(worksheet.title)
                # Tìm sheet "basket" (không phân biệt chữ hoa/thường)
                if worksheet.title.lower() == 'basket':
                    default_sheet_index = i
                # Tìm sheet "Deal list" hoặc tương tự
                if worksheet.title.lower() == 'deal list':
                    deal_list_index = i

            self.sheet_combo.setEnabled(True)
            self.map_columns_button.setEnabled(True)  # Kích hoạt nút Map cột

            self.sheet_combo.currentIndexChanged.connect(self.load_sheet_data)

            # Load Deal list data nếu có
            if deal_list_index >= 0:
                deal_list_sheet = worksheets[deal_list_index]
                # Truyền hàm log để hiển thị thông tin trong UI
                self.log("\n=== ĐANG LOAD DỮ LIỆU TỪ DEAL LIST ===")
                self.deal_list_manager.set_log_function(self.log)
                success, message = self.deal_list_manager.load_data_with_column_positions(deal_list_sheet, self.column_mapping)
                if success:
                    self.log(message)
                else:
                    self.log(f"Lỗi khi load Deal list: {message}")
            else:
                self.log("Không tìm thấy sheet 'Deal list'. Chức năng quản lý Cluster sẽ không hoạt động.")

            if self.sheet_combo.count() > 0:
                self.sheet_combo.setCurrentIndex(default_sheet_index)
                self.load_sheet_data()

        except Exception as e:
            QMessageBox.critical(self, "Lỗi", str(e))

    def load_sheet_data(self):
        if not self.spreadsheet or self.sheet_combo.count() == 0:
            return

        # Không kiểm tra cột đầu tiên khi load sheet
        # Chỉ kiểm tra khi xử lý

        selected_sheet_name = self.sheet_combo.currentText()
        try:
            self.sheet = self.spreadsheet.worksheet(selected_sheet_name)
            all_values = self.sheet.get_all_values()

            # Lấy tất cả các khung giờ có sẵn trong sheet
            time_slots = []

            # CHÚ Ý: Không lấy khung giờ từ dòng header nữa mà lấy trực tiếp từ dòng khung giờ (2)
            timeslots_with_positions = []  # Lưu trữ (col_idx, timeslot)
            if len(all_values) >= TIME_SLOT_ROW:
                time_row = all_values[TIME_SLOT_ROW - 1]
                self.log(f"Debug - Dòng khung giờ {TIME_SLOT_ROW}: {time_row[:10]}")

                # Phát hiện khung giờ trực tiếp từ dòng 2 theo pattern "HH:MM-HH:MM"
                # Lưu cả vị trí cột để tôn trọng thứ tự gốc
                for col_idx, cell in enumerate(time_row):
                    cell_value = cell.strip()
                    if re.search(r'\d+:\d+', cell_value):  # Tìm các ô có định dạng thời gian
                        # Chuẩn hóa định dạng khung giờ nếu cần
                        if re.search(r'\d+:\d+\s+\d+:\d+', cell_value):
                            parts = cell_value.split()
                            if len(parts) >= 2:
                                start_time = parts[0].strip()
                                end_time = parts[1].strip()
                                cell_value = f"{start_time}-{end_time}"

                        timeslots_with_positions.append((col_idx, cell_value))
                        time_slots.append(cell_value)
                        self.log(f"Debug - Phát hiện khung giờ '{cell_value}' ở cột {index_to_col(col_idx)} (vị trí {col_idx})")

            # Hiển thị header để kiểm tra
            if len(all_values) >= HEADER_ROW:
                header_row = all_values[HEADER_ROW - 1]
                self.log(f"Debug - Header dòng {HEADER_ROW}: {header_row[:10]}")

            # MỚI: Ánh xạ khung giờ với nhóm cột dựa trên vị trí thực tế
            # Tôn trọng vị trí cột gốc thay vì sắp xếp lại theo thời gian
            self.timeslot_columns_map = self.map_timeslots_to_columns(timeslots_with_positions)

            # Đưa vào time_slot_map (chỉ cần thông tin cột Item ID cho tương thích ngược)
            self.time_slot_map = {timeslot: info["item_id_column"]
                                for timeslot, info in self.timeslot_columns_map.items()}

            self.log("Ánh xạ khung giờ với nhóm cột theo quy tắc khoảng cách cố định:")

            # Debug: In ra time_slot_map ban đầu
            self.log(f"Debug - Khởi tạo time_slot_map: {self.time_slot_map}")

            # Xóa và tạo lại các dòng điều kiện
            self.clear_condition_rows()

            # Cập nhật các time slots trong dòng điều kiện
            self.update_time_slots_in_rows()

        except Exception as e:
            self.log(f"Lỗi: {str(e)}")
            QMessageBox.critical(self, "Lỗi", str(e))

    def add_condition_row(self):
        """Thêm một dòng điều kiện mới vào cuối danh sách"""
        # Tạo row mới với callback cho nút thêm dòng
        row = ConditionRow(
            list(self.time_slot_map.keys()),
            lambda: self.remove_condition_row(row)
        )

        # Thêm vào cuối danh sách
        self.rows.append(row)
        self.row_layout.addWidget(row)

    def remove_condition_row(self, row):
        self.row_layout.removeWidget(row)
        self.rows.remove(row)
        row.deleteLater()

        # Đảm bảo luôn có ít nhất một dòng
        if not self.rows:
            self.add_condition_row()

    def clear_condition_rows(self):
        for row in self.rows:
            self.row_layout.removeWidget(row)
            row.deleteLater()
        self.rows.clear()

    def _apply_rotation_logic(self, ids, time_slots):
        """
        Áp dụng logic rotation cho ID input khi có >3 IDs

        Args:
            ids: Danh sách ID input
            time_slots: Danh sách khung giờ được chọn

        Returns:
            List[List]: Danh sách ID cho từng khung giờ
        """
        import random

        num_slots = len(time_slots)
        ids_per_slot = 3  # Luôn luôn 3 ID per slot

        # Tạo danh sách kết quả
        result = []

        # Tạo seed dựa trên nội dung để đảm bảo kết quả nhất quán
        seed_content = ''.join(sorted(ids)) + ''.join(sorted(time_slots))
        random.seed(hash(seed_content) % (2**32))

        # Tạo pool ID mở rộng để đảm bảo đủ ID cho tất cả slot
        extended_pool = ids.copy()

        # Nếu cần nhiều ID hơn số ID có sẵn, lặp lại danh sách
        total_needed = num_slots * ids_per_slot
        while len(extended_pool) < total_needed:
            extended_pool.extend(ids)

        # Trộn pool để tạo sự ngẫu nhiên
        random.shuffle(extended_pool)

        # Phân phối ID cho từng slot
        used_ids = set()

        for slot_idx in range(num_slots):
            slot_ids = []
            attempts = 0
            max_attempts = len(extended_pool) * 2

            while len(slot_ids) < ids_per_slot and attempts < max_attempts:
                # Chọn ID ngẫu nhiên từ pool
                candidate_id = random.choice(extended_pool)

                # Thêm ID nếu chưa được sử dụng trong slot này
                if candidate_id not in slot_ids:
                    slot_ids.append(candidate_id)
                    used_ids.add(candidate_id)

                attempts += 1

            # Nếu vẫn thiếu ID, bổ sung từ danh sách gốc
            if len(slot_ids) < ids_per_slot:
                remaining_needed = ids_per_slot - len(slot_ids)
                available_ids = [id_val for id_val in ids if id_val not in slot_ids]

                if available_ids:
                    # Lặp lại available_ids nếu cần
                    while len(available_ids) < remaining_needed:
                        available_ids.extend([id_val for id_val in ids if id_val not in slot_ids])

                    random.shuffle(available_ids)
                    slot_ids.extend(available_ids[:remaining_needed])

            result.append(slot_ids[:ids_per_slot])  # Đảm bảo chỉ lấy đúng 3 ID

        # Đảm bảo mọi ID gốc đều xuất hiện ít nhất một lần
        all_used_ids = set()
        for slot_ids in result:
            all_used_ids.update(slot_ids)

        missing_ids = [id_val for id_val in ids if id_val not in all_used_ids]

        # Nếu có ID bị thiếu, thay thế ngẫu nhiên
        if missing_ids:
            for missing_id in missing_ids:
                # Chọn slot ngẫu nhiên để thay thế
                slot_idx = random.randint(0, num_slots - 1)
                replace_idx = random.randint(0, ids_per_slot - 1)
                result[slot_idx][replace_idx] = missing_id

        return result

    def create_check_quyen_loi_sheet(self, time_slot_processors):
        """Tạo sheet Check quyền lợi để hiển thị vị trí của các ID input"""
        try:
            self.log("\n=== TẠO SHEET CHECK QUYỀN LỢI ===")

            # Tạo hoặc lấy sheet Check quyền lợi
            try:
                check_sheet = self.spreadsheet.worksheet('Check quyền lợi')
                check_sheet.clear()  # Xóa dữ liệu cũ
            except:
                check_sheet = self.spreadsheet.add_worksheet(title='Check quyền lợi', rows=1000, cols=100)

            # Lấy sheet Basket để copy header
            basket_sheet = self.spreadsheet.worksheet('Basket')
            basket_header_data = basket_sheet.get('A1:BV3')  # Lấy 3 dòng header đầu

            # Copy header với format từ sheet Basket
            if basket_header_data:
                self.log("Đang copy header từ sheet Basket...")
                check_sheet.update('A1:BV3', basket_header_data)

                # Copy format của header
                self._copy_header_formatting(basket_sheet, check_sheet)

                # Thêm công thức COUNTUNIQUE vào dòng 3
                self._add_countunique_formulas(check_sheet)

            # Thu thập thông tin ID input và vị trí của chúng
            input_positions_data = self._collect_input_positions(time_slot_processors)

            # Tạo dữ liệu cho sheet Check quyền lợi
            self._write_check_quyen_loi_data(check_sheet, input_positions_data)

            self.log("✅ Đã tạo sheet Check quyền lợi thành công!")

        except Exception as e:
            self.log(f"❌ Lỗi khi tạo sheet Check quyền lợi: {e}")

    def _copy_header_formatting(self, source_sheet, target_sheet):
        """Copy formatting từ sheet nguồn sang sheet đích"""
        try:
            # Sử dụng batch update để copy format
            requests = []

            # Copy format cho 3 dòng header đầu
            for row in range(1, 4):
                requests.append({
                    'copyPaste': {
                        'source': {
                            'sheetId': source_sheet.id,
                            'startRowIndex': row - 1,
                            'endRowIndex': row,
                            'startColumnIndex': 0,
                            'endColumnIndex': 100  # Đến cột CV
                        },
                        'destination': {
                            'sheetId': target_sheet.id,
                            'startRowIndex': row - 1,
                            'endRowIndex': row,
                            'startColumnIndex': 0,
                            'endColumnIndex': 100
                        },
                        'pasteType': 'PASTE_FORMAT'
                    }
                })

            if requests:
                self.spreadsheet.batch_update({'requests': requests})
                self.log("Đã copy format header thành công")

        except Exception as e:
            self.log(f"Cảnh báo: Không thể copy format header: {e}")

    def _add_countunique_formulas(self, check_sheet):
        """Thêm công thức COUNTUNIQUE vào dòng 3 của header"""
        try:
            countunique_updates = []

            # Sắp xếp khung giờ theo thứ tự trong time_slot_map
            sorted_time_slots = list(self.time_slot_map.keys())

            for slot_idx, time_slot in enumerate(sorted_time_slots):
                # Tính cột cho COUNTUNIQUE (D, I, N, S, X, AC, AH, ...)
                start_col_idx = 1 + (slot_idx * 5)  # B=1, G=6, L=11, ...
                countunique_col_idx = start_col_idx + 2  # D=3, I=8, N=13, ...
                countunique_col = index_to_col(countunique_col_idx)
                id_col = index_to_col(start_col_idx)

                # Tạo công thức COUNTUNIQUE
                formula = f"=COUNTUNIQUE({id_col}4:{id_col})"

                countunique_updates.append({
                    'range': f"{countunique_col}3",
                    'values': [[formula]]
                })

                self.log(f"COUNTUNIQUE cho {time_slot}: {countunique_col}3 = {formula}")

            # Ghi công thức COUNTUNIQUE
            if countunique_updates:
                self.log("Đang thêm công thức COUNTUNIQUE...")
                self._write_formulas_properly(check_sheet, countunique_updates)
                self.log(f"Đã thêm {len(countunique_updates)} công thức COUNTUNIQUE")

        except Exception as e:
            self.log(f"Lỗi khi thêm công thức COUNTUNIQUE: {e}")

    def _collect_input_positions(self, time_slot_processors):
        """Thu thập thông tin vị trí của các ID input được xử lý cho từng khung giờ cụ thể"""
        input_positions = {}

        for time_slot, processor in time_slot_processors.items():
            input_positions[time_slot] = []

            # Lấy danh sách ID input được xử lý cho khung giờ này từ các điều kiện của processor
            input_ids_for_this_slot = set()
            for condition in processor.conditions:
                input_ids_for_this_slot.update(condition['ids'])

            # Tìm vị trí của các ID input được xử lý cho khung giờ này trong kết quả cuối cùng
            for i, id_val in enumerate(processor.current_ids):
                if id_val in input_ids_for_this_slot:
                    input_positions[time_slot].append({
                        'id': id_val,
                        'position': i + 1  # Vị trí 1-indexed
                    })

            self.log(f"Khung giờ {time_slot}: {len(input_positions[time_slot])} ID input được xử lý")

        return input_positions

    def _is_id_belongs_to_current_slot(self, id_val, current_time_slot):
        """Kiểm tra ID có thuộc timeline của khung giờ hiện tại không"""
        if not hasattr(self, 'deal_list_manager') or not self.deal_list_manager:
            return True  # Nếu không có deal_list_manager, coi như thuộc

        # Kiểm tra timeline của ID
        id_timeline = self.deal_list_manager.id_to_timeline.get(id_val, "")
        if not id_timeline:
            return True  # Nếu không có timeline, coi như thuộc

        # Chuẩn hóa để so sánh
        normalized_current = normalize_timeline(current_time_slot).lower()
        normalized_id_timeline = normalize_timeline(id_timeline).lower()

        return normalized_current in normalized_id_timeline

    def _write_check_quyen_loi_data(self, check_sheet, input_positions_data):
        """Ghi dữ liệu vào sheet Check quyền lợi"""
        try:
            # Chuẩn bị dữ liệu batch update
            updates = []
            formula_updates = []

            # Sắp xếp khung giờ theo thứ tự trong time_slot_map
            sorted_time_slots = list(self.time_slot_map.keys())

            for slot_idx, time_slot in enumerate(sorted_time_slots):
                if time_slot not in input_positions_data:
                    continue

                positions_data = input_positions_data[time_slot]
                if not positions_data:
                    continue

                # Tính cột bắt đầu (B, G, L, Q, V, AA, AF, ...)
                start_col_idx = 1 + (slot_idx * 5)  # B=1, G=6, L=11, ...
                id_col = index_to_col(start_col_idx)
                pos_col = index_to_col(start_col_idx + 1)
                vlookup1_col = index_to_col(start_col_idx - 1)  # A, F, K, ...
                vlookup2_col = index_to_col(start_col_idx + 2)  # D, I, N, ...

                # Chuẩn bị dữ liệu cho khung giờ này
                slot_data = []
                vlookup1_formulas = []
                vlookup2_formulas = []

                for row_idx, item in enumerate(positions_data):
                    current_row = 4 + row_idx
                    slot_data.append([item['id'], item['position']])

                    # Tạo công thức VLOOKUP
                    vlookup1_formula = f"=VLOOKUP({id_col}{current_row},'Deal list'!$C:$F,4,false)"
                    vlookup2_formula = f"=VLOOKUP({id_col}{current_row},'Deal list'!$C:$U,19,false)"

                    vlookup1_formulas.append([vlookup1_formula])
                    vlookup2_formulas.append([vlookup2_formula])

                if slot_data:
                    # Tạo range để update dữ liệu ID và vị trí
                    end_row = 4 + len(slot_data) - 1
                    range_name = f"{id_col}4:{pos_col}{end_row}"

                    updates.append({
                        'range': range_name,
                        'values': slot_data
                    })

                    # Tạo range để update công thức VLOOKUP
                    vlookup1_range = f"{vlookup1_col}4:{vlookup1_col}{end_row}"
                    vlookup2_range = f"{vlookup2_col}4:{vlookup2_col}{end_row}"

                    formula_updates.append({
                        'range': vlookup1_range,
                        'values': vlookup1_formulas
                    })

                    formula_updates.append({
                        'range': vlookup2_range,
                        'values': vlookup2_formulas
                    })

                    self.log(f"Khung giờ {time_slot}: {len(slot_data)} ID tại {range_name}")
                    self.log(f"  VLOOKUP1: {vlookup1_range}, VLOOKUP2: {vlookup2_range}")

            # Thực hiện batch update cho dữ liệu
            if updates:
                self.log("Đang ghi dữ liệu vào sheet Check quyền lợi...")
                check_sheet.batch_update(updates)
                self.log(f"Đã ghi {len(updates)} khung giờ vào sheet Check quyền lợi")

            # Thực hiện batch update cho công thức bằng cách khác để tránh dấu '
            if formula_updates:
                self.log("Đang ghi công thức VLOOKUP...")
                self._write_formulas_properly(check_sheet, formula_updates)
                self.log(f"Đã ghi {len(formula_updates)} công thức VLOOKUP")

            if not updates and not formula_updates:
                self.log("Không có dữ liệu ID input để ghi")

        except Exception as e:
            self.log(f"Lỗi khi ghi dữ liệu Check quyền lợi: {e}")

    def _write_formulas_properly(self, check_sheet, formula_updates):
        """Ghi công thức VLOOKUP một cách chính xác để tránh dấu '"""
        try:
            # Sử dụng batch_update với valueInputOption là 'USER_ENTERED'
            # để Google Sheets hiểu đây là công thức chứ không phải text
            requests = []

            for update in formula_updates:
                range_name = update['range']
                values = update['values']

                requests.append({
                    'updateCells': {
                        'range': self._range_to_grid_range(check_sheet, range_name),
                        'rows': [
                            {
                                'values': [
                                    {
                                        'userEnteredValue': {
                                            'formulaValue': formula[0]
                                        }
                                    }
                                ]
                            } for formula in values
                        ],
                        'fields': 'userEnteredValue'
                    }
                })

            if requests:
                self.spreadsheet.batch_update({'requests': requests})

        except Exception as e:
            self.log(f"Lỗi khi ghi công thức: {e}")
            # Fallback: sử dụng phương thức cũ
            try:
                for update in formula_updates:
                    range_name = update['range']
                    values = update['values']
                    check_sheet.update(range_name, values, value_input_option='USER_ENTERED')
            except Exception as e2:
                self.log(f"Lỗi fallback: {e2}")

    def _range_to_grid_range(self, sheet, range_name):
        """Chuyển đổi range string thành GridRange object"""
        try:
            # Parse range như "A1:B5"
            parts = range_name.split(':')
            start_cell = parts[0]
            end_cell = parts[1] if len(parts) > 1 else start_cell

            # Chuyển đổi cell thành row/col index
            start_col_idx = col_to_index(start_cell.rstrip('0123456789'))
            start_row_idx = int(start_cell.lstrip('ABCDEFGHIJKLMNOPQRSTUVWXYZ')) - 1

            end_col_idx = col_to_index(end_cell.rstrip('0123456789')) + 1
            end_row_idx = int(end_cell.lstrip('ABCDEFGHIJKLMNOPQRSTUVWXYZ'))

            return {
                'sheetId': sheet.id,
                'startRowIndex': start_row_idx,
                'endRowIndex': end_row_idx,
                'startColumnIndex': start_col_idx,
                'endColumnIndex': end_col_idx
            }
        except Exception as e:
            self.log(f"Lỗi parse range {range_name}: {e}")
            return None

    def update_time_slots_in_rows(self):
        """Cập nhật các khung giờ trong tất cả các dòng điều kiện"""
        time_slots = list(self.time_slot_map.keys())
        for row in self.rows:
            row.update_time_slots(time_slots)

    def save_settings(self):
        """Lưu cài đặt khung giờ, điều kiện và ID input vào file JSON"""
        try:
            # Lấy tên file từ người dùng
            filename, ok = QInputDialog.getText(
                self,
                "Lưu cài đặt",
                "Nhập tên file cài đặt:",
                QLineEdit.EchoMode.Normal,
                f"settings_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )

            if not ok or not filename.strip():
                return

            filename = filename.strip()

            # Thu thập dữ liệu khung giờ
            time_slots = list(self.time_slot_map.keys())

            # Thu thập dữ liệu điều kiện từ tất cả các dòng
            conditions_data = []
            for row in self.rows:
                top_text, ids, selected_times, is_group = row.get_values()
                if ids or selected_times:  # Chỉ lưu dòng có dữ liệu
                    condition = {
                        "top_text": top_text,
                        "ids": ids,
                        "selected_times": selected_times,
                        "is_group": is_group
                    }
                    conditions_data.append(condition)

            # Lưu cài đặt
            success, message = self.settings_manager.save_settings(filename, time_slots, conditions_data)

            if success:
                QMessageBox.information(self, "Thành công", message)
                self.log(f"✓ Đã lưu cài đặt: {filename}")
            else:
                QMessageBox.warning(self, "Lỗi", message)
                self.log(f"✗ Lỗi lưu cài đặt: {message}")

        except Exception as e:
            error_msg = f"Lỗi khi lưu cài đặt: {e}"
            QMessageBox.critical(self, "Lỗi", error_msg)
            self.log(f"✗ {error_msg}")

    def load_settings(self):
        """Tải cài đặt từ file JSON và thay thế tất cả điều kiện hiện tại"""
        try:
            # Lấy danh sách file có sẵn
            available_files = self.settings_manager.get_settings_files()

            if not available_files:
                QMessageBox.information(self, "Thông báo", "Không có file cài đặt nào được tìm thấy.")
                return

            # Hiển thị dialog chọn file
            dialog = SettingsFileDialog(available_files, self.settings_manager, self)
            result = dialog.exec()

            if result == QDialog.DialogCode.Accepted:
                selected_file = dialog.get_selected_file()
                if selected_file:
                    self._load_settings_from_file(selected_file)

        except Exception as e:
            error_msg = f"Lỗi khi tải cài đặt: {e}"
            QMessageBox.critical(self, "Lỗi", error_msg)
            self.log(f"✗ {error_msg}")

    def _load_settings_from_file(self, filename):
        """Tải cài đặt từ file cụ thể"""
        try:
            # Tải dữ liệu từ file
            success, data = self.settings_manager.load_settings(filename)

            if not success:
                QMessageBox.warning(self, "Lỗi", data)
                self.log(f"✗ Lỗi tải cài đặt: {data}")
                return

            # Kiểm tra tương thích khung giờ
            saved_time_slots = data.get("time_slots", [])
            current_time_slots = list(self.time_slot_map.keys())

            if not saved_time_slots:
                QMessageBox.warning(self, "Lỗi", "File cài đặt không chứa thông tin khung giờ.")
                return

            # Cảnh báo nếu khung giờ không khớp
            missing_slots = [slot for slot in saved_time_slots if slot not in current_time_slots]
            if missing_slots:
                reply = QMessageBox.question(
                    self,
                    "Khung giờ không khớp",
                    f"Một số khung giờ trong file cài đặt không có trong sheet hiện tại:\n"
                    f"{', '.join(missing_slots)}\n\n"
                    f"Bạn có muốn tiếp tục tải cài đặt không?\n"
                    f"(Các điều kiện với khung giờ không tồn tại sẽ bị bỏ qua)",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return

            # Xóa tất cả điều kiện hiện tại (bao gồm dòng mặc định)
            for row in self.rows:
                self.row_layout.removeWidget(row)
                row.deleteLater()
            self.rows.clear()

            # Tải các điều kiện từ file
            conditions_data = data.get("conditions", [])
            loaded_count = 0

            for condition in conditions_data:
                # Lọc ra các khung giờ tồn tại
                valid_times = [t for t in condition.get("selected_times", []) if t in current_time_slots]

                if valid_times or not condition.get("selected_times"):  # Cho phép điều kiện không có khung giờ
                    # Tạo dòng điều kiện mới
                    row = ConditionRow(
                        list(self.time_slot_map.keys()),
                        lambda r=None: self.remove_condition_row(r) if r else None
                    )

                    # Thiết lập giá trị
                    row.combo_top.setCurrentText(condition.get("top_text", "Top 10"))
                    row.input_ids.set_id_list(condition.get("ids", []))
                    row.time_selector.set_selected_times(valid_times)
                    row.is_group.setChecked(condition.get("is_group", False))

                    # Thêm vào danh sách
                    self.rows.append(row)
                    self.row_layout.addWidget(row)
                    loaded_count += 1

            # Chỉ thêm dòng mặc định nếu không có điều kiện nào được load
            if not self.rows:
                self.add_condition_row()

            # Thông báo thành công
            success_msg = f"Đã tải thành công {loaded_count} điều kiện từ file: {filename}"
            QMessageBox.information(self, "Thành công", success_msg)
            self.log(f"✓ {success_msg}")

            if missing_slots:
                self.log(f"⚠️ Đã bỏ qua các khung giờ không tồn tại: {', '.join(missing_slots)}")

        except Exception as e:
            error_msg = f"Lỗi khi xử lý file cài đặt: {e}"
            QMessageBox.critical(self, "Lỗi", error_msg)
            self.log(f"✗ {error_msg}")

    def process_rows(self):
        # Kiểm tra cột đầu tiên trước khi xử lý
        first_col = self.first_col_input.text().strip().upper()
        if not re.fullmatch(r"[A-Z]+", first_col):
            QMessageBox.warning(self, "Lỗi", "Cột đầu tiên không hợp lệ")
            return

        # Hiển thị thông tin từ Deal List để người dùng kiểm tra
        self.log("\n=== THÔNG TIN DEAL LIST ĐANG SỬ DỤNG ===")
        self.log(f"Tổng số ID trong Deal List: {len(self.deal_list_manager.id_to_cluster)}")

        # Đếm số ID có review
        review_count = sum(1 for id_val in self.deal_list_manager.id_to_cluster
                        if self.deal_list_manager.has_review(id_val))
        self.log(f"Số ID có review: {review_count}")

        # Đếm theo khung giờ
        timeline_counts = {}
        for id_val, timeline in self.deal_list_manager.id_to_timeline.items():
            if timeline:
                timelines = [t.strip().lower() for t in timeline.split(',')]
                for t in timelines:
                    if t:
                        timeline_counts[t] = timeline_counts.get(t, 0) + 1

        self.log("Phân bố ID theo khung giờ:")
        for timeline in sorted(timeline_counts.keys()):
            count = timeline_counts.get(timeline, 0)
            self.log(f"  - {timeline}: {count} ID")

        # Hiển thị phân bố NO và số review
        no_counts = {}
        no_review_counts = {}
        for id_val, no_value in self.deal_list_manager.id_to_no.items():
            if no_value > 0:  # Chỉ đếm NO > 0
                no_counts[no_value] = no_counts.get(no_value, 0) + 1
                if self.deal_list_manager.has_review(id_val):
                    no_review_counts[no_value] = no_review_counts.get(no_value, 0) + 1

        self.log("Phân bố ID theo NO trong Deal List:")
        for no_value in sorted(no_counts.keys()):
            count = no_counts.get(no_value, 0)
            review_count = no_review_counts.get(no_value, 0)
            self.log(f"  - NO {no_value}: {count} ID, trong đó {review_count} ID có review")

        self.log("=======================================\n")

        # Lấy tất cả dữ liệu từ sheet
        all_values = self.sheet.get_all_values()

        # Phát hiện vị trí cột dựa vào header - phương pháp mới
        header_columns = self.detect_columns_by_headers(all_values)

        # Kiểm tra xem có header "Shop ID" và "Item ID" không
        if "Shop ID" in header_columns and "Item ID" in header_columns:
            # Sử dụng header để tìm cột Item ID
            item_id_col = header_columns["Item ID"]
            self.log(f"Đã phát hiện cột Item ID theo header: {item_id_col}")

            # Sử dụng cột Item ID làm cột đầu tiên
            first_col = item_id_col

            # Ghi đè giá trị trong ô input
            self.first_col_input.setText(first_col)

            # Thông báo cho người dùng
            self.log(f"Tự động cập nhật cột đầu tiên thành: {first_col} dựa trên header 'Item ID'")

        # Tạo danh sách cột ID dựa trên cột đầu tiên (B, G, L, ...) hoặc cột đã phát hiện
        self.id_columns = self.generate_id_columns(first_col)

        self.log(f"Các cột ID sẽ sử dụng: {', '.join(self.id_columns)}")

        # Cập nhật time_slot_map với cột tương ứng
        updated_time_slot_map = {}

        # Lấy khung giờ từ dòng TIME_SLOT_ROW (dòng 2)
        # INDEX 0-BASED: dòng 2 trong Google Sheets = index 1 trong Python
        timeslot_row = all_values[TIME_SLOT_ROW - 1] if len(all_values) >= TIME_SLOT_ROW else []
        print(f"Debug - Load khung giờ từ dòng thực tế: {TIME_SLOT_ROW} (index Python: {TIME_SLOT_ROW - 1})")

        # Tổng hợp thông tin từ dòng TIME_SLOT_ROW
        all_time_slots = []

        self.log(f"Debug - Đọc dữ liệu từ dòng {TIME_SLOT_ROW} (khung giờ và tên giờ)")

        # In ra nội dung của dòng 2 để kiểm tra
        if len(timeslot_row) > 0:
            self.log(f"Debug - Nội dung dòng 2: {timeslot_row[:10]}")

        # Lấy tất cả các khung giờ có trong dòng 2
        # Cấu trúc phức tạp: Tên giờ (Giờ 3) và khung giờ (14:00-15:00) nằm trong cùng một dòng
        # Nhưng ở các ô khác nhau

        # Hiển thị thông tin về cấu trúc sheet
        self.log(f"Debug - Cấu trúc dòng: TIME_SLOT_ROW={TIME_SLOT_ROW}, HEADER_ROW={HEADER_ROW}, DATA_START_ROW={DATA_START_ROW}")
        for col_idx in range(len(timeslot_row)):
            cell_value = timeslot_row[col_idx].strip() if col_idx < len(timeslot_row) else ""

            if not cell_value:
                continue

            # Kiểm tra xem đây là tên giờ (Giờ 3) hay khung giờ (14:00-15:00)
            is_timeslot = bool(re.search(r'\d+:\d+', cell_value))
            is_hour_name = bool(re.search(r'Giờ\s+\d+', cell_value, re.IGNORECASE))

            if is_timeslot:
                # Đây là khung giờ (14:00-15:00)
                time_value = cell_value
                # Tìm tên giờ trong cùng cột
                hour_name = ""
                # Thông thường, tên giờ (Giờ 3) nằm ở ô bên trên khung giờ (14:00-15:00)
                # Nhưng vì cả hai đều nằm ở dòng 2, chúng ta cần phát hiện tên giờ từ các cột gần đó

                # Cách 1: Kiểm tra cột bên trái
                if col_idx > 0:
                    left_cell = timeslot_row[col_idx - 1].strip()
                    if re.search(r'Giờ\s+\d+', left_cell, re.IGNORECASE):
                        hour_name = left_cell

                # Cách 2: Kiểm tra bản thân ô, phòng trường hợp ô chứa cả "Giờ 3 14:00-15:00"
                if not hour_name and re.search(r'Giờ\s+\d+', cell_value, re.IGNORECASE):
                    # Tách tên giờ và khung giờ
                    match = re.search(r'(Giờ\s+\d+).*?(\d+:\d+)', cell_value, re.IGNORECASE)
                    if match:
                        hour_name = match.group(1).strip()
                        time_value = cell_value[match.end(1):].strip()

                self.log(f"Debug - Tìm thấy khung giờ '{time_value}' ở cột {index_to_col(col_idx)}")
                if hour_name:
                    self.log(f"Debug - Liên kết với tên '{hour_name}'")

                all_time_slots.append((col_idx, time_value, hour_name))
            elif is_hour_name and not any(col_idx == slot[0] for slot in all_time_slots):
                # Đây là tên giờ (Giờ 3) mà chưa liên kết với khung giờ nào
                # Ghi nhớ để duyệt trong vòng lặp tiếp theo
                hour_name = cell_value

                # Cách 1: Kiểm tra cột bên phải
                time_value = ""
                if col_idx + 1 < len(timeslot_row):
                    right_cell = timeslot_row[col_idx + 1].strip()
                    if re.search(r'\d+:\d+', right_cell):
                        time_value = right_cell

                if time_value:
                    self.log(f"Debug - Tìm thấy tên giờ '{hour_name}' ở cột {index_to_col(col_idx)}, liên kết với khung giờ '{time_value}'")
                    all_time_slots.append((col_idx + 1, time_value, hour_name))

        self.log(f"Debug - Tất cả khung giờ trong sheet: {[ts[1] for ts in all_time_slots]}")

        # Lấy thông tin khung giờ từ dòng 2 với vị trí thực tế
        timeslots_in_row = []
        for col_idx, cell_value in enumerate(timeslot_row):
            if re.search(r'\d+:\d+', cell_value):
                # Chuẩn hóa khung giờ
                clean_value = cell_value.strip()
                if re.search(r'\d+:\d+\s+\d+:\d+', clean_value):
                    parts = clean_value.split()
                    if len(parts) >= 2:
                        start_time = parts[0].strip()
                        end_time = parts[1].strip()
                        clean_value = f"{start_time}-{end_time}"

                timeslots_in_row.append((col_idx, clean_value))
                self.log(f"Phát hiện khung giờ '{clean_value}' ở cột {index_to_col(col_idx)} (vị trí thực tế: {col_idx})")

        # In thông tin về các khung giờ đã tìm thấy
        self.log(f"Tổng số khung giờ tìm thấy trong dòng 2: {len(timeslots_in_row)}")

        # Ánh xạ khung giờ với cột dựa trên vị trí thực tế (TÔN TRỌNG VỊ TRÍ GỐC)
        # Sắp xếp theo vị trí cột thay vì theo thời gian
        sorted_by_position = sorted(timeslots_in_row, key=lambda x: x[0])

        # Khởi tạo map từ khung giờ đến cột
        timeslot_to_column = {}

        # Tạo ánh xạ khung giờ -> cột ID dựa trên vị trí thực tế
        for col_idx, timeslot in sorted_by_position:
            # Sử dụng vị trí thực tế của khung giờ
            time_col = index_to_col(col_idx)           # cột khung giờ (vị trí thực tế)
            id_col = index_to_col(col_idx + 1)         # cột Item ID (cột tiếp theo)
            timeslot_to_column[timeslot] = id_col
            self.log(f"Ánh xạ khung giờ '{timeslot}' (vị trí gốc {time_col}) với cột ID '{id_col}' - TÔN TRỌNG VỊ TRÍ GỐC")

            # Cập nhật time_slot_map
            updated_time_slot_map[timeslot] = id_col

        # Xử lý chuẩn hóa và mapping cho các khung giờ theo vị trí thực tế
        for col_idx, time_value in sorted_by_position:
            id_col = timeslot_to_column[time_value]
            hour_name = ""  # Khởi tạo với giá trị mặc định

            if time_value:
                # Chuẩn hóa định dạng khung giờ (nếu cần)
                # Ví dụ: "16:00 17:00" -> "16:00-17:00"
                normalized_time = time_value

                # Xử lý trường hợp định dạng "16:00 17:00" -> "16:00-17:00"
                if re.search(r'\d+:\d+\s+\d+:\d+', time_value):
                    parts = time_value.split()
                    if len(parts) >= 2:
                        start_time = parts[0].strip()
                        end_time = parts[1].strip()
                        normalized_time = f"{start_time}-{end_time}"

                # Đảm bảo chúng ta gán cột ID vào time_slot_map
                updated_time_slot_map[normalized_time] = id_col

                # Thêm log chi tiết
                if hour_name:
                    self.log(f"Debug - Map khung giờ '{normalized_time}' ({hour_name}) với cột ID '{id_col}' (vị trí gốc: {index_to_col(col_idx)})")
                else:
                    self.log(f"Debug - Map khung giờ '{normalized_time}' với cột ID '{id_col}' (vị trí gốc: {index_to_col(col_idx)})")

                # Lưu thêm định dạng gốc để đối chiếu sau này
                if normalized_time != time_value:
                    updated_time_slot_map[time_value] = id_col
                    self.log(f"Debug - Đã chuẩn hóa khung giờ '{time_value}' thành '{normalized_time}'")

                # Lưu cả tên giờ (Giờ 1, Giờ 2, ...) nếu có
                if hour_name:
                    updated_time_slot_map[hour_name] = id_col
                    self.log(f"Debug - Đã map tên '{hour_name}' với cột ID '{id_col}'")

        # Cập nhật time_slot_map
        self.time_slot_map = updated_time_slot_map

        # Debug: In ra time_slot_map để kiểm tra chi tiết
        self.log(f"Đã cập nhật time_slot_map (chi tiết):")
        for ts, col in self.time_slot_map.items():
            self.log(f"  Khung giờ: '{ts}' -> Cột: '{col}'")

        # Kiểm tra xem có khung giờ nào không được map không
        empty_mappings = [time_slot for time_slot, col in self.time_slot_map.items() if not col]
        if empty_mappings:
            self.log(f"Cảnh báo: Các khung giờ sau không được map với cột ID nào: {', '.join(empty_mappings)}")

        # Cập nhật lại các time slots trong dòng điều kiện
        self.update_time_slots_in_rows()

        if not self.time_slot_map:
            QMessageBox.warning(self, "Lỗi", "Không tìm thấy khung giờ nào ở vị trí cột đã chọn")
            return

        worksheet = self.sheet
        all_values = worksheet.get_all_values()

        # Sử dụng danh sách cột ID dựa trên vị trí thực tế
        # Thu thập các cột ID từ time_slot_map đã được cập nhật theo thứ tự vị trí
        actual_id_columns = []
        timeslots_by_position = [timeslot for _, timeslot in sorted_by_position]
        for timeslot in timeslots_by_position:
            column = updated_time_slot_map.get(timeslot, "")
            if column:
                actual_id_columns.append(column)

        # Cập nhật id_columns với các cột ID thực tế theo thứ tự vị trí gốc
        if actual_id_columns:
            self.id_columns = actual_id_columns
            self.log(f"Đã cập nhật danh sách cột ID theo vị trí gốc: {', '.join(self.id_columns)}")

        # Đọc dữ liệu đầy đủ từ tất cả các nhóm cột
        col_data = {}
        col_shop_data = {}  # Lưu trữ dữ liệu Shop ID
        col_stt_data = {}   # Lưu trữ dữ liệu STT
        col_brand_data = {} # Lưu trữ dữ liệu tên Brand

        self.log("\n=== BẮT ĐẦU ĐỌC DỮ LIỆU TỪ SHEET BASKET ===")

        # Duyệt qua các khung giờ theo thứ tự vị trí gốc
        for timeslot in timeslots_by_position:
            column_info = self.timeslot_columns_map.get(timeslot, {})
            if not column_info:
                self.log(f"Bỏ qua khung giờ {timeslot}: Không tìm thấy thông tin cột")
                continue

            # Lấy thông tin các cột
            item_id_col = column_info.get("item_id_column", "")
            shop_id_col = column_info.get("shop_id_column", "")
            stt_col = column_info.get("stt_column", "")
            brand_col = column_info.get("brand_column", "")

            self.log(f"\n--- ĐỌC DỮ LIỆU KHUNG GIỜ {timeslot} ---")
            self.log(f"  Cột Item ID: {item_id_col}, Shop ID: {shop_id_col}, STT: {stt_col}, Brand: {brand_col}")

            if not item_id_col:
                self.log(f"  Bỏ qua: Không tìm thấy cột Item ID")
                continue

            # Xử lý cột Item ID
            try:
                col_idx = col_to_index(item_id_col)

                # Lấy dữ liệu từ cột này bắt đầu từ dòng DATA_START_ROW
                data = [row[col_idx] if len(row) > col_idx else '' for row in all_values[DATA_START_ROW - 1:]]
                # Lọc bỏ các giá trị trống
                data = [id_val for id_val in data if id_val.strip()]
                col_data[item_id_col] = data

                # Kiểm tra số ID có review trong cột hiện tại
                review_count = 0
                no_distribution = {}

                for id_val in data:
                    if id_val in self.deal_list_manager.id_to_timeline:
                        timeline = self.deal_list_manager.id_to_timeline.get(id_val, "").lower()
                        if timeslot.lower().strip() in timeline:
                            # Kiểm tra review
                            if self.deal_list_manager.has_review(id_val):
                                review_count += 1
                                # Lấy NO để thống kê
                                no_value = self.deal_list_manager.id_to_no.get(id_val, 0)
                                no_distribution[no_value] = no_distribution.get(no_value, 0) + 1
            except Exception as e:
                self.log(f"  Lỗi khi đọc dữ liệu từ cột Item ID {item_id_col}: {str(e)}")
                col_data[item_id_col] = []

            # Xử lý cột Shop ID (cùng cột với khung giờ nhưng bắt đầu từ dòng DATA_START_ROW)
            if shop_id_col:
                try:
                    shop_col_idx = col_to_index(shop_id_col)
                    # Lấy dữ liệu từ cột Shop ID (cột A, F, K,... từ dòng DATA_START_ROW)
                    shop_data = [row[shop_col_idx] if len(row) > shop_col_idx else ''
                              for row in all_values[DATA_START_ROW - 1:]]
                    # Lọc bỏ các giá trị trống
                    shop_data = [shop_id for shop_id in shop_data if shop_id.strip()]
                    col_shop_data[item_id_col] = shop_data

                    # In một số Shop ID đầu tiên để kiểm tra
                    sample_shop_ids = shop_data[:3] if len(shop_data) >= 3 else shop_data
                    self.log(f"  Đã đọc {len(shop_data)} Shop ID. Mẫu: {sample_shop_ids}")
                except Exception as e:
                    self.log(f"  Lỗi khi đọc dữ liệu Shop ID: {str(e)}")
                    col_shop_data[item_id_col] = []

            # Xử lý cột STT
            if stt_col:
                try:
                    stt_col_idx = col_to_index(stt_col)
                    # Lấy dữ liệu từ cột STT
                    stt_data = [row[stt_col_idx] if len(row) > stt_col_idx else ''
                             for row in all_values[DATA_START_ROW - 1:]]
                    # Lọc bỏ các giá trị trống
                    stt_data = [stt for stt in stt_data if stt.strip()]
                    col_stt_data[item_id_col] = stt_data
                    self.log(f"  Đã đọc {len(stt_data)} STT từ cột {stt_col}")
                except Exception as e:
                    self.log(f"  Lỗi khi đọc dữ liệu STT: {str(e)}")
                    col_stt_data[item_id_col] = []


            # Xử lý cột tên Brand
            if brand_col:
                try:
                    brand_col_idx = col_to_index(brand_col)
                    # Lấy dữ liệu từ cột Brand
                    brand_data = [row[brand_col_idx] if len(row) > brand_col_idx else ''
                               for row in all_values[DATA_START_ROW - 1:]]
                    # Lọc bỏ các giá trị trống
                    brand_data = [brand for brand in brand_data if brand.strip()]
                    col_brand_data[item_id_col] = brand_data

                    # In một số tên Brand đầu tiên để kiểm tra
                    sample_brands = brand_data[:3] if len(brand_data) >= 3 else brand_data
                    self.log(f"Đã đọc {len(brand_data)} tên Brand từ cột {brand_col}. Mẫu: {sample_brands}")
                except Exception as e:
                    self.log(f"  Lỗi khi đọc dữ liệu Brand: {str(e)}")
                    col_brand_data[item_id_col] = []

        self.log("=== KẾT THÚC ĐỌC DỮ LIỆU ===\n")

        # Lưu tất cả dữ liệu vào thuộc tính để truy cập sau này
        self.col_data = col_data
        self.col_shop_data = col_shop_data
        self.col_stt_data = col_stt_data
        self.col_brand_data = col_brand_data

        # Tạo các TimeSlotProcessor cho từng khung giờ
        time_slot_processors = {}
        for time_slot, col in self.time_slot_map.items():
            # Lấy danh sách ID Exclusive cho khung giờ này
            exclusive_ids = self.exclusive_ids.get(time_slot, [])

            processor = TimeSlotProcessor(time_slot, col, self.deal_list_manager, exclusive_ids)
            processor.current_ids = col_data.get(col, []).copy()

            # Lưu vị trí hiện tại của các ID Exclusive
            if exclusive_ids:
                for idx, id_val in enumerate(processor.current_ids):
                    if id_val in exclusive_ids:
                        processor.exclusive_positions[id_val] = idx

                # Log thông tin về các ID Exclusive được tìm thấy
                found_exclusive = [id_val for id_val in exclusive_ids if id_val in processor.current_ids]
                if found_exclusive:
                    self.log(f"Khung giờ {time_slot}: Tìm thấy {len(found_exclusive)}/{len(exclusive_ids)} ID Exclusive trong dữ liệu hiện tại")

            time_slot_processors[time_slot] = processor

        # Phân phối các điều kiện cho từng khung giờ với logic rotation
        for row in self.rows:
            top_text, ids, time_slots, is_group = row.get_values()
            if not ids or not time_slots:
                continue

            # Áp dụng logic rotation nếu có >3 IDs
            if len(ids) > 3:
                self.log(f"Áp dụng rotation cho {len(ids)} IDs vào {len(time_slots)} khung giờ")
                rotated_ids = self._apply_rotation_logic(ids, time_slots)

                # Phân phối ID đã rotation cho từng khung giờ
                for i, time_slot in enumerate(time_slots):
                    if time_slot in time_slot_processors:
                        slot_ids = rotated_ids[i]
                        self.log(f"  {time_slot}: {len(slot_ids)} IDs - {slot_ids}")
                        time_slot_processors[time_slot].add_condition(top_text, slot_ids, is_group)
            else:
                # Logic cũ: ≤3 IDs thì input toàn bộ vào tất cả khung giờ
                self.log(f"Input toàn bộ {len(ids)} IDs vào {len(time_slots)} khung giờ (≤3 IDs)")
                for time_slot in time_slots:
                    if time_slot in time_slot_processors:
                        time_slot_processors[time_slot].add_condition(top_text, ids, is_group)

        # Xử lý từng khung giờ
        for time_slot, processor in time_slot_processors.items():
            self.log(f"\nXử lý khung giờ: {time_slot}")

            # Đếm số ID có Review thuộc khung giờ này
            review_count = sum(1 for id_val in processor.current_ids
                             if self.deal_list_manager.has_review(id_val) and
                             self.deal_list_manager.is_id_in_timeline(id_val, time_slot))

            # KIỂM TRA THÊM NO 1
            no1_ids = []
            for id_val in processor.current_ids:
                if (self.deal_list_manager.has_review(id_val) and
                    self.deal_list_manager.is_id_in_timeline(id_val, time_slot) and
                    self.deal_list_manager.id_to_no.get(id_val, 0) == 1):
                    no1_ids.append(id_val)

            # Hiển thị thông tin chi tiết về ID với NO 1
            if no1_ids:
                self.log(f"- ✓ Tìm thấy {len(no1_ids)} ID có NO = 1 thuộc khung giờ này: {', '.join(no1_ids)}")
            else:
                self.log(f"- ⚠️ CẢNH BÁO: Không tìm thấy ID nào có NO = 1 trong khung giờ {time_slot}!")
                # Kiểm tra xem các ID NO 1 có thuộc timeline này không
                all_no1_ids = [id_val for id_val, no in self.deal_list_manager.id_to_no.items() if no == 1]

                # Kiểm tra chi tiết các ID NO = 1
                self.log(f"  Kiểm tra chi tiết {len(all_no1_ids)} ID có NO = 1 từ Deal List:")
                for id_val in all_no1_ids[:10]:  # Giới hạn hiển thị tối đa 10 ID
                    timeline_orig = self.deal_list_manager.id_to_timeline.get(id_val, "không có")
                    timeline_norm = self.deal_list_manager.id_to_normalized_timeline.get(id_val, "không có")
                    is_in_timeline = self.deal_list_manager.is_id_in_timeline(id_val, time_slot)
                    has_review = self.deal_list_manager.has_review(id_val)

                    self.log(f"  - ID {id_val}: NO=1, Timeline='{timeline_orig}', Timeline chuẩn hóa='{timeline_norm}'")
                    self.log(f"    Thuộc timeline {time_slot}: {is_in_timeline}, Có review: {has_review}")

            # Hiển thị thông tin về Review và NO
            self.log(f"- Khung giờ {time_slot} có {len(processor.current_ids)} ID, trong đó có {review_count} ID có giá trị Review.")
            self.log(f"- Các ID Review sẽ được sắp xếp theo thứ tự NO (cột {self.column_mapping['no_column']}) và được ưu tiên vị trí cao.")

            # Xử lý processor trước khi cập nhật Google Sheet
            self.log(f"\n=== BẮT ĐẦU XỬ LÝ KHUNG GIỜ {time_slot} ===")
            self.log(f"Đang quét dữ liệu chi tiết các dòng cho khung giờ này...")

            # Hiển thị thông tin dòng từ timeline_rows
            if time_slot in self.deal_list_manager.timeline_rows:
                rows = self.deal_list_manager.timeline_rows[time_slot]
                min_row = min(rows) if rows else 0
                max_row = max(rows) if rows else 0
                self.log(f"Tìm thấy {len(rows)} dòng cho khung giờ {time_slot} (từ dòng {min_row} đến {max_row})")

                # Hiển thị một số dòng đầu tiên để tham khảo
                if len(rows) > 0:
                    self.log("Thông tin một số dòng đầu tiên:")
                    row_samples = sorted(rows)[:3]  # Lấy 3 dòng đầu tiên

                    # Hiển thị thông tin chi tiết cho từng dòng
                    for row_num in row_samples:
                        row_data = self.deal_list_manager.get_timeline_row_data(time_slot, row_num)
                        if row_data:
                            item_id = row_data.get('item_id', '')
                            review_m = row_data.get('review_m', '')
                            review_n = row_data.get('review_n', '')
                            no_value = row_data.get('no', '')
                            has_review = bool(review_m or review_n)
                            self.log(f"  Dòng {row_num}: ID={item_id}, NO={no_value}, Có review: {has_review}")
            else:
                normalized_time_slot = normalize_timeline(time_slot)
                if normalized_time_slot in self.deal_list_manager.timeline_rows:
                    rows = self.deal_list_manager.timeline_rows[normalized_time_slot]
                    min_row = min(rows) if rows else 0
                    max_row = max(rows) if rows else 0
                    self.log(f"Tìm thấy {len(rows)} dòng cho khung giờ {normalized_time_slot} (từ dòng {min_row} đến {max_row})")

                    # Hiển thị một số dòng đầu tiên để tham khảo
                    if len(rows) > 0:
                        self.log("Thông tin một số dòng đầu tiên:")
                        row_samples = sorted(rows)[:3]  # Lấy 3 dòng đầu tiên

                        # Hiển thị thông tin chi tiết cho từng dòng
                        for row_num in row_samples:
                            row_data = self.deal_list_manager.get_timeline_row_data(normalized_time_slot, row_num)
                            if row_data:
                                item_id = row_data.get('item_id', '')
                                review_m = row_data.get('review_m', '')
                                review_n = row_data.get('review_n', '')
                                no_value = row_data.get('no', '')
                                has_review = bool(review_m or review_n)
                                self.log(f"  Dòng {row_num}: ID={item_id}, NO={no_value}, Có review: {has_review}")
                else:
                    self.log(f"Không tìm thấy dữ liệu dòng nào cho khung giờ {time_slot}")

            original_count = len(processor.current_ids)
            processor.process()
            final_count = len(processor.current_ids)

            if original_count > final_count:
                self.log(f"Đã cắt giảm từ {original_count} ID xuống còn {final_count} ID sau khi lọc thông minh theo GMV")

            # Cập nhật dữ liệu lên Google Sheet
            try:
                # Tạo danh sách ID cuối cùng với các vị trí trống
                max_len = max(len(processor.current_ids), len(all_values) - (DATA_START_ROW - 1))
                final_column_data = [''] * max_len

                # Đặt các ID vào đúng vị trí
                for i, id_val in enumerate(processor.current_ids):
                    if i < max_len:
                        final_column_data[i] = id_val

                # Tạo range để cập nhật Google Sheet
                range_start = f"{processor.column}{DATA_START_ROW}"
                range_end = f"{processor.column}{DATA_START_ROW + max_len - 1}"
                cell_range = f"{range_start}:{range_end}"

                self.log(f"\n=== CẬP NHẬT SHEET BASKET ===")
                self.log(f"Khung giờ: {time_slot}")
                self.log(f"Cột: {processor.column}")
                self.log(f"Range: {cell_range}")
                self.log(f"Số lượng ID: {len(processor.current_ids)}")

                # Phân tích số lượng review trong kết quả
                result_review_count = sum(1 for id_val in processor.current_ids
                                        if self.deal_list_manager.has_review(id_val))
                self.log(f"Số ID có review: {result_review_count}")

                # In ra một số ID review đầu tiên
                review_ids = [id_val for id_val in processor.current_ids
                            if self.deal_list_manager.has_review(id_val)][:5]
                if review_ids:
                    self.log(f"Một số ID review đầu tiên: {', '.join(review_ids)}")

                # Phân tích theo NO
                no_count_in_result = {}
                for id_val in processor.current_ids:
                    no_value = self.deal_list_manager.id_to_no.get(id_val, 0)
                    if no_value > 0:  # Chỉ đếm NO > 0
                        no_count_in_result[no_value] = no_count_in_result.get(no_value, 0) + 1

                if no_count_in_result:
                    self.log("Phân bố ID theo NO trong kết quả:")
                    for no_value in sorted(no_count_in_result.keys()):
                        count = no_count_in_result[no_value]
                        self.log(f"  - NO {no_value}: {count} ID")

                # Lưu dữ liệu đã xử lý thay vì upload ngay
                self.processed_data[time_slot] = {
                    'processor': processor,
                    'final_column_data': final_column_data,
                    'cell_range': cell_range,
                    'max_len': max_len
                }

                self.log(f"Đã xử lý xong khung giờ {time_slot}")
                self.log(f"Cột: {processor.column}")
                self.log(f"Range: {cell_range}")
                self.log(f"Số lượng ID: {len(processor.current_ids)}")

                # LƯU VỊ TRÍ ID KHÔNG CẬP NHẬT TIMELINE
                for i, id_val in enumerate(processor.current_ids):
                    if id_val.strip():
                        # Lưu vị trí để sử dụng trong tương lai
                            if time_slot not in self.fixed_positions:
                                self.fixed_positions[time_slot] = {}
                                self.fixed_positions[time_slot][id_val] = i

            except Exception as e:
                self.log(f"Lỗi khi xử lý cột {processor.column} (khung giờ {time_slot}): {e}")

        # Mở trực tiếp Processing Results Dialog
        self.open_processing_results()

        self.log("\n=== HOÀN THÀNH XỬ LÝ ===")
        self.log("Cửa sổ Processing Results đã mở!")
        self.log("Chọn khung giờ, chỉnh sửa nếu cần, và upload.")
        self.log("=====================================\n")

    def open_processing_results(self):
        """Mở cửa sổ Processing Results (thay thế Preview & Approval)"""
        if not self.processed_data:
            QMessageBox.warning(self, "Lỗi", "Chưa có dữ liệu đã xử lý. Vui lòng chạy 'Start processing' trước.")
            return

        # Tạo Processing Results Dialog mới
        self.processing_results_dialog = ProcessingResultsDialog(self, self.processed_data)
        self.processing_results_dialog.show()
        self.processing_results_dialog.raise_()
        self.processing_results_dialog.activateWindow()

    def show_column_mapping_dialog(self):
        """Hiển thị dialog ánh xạ cột để người dùng có thể điều chỉnh"""
        dialog = ColumnMappingDialog(self.column_mapping, self)
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            self.column_mapping = dialog.get_updated_mapping()
            self.log(f"Đã cập nhật ánh xạ cột: ID={self.column_mapping['id_column']}, "
                    f"Cluster={self.column_mapping['cluster_column']}, "
                    f"NMV={self.column_mapping['nmv_column']}, "
                    f"Timeline={self.column_mapping['timeline_column']}, "
                    f"Shop ID={self.column_mapping['shop_id_column']}, "
                    f"Review M={self.column_mapping['review_m_column']}, "
                    f"Review N={self.column_mapping['review_n_column']}, "
                    f"NO={self.column_mapping['no_column']}, "
                    f"Brand={self.column_mapping['brand_column']}")

            # Nếu đã load Deal list, cập nhật lại dữ liệu
            if hasattr(self, 'spreadsheet') and self.spreadsheet:
                deal_list_sheet = None
                try:
                    deal_list_sheet = self.spreadsheet.worksheet('Deal list')
                except:
                    self.log("Không tìm thấy sheet 'Deal list' để cập nhật.")
                    return

                if deal_list_sheet:
                    # Cập nhật lại Deal list với ánh xạ cột mới
                    self.log("\n=== CẬP NHẬT LẠI DỮ LIỆU DEAL LIST ===")
                    self.deal_list_manager.set_log_function(self.log)
                    success, message = self.deal_list_manager.load_data_with_column_positions(deal_list_sheet, self.column_mapping)
                    if success:
                        self.log(message)
                    else:
                        self.log(f"Lỗi khi cập nhật Deal list: {message}")

    def show_exclusive_dialog(self):
        """Hiển thị dialog quản lý ID Exclusive"""
        if not self.time_slot_map:
            QMessageBox.warning(self, "Cảnh báo", "Vui lòng load Google Sheet trước để lấy danh sách khung giờ.")
            return

        # Debug: In ra time_slot_map hiện tại để kiểm tra
        print(f"Debug - time_slot_map hiện tại:")
        for ts, col in self.time_slot_map.items():
            print(f"  Khung giờ: '{ts}' -> Cột: '{col}'")

        # Tạo ExclusiveIDsDialog với các khung giờ hiện có và map ID exclusive
        dialog = ExclusiveIDsDialog(list(self.time_slot_map.keys()), self.exclusive_ids, self)
        result = dialog.exec()

        if result == QDialog.DialogCode.Accepted:
            # Cập nhật map ID exclusive
            self.exclusive_ids = dialog.get_updated_exclusive_ids()

            # Hiển thị thông tin về ID exclusive đã thiết lập
            message = "Đã thiết lập ID Exclusive cho các khung giờ:\n"
            for time_slot, ids in self.exclusive_ids.items():
                message += f"- {time_slot}: {len(ids)} ID\n"

            self.log(message)
            QMessageBox.information(self, "Exclusive Mode", "Đã thiết lập các ID Exclusive thành công.")

# Lớp dialog để chọn Cluster cần xóa
class ClusterSelectionDialog(QDialog):
    def __init__(self, clusters, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Chọn Cluster cần xóa")
        self.setMinimumWidth(400)

        # Lưu trữ các cluster và checkbox tương ứng
        self.clusters = clusters
        self.cluster_checkboxes = {}

        # Layout chính
        self.layout = QVBoxLayout()

        # Label hướng dẫn
        self.label = QLabel("Đã vượt quá giới hạn 500 ID. Vui lòng chọn các Cluster mà bạn muốn xóa ID (ưu tiên xóa ID có 30D NMV thấp nhất trong các Cluster đã chọn):")
        self.label.setWordWrap(True)
        self.layout.addWidget(self.label)

        # Vùng cuộn cho danh sách cluster
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout()

        # Thêm các cluster vào danh sách
        for cluster in sorted(clusters):
            checkbox = QCheckBox(cluster)
            self.cluster_checkboxes[cluster] = checkbox
            self.scroll_layout.addWidget(checkbox)

        # Buttons chọn tất cả / bỏ chọn tất cả
        button_layout = QHBoxLayout()
        self.select_all_button = QPushButton("Chọn tất cả")
        self.select_all_button.clicked.connect(self.select_all)
        self.clear_all_button = QPushButton("Bỏ chọn tất cả")
        self.clear_all_button.clicked.connect(self.clear_all)

        button_layout.addWidget(self.select_all_button)
        button_layout.addWidget(self.clear_all_button)
        self.scroll_layout.addLayout(button_layout)

        self.scroll_widget.setLayout(self.scroll_layout)
        self.scroll_area.setWidget(self.scroll_widget)
        self.layout.addWidget(self.scroll_area)

        # Nút xác nhận và hủy
        button_box = QHBoxLayout()
        self.ok_button = QPushButton("Xác nhận")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("Hủy")
        self.cancel_button.clicked.connect(self.reject)

        button_box.addWidget(self.ok_button)
        button_box.addWidget(self.cancel_button)
        self.layout.addLayout(button_box)

        self.setLayout(self.layout)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #222;
                color: #EEE;
            }
            QLabel {
                color: #EEE;
            }
            QCheckBox {
                color: #EEE;
                padding: 5px;
            }
            QCheckBox:hover {
                background-color: #333;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 6px;
                border-radius: 3px;
                color: #EEE;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)

    def select_all(self):
        for checkbox in self.cluster_checkboxes.values():
            checkbox.setChecked(True)

    def clear_all(self):
        for checkbox in self.cluster_checkboxes.values():
            checkbox.setChecked(False)

    def get_selected_clusters(self):
        return [cluster for cluster, checkbox in self.cluster_checkboxes.items() if checkbox.isChecked()]

# Lớp quản lý dữ liệu Deal list
class DealListManager:
    def __init__(self):
        self.id_to_cluster = {}  # Map từ ID đến Cluster (Category)
        self.id_to_brand = {}    # Map từ ID đến Brand thật (cột U - Nhãn hàng)
        self.id_to_nmv = {}      # Map từ ID đến 30D NMV
        self.id_to_timeline = {} # Map từ ID đến Timeline (khung giờ)
        self.id_to_normalized_timeline = {}  # Map từ ID đến Timeline đã chuẩn hóa
        self.id_to_shop = {}     # Map từ ID đến Shop ID
        self.id_to_review_m = {} # Map từ ID đến giá trị Review ở cột M
        self.id_to_review_n = {} # Map từ ID đến giá trị Review ở cột N
        self.id_to_no = {}       # Map từ ID đến giá trị NO (thứ tự ưu tiên)
        self.clusters = set()    # Set các Cluster duy nhất
        self.brands = set()      # Set các Brand duy nhất
        self.shop_ids = set()    # Set các Shop ID duy nhất
        self.original_timeline_values = {}  # Lưu giá trị gốc của Timeline để tránh mất dữ liệu
        self.original_no_values = {}        # Lưu giá trị gốc của NO để tránh mất dữ liệu
        self.timeline_rows = {}   # Lưu trữ các dòng tương ứng với từng timeline
        self.timeline_row_details = {}  # Lưu trữ thông tin chi tiết {timeline: {row_number: {column_name: value}}}
        self.log_function = None  # Hàm log để gửi log đến UI

    def normalize_vietnamese_text(self, text):
        """Chuyển tiếng Việt có dấu thành không dấu để dễ mapping"""
        if not text:
            return ""

        # Bảng chuyển đổi tiếng Việt
        vietnamese_map = {
            'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
            'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
            'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
            'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
            'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
            'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
            'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
            'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
            'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
            'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
            'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
            'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
            'đ': 'd', 'Đ': 'D'
        }

        result = ""
        for char in text:
            result += vietnamese_map.get(char, char)

        return result

    def set_log_function(self, log_function):
        """Thiết lập hàm log để gửi thông tin đến UI"""
        self.log_function = log_function

    def log(self, message):
        """Gửi log đến UI nếu có hàm log, nếu không thì in ra console"""
        if self.log_function:
            self.log_function(message)
        else:
            print(message)

    def load_data(self, worksheet, log_function=None):
        """Load dữ liệu từ sheet Deal list với vị trí cột mặc định"""
        if log_function:
            self.set_log_function(log_function)

        default_mapping = {
            "id_column": "A",
            "cluster_column": "I",
            "nmv_column": "AC",
            "timeline_column": "R",  # Thêm cột Timeline mặc định
            "shop_id_column": "F",   # Thêm cột Shop ID mặc định
            "review_m_column": "M",  # Thêm cột Review M mặc định
            "review_n_column": "N",  # Thêm cột Review N mặc định
            "no_column": "S",        # Thêm cột NO mặc định
            "brand_column": "U"      # Thêm cột U - Nhãn hàng mặc định
        }
        return self.load_data_with_column_positions(worksheet, default_mapping)

    def load_data_with_column_positions(self, worksheet, column_mapping, log_function=None):
        """Load dữ liệu từ sheet Deal list với vị trí cột đã chỉ định"""
        if log_function:
            self.set_log_function(log_function)

        try:
            # Lấy tất cả dữ liệu từ sheet
            all_values = worksheet.get_all_values()
            self.log(f"Đã lấy {len(all_values)} dòng dữ liệu từ Deal List")

            # Kiểm tra sheet có đủ dữ liệu
            if len(all_values) < DEAL_LIST_DATA_START_ROW:  # Cần ít nhất đủ dòng dữ liệu
                return False, "Sheet không có đủ dữ liệu"

            # Chuyển đổi chữ cái cột sang index (0-based)
            try:
                id_col_idx = col_to_index(column_mapping["id_column"])
                cluster_col_idx = col_to_index(column_mapping["cluster_column"])
                nmv_col_idx = col_to_index(column_mapping["nmv_column"])
                timeline_col_idx = col_to_index(column_mapping["timeline_column"])
                shop_id_col_idx = col_to_index(column_mapping["shop_id_column"])  # Thêm cột Shop ID

                # Thêm các cột Review, NO và Brand
                review_m_col_idx = col_to_index(column_mapping.get("review_m_column", "M"))
                review_n_col_idx = col_to_index(column_mapping.get("review_n_column", "N"))
                no_col_idx = col_to_index(column_mapping.get("no_column", "S"))
                brand_col_idx = col_to_index(column_mapping.get("brand_column", "U"))

                self.log(f"Đã ánh xạ các cột: ID={column_mapping['id_column']}({id_col_idx}), "
                      f"Timeline={column_mapping['timeline_column']}({timeline_col_idx}), "
                      f"Review M={column_mapping.get('review_m_column', 'M')}({review_m_col_idx}), "
                      f"Review N={column_mapping.get('review_n_column', 'N')}({review_n_col_idx}), "
                      f"NO={column_mapping.get('no_column', 'S')}({no_col_idx})")

            except Exception as e:
                return False, f"Lỗi khi chuyển đổi vị trí cột: {str(e)}"

            # Reset dữ liệu
            self.id_to_cluster = {}
            self.id_to_nmv = {}
            self.id_to_timeline = {}
            self.id_to_normalized_timeline = {}
            self.id_to_shop = {}
            self.id_to_review_m = {}
            self.id_to_review_n = {}
            self.id_to_no = {}
            self.clusters = set()
            self.shop_ids = set()
            self.original_timeline_values = {}
            self.original_no_values = {}
            self.timeline_rows = {}
            self.timeline_row_details = {}

            # Parse dữ liệu từ dòng bắt đầu đã được chỉ định
            data_start_index = DEAL_LIST_DATA_START_ROW - 1  # Chuyển từ 1-indexed sang 0-indexed

            # Thêm biến đếm timeline và review
            timeline_counts = {}  # {timeline: count}
            timeline_review_counts = {}  # {timeline: review_count}

            for row_idx, row in enumerate(all_values[data_start_index:], start=data_start_index + 1):
                # Kiểm tra hàng có đủ cột hay không
                max_col_idx = max(id_col_idx, cluster_col_idx, nmv_col_idx, timeline_col_idx,
                                 shop_id_col_idx, review_m_col_idx, review_n_col_idx, no_col_idx)
                if len(row) <= max_col_idx:
                    continue  # Bỏ qua hàng không đủ cột

                item_id = row[id_col_idx].strip()
                cluster = row[cluster_col_idx].strip()

                # Chỉ xử lý nếu ID và Cluster không trống
                if item_id and cluster:
                    # Parse giá trị NMV
                    nmv_value = 0
                    if len(row) > nmv_col_idx and row[nmv_col_idx].strip():
                        try:
                            # Loại bỏ các ký tự không phải số và dấu thập phân
                            nmv_text = re.sub(r'[^\d.]', '', row[nmv_col_idx])
                            nmv_value = float(nmv_text) if nmv_text else 0
                        except ValueError:
                            nmv_value = 0

                    # Lấy giá trị Timeline
                    timeline = ""
                    normalized_timeline = ""
                    if len(row) > timeline_col_idx:
                        timeline = row[timeline_col_idx].strip()
                        # Lưu giá trị Timeline gốc
                        self.original_timeline_values[item_id] = timeline

                        # Chuẩn hóa timeline để dùng cho so sánh
                        if timeline:
                            normalized_timeline = normalize_timeline(timeline)

                        # Đếm số lượng ID theo timeline
                        if timeline:
                            # Có thể có nhiều timeline trong một chuỗi, phân tách chúng
                            timelines = [t.strip().lower() for t in timeline.split(',')]
                            normalized_timelines = [normalize_timeline(t) for t in timelines]

                            # Lưu thông tin dòng cho từng timeline
                            for t_idx, t in enumerate(timelines):
                                if t:
                                    t_clean = t.strip().lower()

                                    # Lưu số dòng cho timeline này
                                    if t_clean not in self.timeline_rows:
                                        self.timeline_rows[t_clean] = []

                                    actual_row_num = row_idx  # Số dòng thực tế trong sheet
                                    self.timeline_rows[t_clean].append(actual_row_num)

                                    # Lưu chi tiết dữ liệu của dòng này
                                    if t_clean not in self.timeline_row_details:
                                        self.timeline_row_details[t_clean] = {}

                                    # Tạo entry mới cho dòng này nếu chưa có
                                    if actual_row_num not in self.timeline_row_details[t_clean]:
                                        self.timeline_row_details[t_clean][actual_row_num] = {}

                                    # Lưu giá trị của các cột quan trọng
                                    row_detail = {
                                        'item_id': item_id,
                                        'shop_id': row[shop_id_col_idx].strip() if len(row) > shop_id_col_idx else "",
                                        'review_m': row[review_m_col_idx].strip() if len(row) > review_m_col_idx else "",
                                        'review_n': row[review_n_col_idx].strip() if len(row) > review_n_col_idx else "",
                                        'no': row[no_col_idx].strip() if len(row) > no_col_idx else "",
                                        'nmv': nmv_value,
                                        'cluster': cluster,
                                        'timeline': timeline
                                    }

                                    self.timeline_row_details[t_clean][actual_row_num] = row_detail

                                    # Làm tương tự cho phiên bản chuẩn hóa
                                    t_normalized = normalized_timelines[t_idx]
                                    if t_normalized and t_normalized != t_clean:
                                        if t_normalized not in self.timeline_rows:
                                            self.timeline_rows[t_normalized] = []
                                        if actual_row_num not in self.timeline_rows[t_normalized]:
                                            self.timeline_rows[t_normalized].append(actual_row_num)

                                        # Lưu chi tiết dữ liệu cho phiên bản chuẩn hóa
                                        if t_normalized not in self.timeline_row_details:
                                            self.timeline_row_details[t_normalized] = {}
                                        if actual_row_num not in self.timeline_row_details[t_normalized]:
                                            self.timeline_row_details[t_normalized][actual_row_num] = row_detail

                            # Đếm timeline
                            for t_idx, t in enumerate(timelines):
                                if t:
                                    t_clean = t.strip().lower()
                                    timeline_counts[t_clean] = timeline_counts.get(t_clean, 0) + 1
                                    # Lưu cả phiên bản chuẩn hóa
                                    t_normalized = normalized_timelines[t_idx]
                                    if t_normalized:
                                        timeline_counts[t_normalized] = timeline_counts.get(t_normalized, 0) + 1

                    # Lấy giá trị Shop ID
                    shop_id = ""
                    if len(row) > shop_id_col_idx:
                        shop_id = row[shop_id_col_idx].strip()
                        if shop_id:
                            self.shop_ids.add(shop_id)

                    # Lấy giá trị Review M
                    review_m = ""
                    if len(row) > review_m_col_idx:
                        review_m = row[review_m_col_idx].strip()

                    # Lấy giá trị Review N
                    review_n = ""
                    if len(row) > review_n_col_idx:
                        review_n = row[review_n_col_idx].strip()

                    # Kiểm tra có review không
                    has_review = bool(review_m or review_n)

                    # Cập nhật đếm review theo timeline
                    if has_review and timeline:
                        timelines = [t.strip().lower() for t in timeline.split(',')]
                        for t in timelines:
                            if t:
                                timeline_review_counts[t] = timeline_review_counts.get(t, 0) + 1

                    # Lấy giá trị NO (cột S)
                    no_value = 0
                    if len(row) > no_col_idx and row[no_col_idx].strip():
                        try:
                            no_text = row[no_col_idx].strip()
                            no_value = int(no_text) if no_text and no_text.isdigit() else 0
                            # Lưu giá trị NO gốc
                            self.original_no_values[item_id] = no_text
                        except ValueError:
                            no_value = 0

                    # Lấy giá trị Brand (cột U - Nhãn hàng)
                    brand = ""
                    if len(row) > brand_col_idx:
                        brand = row[brand_col_idx].strip()
                        # Chuyển về tiếng Việt không dấu để dễ mapping
                        if brand:
                            brand = self.normalize_vietnamese_text(brand)

                    self.id_to_cluster[item_id] = cluster
                    self.id_to_brand[item_id] = brand
                    self.id_to_nmv[item_id] = nmv_value
                    self.id_to_timeline[item_id] = timeline

                    # Lưu timeline đã chuẩn hóa
                    if timeline:
                        # Chuẩn hóa mỗi phần của timeline (nếu có nhiều phần cách nhau bởi dấu ,)
                        normalized_parts = []
                        for part in timeline.split(','):
                            part = part.strip()
                            if part:
                                normalized_parts.append(normalize_timeline(part))
                        self.id_to_normalized_timeline[item_id] = ','.join(normalized_parts)
                    else:
                        self.id_to_normalized_timeline[item_id] = ""

                    self.id_to_shop[item_id] = shop_id  # Lưu giá trị Shop ID
                    self.id_to_review_m[item_id] = review_m  # Lưu giá trị Review M
                    self.id_to_review_n[item_id] = review_n  # Lưu giá trị Review N
                    self.id_to_no[item_id] = no_value  # Lưu giá trị NO
                    self.clusters.add(cluster)
                    self.brands.add(brand)      # Thêm Brand vào set
                    self.shop_ids.add(shop_id)  # Thêm Shop ID vào set

            # In thông tin chi tiết về khung giờ và số lượng ID review
            self.log("\n=== THỐNG KÊ KHUNG GIỜ TỪ DEAL LIST ===")
            for timeline in sorted(timeline_counts.keys()):
                total_count = timeline_counts.get(timeline, 0)
                review_count = timeline_review_counts.get(timeline, 0)

                # Lấy thông tin dòng cho timeline này
                row_range = "không có thông tin"
                if timeline in self.timeline_rows and self.timeline_rows[timeline]:
                    min_row = min(self.timeline_rows[timeline])
                    max_row = max(self.timeline_rows[timeline])
                    row_range = f"từ dòng {min_row} đến dòng {max_row}"

                self.log(f"- Khung giờ {timeline}: {total_count} ID, trong đó có {review_count} ID có review, {row_range}")
            self.log("=====================================\n")

            # Thống kê NO và review
            no_review_counts = {}  # {no_value: review_count}
            for id_val, no_value in self.id_to_no.items():
                if self.has_review(id_val):
                    no_review_counts[no_value] = no_review_counts.get(no_value, 0) + 1

            self.log("=== THỐNG KÊ ID REVIEW THEO NO ===")
            for no_value in sorted(no_review_counts.keys()):
                review_count = no_review_counts.get(no_value, 0)
                self.log(f"- NO {no_value}: {review_count} ID có review")
            self.log("=====================================\n")

            self.log("\n=== THÔNG TIN CHI TIẾT VỀ DỮ LIỆU DEAL LIST ĐÃ QUÉT ===")
            self.log(f"Đã quét từ dòng {DEAL_LIST_DATA_START_ROW} đến dòng {len(all_values)}")
            self.log(f"Tổng số dòng đã quét: {len(all_values) - (DEAL_LIST_DATA_START_ROW - 1)}")

            # Hiển thị tổng số khung giờ đã phát hiện
            self.log(f"\nTổng số khung giờ đã phát hiện: {len(self.timeline_rows)}")
            self.log("\nPhân bố dòng dữ liệu theo timeline:")
            for timeline, rows in self.timeline_rows.items():
                timeline_normalized = normalize_timeline(timeline)
                if timeline == timeline_normalized:
                    self.log(f"- Timeline '{timeline}': {len(rows)} dòng (từ {min(rows)} đến {max(rows)})")
                else:
                    self.log(f"- Timeline '{timeline}' (chuẩn hóa: '{timeline_normalized}'): {len(rows)} dòng (từ {min(rows)} đến {max(rows)})")

            return True, f"Đã load {len(self.id_to_cluster)} ID từ sheet Deal list"

        except Exception as e:
            import traceback
            self.log(f"Stack trace: {traceback.format_exc()}")
            return False, f"Lỗi khi load Deal list: {str(e)}"

    def get_timeline_row_data(self, timeline, row_number=None):
        """
        Trả về dữ liệu chi tiết cho một timeline cụ thể.
        Nếu row_number được chỉ định, chỉ trả về dữ liệu của dòng đó.
        """
        normalized_timeline = normalize_timeline(timeline)

        # Tìm thông tin dòng dựa trên timeline
        timeline_key = timeline.lower().strip()
        normalized_key = normalized_timeline

        # Kiểm tra cả key gốc và key đã chuẩn hóa
        found_key = None
        if timeline_key in self.timeline_row_details:
            found_key = timeline_key
        elif normalized_key in self.timeline_row_details:
            found_key = normalized_key

        if not found_key:
            return None

        if row_number is not None:
            # Trả về dữ liệu của dòng cụ thể
            return self.timeline_row_details[found_key].get(row_number)
        else:
            # Trả về tất cả dữ liệu của timeline
            return self.timeline_row_details[found_key]

    def get_ids_to_remove(self, ids_to_filter, selected_clusters, count_to_remove, current_time_slot=None):
        """
        Lấy danh sách ID cần xóa dựa trên Cluster đã chọn và số lượng cần xóa

        Args:
            ids_to_filter: Danh sách ID cần lọc
            selected_clusters: Các Cluster đã chọn để xóa
            count_to_remove: Số lượng ID cần xóa
            current_time_slot: Khung giờ hiện tại đang xử lý (nếu có)
        """
        # Lọc danh sách ID thuộc các Cluster đã chọn
        filtered_ids = [id_val for id_val in ids_to_filter
                      if id_val in self.id_to_cluster and self.id_to_cluster[id_val] in selected_clusters]

        # Nếu có khung giờ hiện tại, loại bỏ các ID có Timeline trùng với khung giờ hiện tại
        if current_time_slot:
            # Chuẩn hóa khung giờ hiện tại để so sánh (loại bỏ khoảng trắng, chữ thường)
            normalized_time_slot = current_time_slot.lower().strip()

            # Lọc ra các ID không có Timeline trùng với khung giờ hiện tại
            filtered_ids = [id_val for id_val in filtered_ids
                          if id_val not in self.id_to_timeline or
                             normalized_time_slot not in self.id_to_timeline[id_val].lower()]

        # Sắp xếp theo 30D NMV tăng dần (ưu tiên xóa giá trị thấp)
        sorted_ids = sorted(filtered_ids, key=lambda id_val: self.id_to_nmv.get(id_val, 0))

        # Lấy số lượng ID cần xóa
        return sorted_ids[:count_to_remove]

    def has_review(self, id_val):
        """Kiểm tra xem ID có giá trị Review ở cột M hoặc N không"""
        return (id_val in self.id_to_review_m and self.id_to_review_m[id_val]) or \
               (id_val in self.id_to_review_n and self.id_to_review_n[id_val])

    def is_id_in_timeline(self, id_val, time_slot):
        """Kiểm tra xem ID có thuộc timeline đã cho không, sử dụng 3 phương pháp kiểm tra"""
        # Phương pháp 1: Kiểm tra từ timeline_row_details (ưu tiên cao nhất)
        normalized_time_slot = normalize_timeline(time_slot)
        time_slot_clean = time_slot.strip().lower()
        normalized_time_slot = normalized_time_slot.strip().lower()

        # Kiểm tra trong timeline_row_details
        found_in_rows = False
        timeline_key = None

        # Tìm key phù hợp trong timeline_row_details
        if time_slot_clean in self.timeline_row_details:
            timeline_key = time_slot_clean
        elif normalized_time_slot in self.timeline_row_details:
            timeline_key = normalized_time_slot

        # Nếu tìm thấy key, kiểm tra ID trong các dòng
        if timeline_key:
            for row_num, row_data in self.timeline_row_details[timeline_key].items():
                if row_data.get('item_id') == id_val:
                    return True

        # Phương pháp 2: Kiểm tra từ id_to_normalized_timeline
        if id_val in self.id_to_normalized_timeline:
            normalized_id_timeline = self.id_to_normalized_timeline[id_val]

        # Kiểm tra nếu timeline đã chuẩn hóa chứa time_slot đã chuẩn hóa
        if normalized_id_timeline and normalized_time_slot:
        # So sánh với các phần của timeline đã chuẩn hóa (có thể có nhiều phần cách nhau bởi dấu ,)
                for part in normalized_id_timeline.split(','):
                    if part.strip() == normalized_time_slot:
                        return True

        # Phương pháp 3: Kiểm tra từ timeline gốc (không chuẩn hóa)
        if id_val in self.id_to_timeline:
            original_timeline = self.id_to_timeline[id_val].lower()
            # Kiểm tra các phần của timeline gốc
            timeline_parts = [t.strip() for t in original_timeline.split(',')]
            if time_slot_clean in timeline_parts or normalized_time_slot in timeline_parts:
                return True

            # Kiểm tra chuỗi chứa
            if time_slot_clean in original_timeline or normalized_time_slot in original_timeline:
                return True

        # Nếu không tìm thấy bằng bất kỳ phương pháp nào
        return False

class TimeSlotProcessor:
    def __init__(self, time_slot, column, deal_list_manager, exclusive_ids=None):
        self.time_slot = time_slot
        self.column = column
        self.deal_list_manager = deal_list_manager
        self.current_ids = []
        self.protected_ids = set()
        self.fixed_positions = {}
        self.exclusive_ids = exclusive_ids or []  # Các ID có chế độ Exclusive - luôn giữ nguyên vị trí
        self.exclusive_positions = {}  # Lưu vị trí hiện tại của các ID exclusive {id: position}
        self.conditions = []  # List các điều kiện áp dụng cho khung giờ này
        # Khởi tạo seed ngẫu nhiên riêng cho từng khung giờ để đảm bảo sự khác biệt
        self.random_seed = random.randint(1, 10000)

        # Khởi tạo Smart Placement Engine
        self.smart_placement_engine = SmartPlacementEngine(
            deal_list_manager,
            log_function=lambda msg: print(f"[{time_slot}] {msg}")
        )

        # Quét thông tin dòng chi tiết từ DealListManager
        self.scan_timeline_rows()

    def scan_timeline_rows(self):
        """Quét thông tin chi tiết từng dòng cho khung giờ này từ DealListManager"""
        self.timeline_row_data = {}
        normalized_time_slot = normalize_timeline(self.time_slot)
        time_slot_clean = self.time_slot.strip().lower()

        # Lấy dữ liệu chi tiết từng dòng của timeline này
        row_data = self.deal_list_manager.get_timeline_row_data(self.time_slot)
        if row_data:
            self.timeline_row_data = row_data
            print(f"Đã quét được {len(row_data)} dòng chi tiết cho khung giờ {self.time_slot}")

            # Kiểm tra một số dữ liệu mẫu để debug
            sample_keys = list(row_data.keys())[:3]  # Lấy 3 dòng đầu tiên để kiểm tra
            for row_idx in sample_keys:
                row_detail = row_data[row_idx]
                print(f"Dòng {row_idx}: item_id={row_detail.get('item_id')}, " +
                      f"review_m={row_detail.get('review_m')}, review_n={row_detail.get('review_n')}, " +
                      f"no={row_detail.get('no')}")
        else:
            print(f"Không tìm thấy dữ liệu chi tiết từng dòng cho khung giờ {self.time_slot}")

            # Thử tìm với phiên bản chuẩn hóa
            row_data = self.deal_list_manager.get_timeline_row_data(normalized_time_slot)
            if row_data:
                self.timeline_row_data = row_data
                print(f"Đã tìm thấy dữ liệu với phiên bản chuẩn hóa: {normalized_time_slot}")
            else:
                print(f"Cũng không tìm thấy dữ liệu với phiên bản chuẩn hóa")

        # Phân tích nhanh dữ liệu dòng
        if self.timeline_row_data:
            no_counts = {}
            review_counts = {}
            self.no_to_ids = {}  # Lưu trữ danh sách ID theo từng giá trị NO

            for row_num, row_data in self.timeline_row_data.items():
                # Đếm NO
                no_val = row_data.get('no', '')
                item_id = row_data.get('item_id', '')

                if no_val:
                    if no_val not in no_counts:
                        no_counts[no_val] = 0
                        self.no_to_ids[no_val] = []

                    no_counts[no_val] += 1
                    if item_id and item_id not in self.no_to_ids[no_val]:
                        self.no_to_ids[no_val].append(item_id)

                # Đếm Review
                review_m = row_data.get('review_m', '')
                review_n = row_data.get('review_n', '')
                has_review = bool(review_m or review_n)
                if has_review:
                    if no_val:
                        if no_val not in review_counts:
                            review_counts[no_val] = 0
                        review_counts[no_val] += 1

            # Hiển thị thống kê
            print("\nThống kê nhanh từ dòng chi tiết:")
            print(f"Tổng số dòng tìm thấy: {len(self.timeline_row_data)}")

            # Hiển thị phân bố NO
            if no_counts:
                print("Phân bố NO:")
                for no_val in sorted(no_counts.keys(), key=lambda x: int(x) if x.isdigit() else 999):
                    count = no_counts[no_val]
                    review_count = review_counts.get(no_val, 0)
                    id_count = len(self.no_to_ids.get(no_val, []))
                    print(f"  NO {no_val}: {count} dòng, {id_count} ID unique, trong đó có {review_count} dòng có review")

    def get_avoid_positions(self, top_n, is_atc=False):
        """Trả về số lượng vị trí đầu tiên cần tránh dựa trên top_n"""
        if is_atc:
            return 149  # ATC bắt đầu từ vị trí 150, nên avoid 0-149
        elif top_n == 10: return 3
        elif top_n == 20: return 7
        elif top_n == 30: return 10
        elif top_n == 50: return 20
        elif top_n == 100: return 30
        elif top_n == 150: return 70
        elif top_n == 200: return 100
        else: return top_n // 5

    def add_condition(self, top_text, ids, is_group):
        """Thêm một điều kiện mới cho khung giờ"""
        # Phân tích chuỗi top_text để xác định loại và giá trị
        is_atc = False
        if top_text == "ATC":
            is_atc = True
            top_n = 350  # ATC xử lý từ vị trí 150-350, nên top_n = 350
        else:
            top_n = int(top_text.split()[1])

        self.conditions.append({
            'top_n': top_n,
            'ids': ids,
            'is_group': is_group,
            'is_atc': is_atc  # Thêm flag xác định điều kiện ATC
        })

    def get_protected_ids(self):
        """Lấy danh sách ID cần bảo vệ trong khung giờ này"""
        protected = set()

        # Phương pháp 1: Bảo vệ ID từ dữ liệu dòng chi tiết
        if hasattr(self, 'timeline_row_data') and self.timeline_row_data:
            for row_num, row_data in self.timeline_row_data.items():
                item_id = row_data.get('item_id', '')
                if item_id and item_id in self.current_ids:
                    protected.add(item_id)

        # Phương pháp 2: Bảo vệ từ id_to_timeline (phương pháp cũ)
        if self.deal_list_manager.id_to_timeline:
            normalized_time_slot = self.time_slot.lower().strip()
            for id_val in self.current_ids:
                if id_val in self.deal_list_manager.id_to_timeline:
                    timeline = self.deal_list_manager.id_to_timeline[id_val].lower()
                    if normalized_time_slot in timeline and id_val not in protected:
                        protected.add(id_val)

        # Phương pháp 3: Bảo vệ từ no_to_ids thu thập trong scan_timeline_rows
        if hasattr(self, 'no_to_ids'):
            for no_val, id_list in self.no_to_ids.items():
                for item_id in id_list:
                    if item_id in self.current_ids and item_id not in protected:
                        protected.add(item_id)

        return protected

    def _prepare_assembly_framework(self, base_list, placement_positions, timeline_non_review_ids, used_ids):
        """
        Chuẩn bị framework lắp ráp: tạo danh sách có vị trí trống cho Input IDs

        Args:
            base_list: Danh sách cơ sở hiện tại
            placement_positions: List[(id_val, position, placement_type)] - vị trí Input IDs
            timeline_non_review_ids: Danh sách ID timeline không có review để fill
            used_ids: Set các ID đã sử dụng

        Returns:
            List: Framework với vị trí trống cho Input IDs
        """
        print(f"Tạo framework từ {len(base_list)} ID cơ sở với {len(placement_positions)} vị trí Input ID")

        # Lấy danh sách vị trí cần dành cho Input IDs (sắp xếp tăng dần)
        input_positions = sorted([pos for _, pos, _ in placement_positions])
        print(f"Các vị trí cần dành cho Input IDs: {input_positions}")

        # Tính số lượng ID cần loại bỏ để tạo chỗ trống
        num_input_ids = len(placement_positions)
        current_total = len(base_list)
        target_final_size = MAX_UNIQUE_IDS  # 500

        # Logic đúng: current_total - num_to_remove + num_input_ids = target_final_size
        # => num_to_remove = current_total + num_input_ids - target_final_size
        num_to_remove = current_total + num_input_ids - target_final_size
        target_base_size = current_total - num_to_remove

        print(f"Tổng ID hiện tại: {current_total}, Input IDs: {num_input_ids}, Mục tiêu cuối: {target_final_size}")
        print(f"Cần loại bỏ: {num_to_remove} ID từ danh sách cơ sở để tạo {num_input_ids} chỗ trống")
        print(f"Danh sách cơ sở sau khi loại bỏ: {target_base_size} ID")

        # Loại bỏ ID ít quan trọng nhất (ưu tiên loại bỏ ID không thuộc timeline)
        reduced_base_list = self._reduce_base_list(base_list, target_base_size, timeline_non_review_ids, used_ids)

        # Tạo framework với vị trí trống
        framework = self._create_framework_with_gaps(reduced_base_list, input_positions)

        print(f"Framework hoàn thành: {len(framework)} vị trí, trong đó {num_input_ids} vị trí trống")
        return framework

    def _reduce_base_list(self, base_list, target_size, timeline_non_review_ids, used_ids):
        """
        Loại bỏ ID ít quan trọng nhất từ danh sách cơ sở

        Args:
            base_list: Danh sách cơ sở hiện tại
            target_size: Kích thước mục tiêu
            timeline_non_review_ids: Danh sách ID timeline không có review
            used_ids: Set các ID đã sử dụng

        Returns:
            List: Danh sách đã giảm kích thước
        """
        current_size = len(base_list)
        print(f"_reduce_base_list: current_size={current_size}, target_size={target_size}")

        if current_size <= target_size:
            print(f"Không cần loại bỏ ID nào (current_size <= target_size)")
            return base_list.copy()

        if target_size < 0:
            print(f"⚠️ Target size âm ({target_size}), trả về danh sách trống")
            return []

        # Phân loại ID theo mức độ quan trọng
        timeline_review_ids = []
        timeline_non_review_ids_in_base = []
        other_ids = []

        for id_val in base_list:
            if self.deal_list_manager.has_review(id_val) and self.deal_list_manager.is_id_in_timeline(id_val, self.time_slot):
                timeline_review_ids.append(id_val)
            elif id_val in timeline_non_review_ids:
                timeline_non_review_ids_in_base.append(id_val)
            else:
                other_ids.append(id_val)

        print(f"Phân loại: {len(timeline_review_ids)} timeline+review, {len(timeline_non_review_ids_in_base)} timeline-review, {len(other_ids)} other")

        # Ưu tiên loại bỏ: Other IDs > Timeline non-review > Timeline review (theo GMV thấp)
        ids_to_remove = []
        num_to_remove = len(base_list) - target_size

        # Loại bỏ Other IDs trước (GMV thấp nhất)
        other_ids_sorted = sorted(other_ids, key=lambda x: self.deal_list_manager.id_to_nmv.get(x, 0))
        ids_to_remove.extend(other_ids_sorted[:min(num_to_remove, len(other_ids_sorted))])

        # Nếu vẫn cần loại bỏ thêm, loại bỏ Timeline non-review (GMV thấp nhất)
        if len(ids_to_remove) < num_to_remove:
            remaining_to_remove = num_to_remove - len(ids_to_remove)
            timeline_non_review_sorted = sorted(timeline_non_review_ids_in_base,
                                              key=lambda x: self.deal_list_manager.id_to_nmv.get(x, 0))
            ids_to_remove.extend(timeline_non_review_sorted[:min(remaining_to_remove, len(timeline_non_review_sorted))])

        # Tạo danh sách kết quả
        result = [id_val for id_val in base_list if id_val not in ids_to_remove]
        print(f"Đã loại bỏ {len(ids_to_remove)} ID, còn lại {len(result)} ID")

        return result

    def _create_framework_with_gaps(self, reduced_base_list, input_positions):
        """
        Tạo framework với vị trí trống tại các vị trí input_positions

        Args:
            reduced_base_list: Danh sách cơ sở đã giảm kích thước
            input_positions: List vị trí cần để trống cho Input IDs

        Returns:
            List: Framework với vị trí trống (None) tại các vị trí input_positions
        """
        framework = [None] * MAX_UNIQUE_IDS
        input_positions_set = set(input_positions)

        # Đặt các ID cơ sở vào framework, bỏ qua vị trí dành cho Input IDs
        base_index = 0
        for pos in range(MAX_UNIQUE_IDS):
            if pos not in input_positions_set:
                if base_index < len(reduced_base_list):
                    framework[pos] = reduced_base_list[base_index]
                    base_index += 1
                else:
                    break

        print(f"Framework: {base_index} ID cơ sở đã được đặt, {len(input_positions)} vị trí trống")
        return framework

    def _assemble_input_ids(self, framework, placement_positions, top_limits, used_ids):
        """
        Lắp ráp Input IDs vào framework đã chuẩn bị

        Args:
            framework: Framework với vị trí trống (None) cho Input IDs
            placement_positions: List[(id_val, position, placement_type)]
            top_limits: Dict {id: top_limit}
            used_ids: Set các ID đã sử dụng

        Returns:
            List: Danh sách hoàn chỉnh với Input IDs đã được lắp ráp
        """
        result = framework.copy()

        # Kiểm tra và điều chỉnh avoid_position cho từng Input ID
        for id_val, pos, placement_type in placement_positions:
            top_limit = top_limits.get(id_val, MAX_UNIQUE_IDS)
            avoid_positions_limit = self.smart_placement_engine._get_avoid_positions_for_top(top_limit)

            # Điều chỉnh vị trí nếu vi phạm avoid_position
            original_pos = pos
            if pos < avoid_positions_limit:
                pos = avoid_positions_limit
                print(f"  ⚠️ ĐIỀU CHỈNH AVOID_POSITION: ID {id_val} từ vị trí {original_pos + 1} -> {pos + 1} (avoid_limit: {avoid_positions_limit})")

            # Kiểm tra trùng lặp
            if id_val in used_ids:
                print(f"  ⚠️ SKIP: ID input {id_val} đã tồn tại - bỏ qua để tránh duplicate")
                continue

            # Đảm bảo vị trí hợp lệ
            if pos >= MAX_UNIQUE_IDS:
                pos = MAX_UNIQUE_IDS - 1
                print(f"  ⚠️ ĐIỀU CHỈNH VỊ TRÍ: ID {id_val} vị trí {original_pos + 1} -> {pos + 1} (vượt giới hạn)")

            # Lắp ráp Input ID vào đúng vị trí
            if result[pos] is None:
                # Vị trí trống như dự kiến
                result[pos] = id_val
                used_ids.add(id_val)
                print(f"  ✓ Lắp ráp ID input {id_val} vào vị trí {pos + 1} (type: {placement_type})")
            else:
                # Vị trí đã có ID khác - tìm vị trí trống gần nhất
                new_pos = self._find_nearest_empty_position(result, pos, avoid_positions_limit, top_limit)
                if new_pos is not None:
                    result[new_pos] = id_val
                    used_ids.add(id_val)
                    print(f"  ✓ Lắp ráp ID input {id_val} vào vị trí {new_pos + 1} (dự phòng, type: {placement_type})")
                else:
                    print(f"  ❌ KHÔNG THỂ lắp ráp ID input {id_val} - không tìm được vị trí trống phù hợp")

        # Loại bỏ các vị trí None còn lại và chuyển thành danh sách liên tục
        final_result = [id_val for id_val in result if id_val is not None]

        print(f"Lắp ráp hoàn thành: {len(final_result)} ID trong danh sách cuối cùng")
        return final_result

    def _find_nearest_empty_position(self, result, preferred_pos, avoid_limit, top_limit):
        """
        Tìm vị trí trống gần nhất với vị trí ưa thích

        Args:
            result: Danh sách hiện tại
            preferred_pos: Vị trí ưa thích
            avoid_limit: Giới hạn avoid_position
            top_limit: Giới hạn top

        Returns:
            int hoặc None: Vị trí trống gần nhất hoặc None nếu không tìm được
        """
        max_pos = min(top_limit - 1, MAX_UNIQUE_IDS - 1)

        # Tìm kiếm xung quanh vị trí ưa thích
        for distance in range(1, max_pos - avoid_limit + 1):
            # Kiểm tra vị trí bên phải
            right_pos = preferred_pos + distance
            if right_pos <= max_pos and right_pos < len(result) and result[right_pos] is None:
                return right_pos

            # Kiểm tra vị trí bên trái
            left_pos = preferred_pos - distance
            if left_pos >= avoid_limit and left_pos < len(result) and result[left_pos] is None:
                return left_pos

        return None

    def _build_complete_base_list(self, current_result, used_ids, timeline_non_review_ids, other_ids):
        """
        Tạo danh sách cơ sở đầy đủ bằng cách thêm timeline non-review và other IDs

        Args:
            current_result: Danh sách hiện tại (timeline review IDs)
            used_ids: Set các ID đã sử dụng
            timeline_non_review_ids: Danh sách ID timeline không có review
            other_ids: Danh sách ID khác

        Returns:
            List: Danh sách cơ sở đầy đủ
        """
        result = current_result.copy()

        # Thêm timeline non-review IDs
        print(f"Thêm timeline non-review IDs...")

        # Nhóm theo NO và sắp xếp
        no_to_non_review_ids = {}
        for id_val in timeline_non_review_ids:
            if id_val in used_ids:
                continue
            no_value = self.deal_list_manager.id_to_no.get(id_val, 999999)
            if no_value not in no_to_non_review_ids:
                no_to_non_review_ids[no_value] = []
            no_to_non_review_ids[no_value].append(id_val)

        # Sắp xếp theo NO tăng dần
        for no_value in sorted(no_to_non_review_ids.keys()):
            sorted_ids = sorted(
                no_to_non_review_ids[no_value],
                key=lambda id_val: self.deal_list_manager.id_to_nmv.get(id_val, 0),
                reverse=True
            )

            for id_val in sorted_ids:
                if id_val not in used_ids:
                    result.append(id_val)
                    used_ids.add(id_val)

        print(f"Đã thêm {len([id for id in timeline_non_review_ids if id in used_ids])} timeline non-review IDs")

        # Thêm other IDs (theo shop GMV)
        print(f"Thêm other IDs...")

        # Nhóm theo Shop ID
        shop_to_ids = {}
        for id_val in other_ids:
            if id_val in used_ids:
                continue
            shop_id = self.deal_list_manager.id_to_shop.get(id_val, "")
            if shop_id not in shop_to_ids:
                shop_to_ids[shop_id] = []
            shop_to_ids[shop_id].append(id_val)

        # Sắp xếp ID trong mỗi Shop theo GMV
        for shop_id, ids in shop_to_ids.items():
            shop_to_ids[shop_id] = sorted(
                ids,
                key=lambda id_val: self.deal_list_manager.id_to_nmv.get(id_val, 0),
                reverse=True
            )

        # Tính GMV trung bình cho mỗi Shop
        shop_avg_gmv = {}
        for shop_id, ids in shop_to_ids.items():
            if ids:
                total_gmv = sum(self.deal_list_manager.id_to_nmv.get(id_val, 0) for id_val in ids)
                shop_avg_gmv[shop_id] = total_gmv / len(ids) if len(ids) > 0 else 0
            else:
                shop_avg_gmv[shop_id] = 0

        # Sắp xếp Shop theo GMV trung bình
        sorted_shops = sorted(
            shop_to_ids.keys(),
            key=lambda shop_id: shop_avg_gmv.get(shop_id, 0),
            reverse=True
        )

        # Thêm ID theo thứ tự shop GMV
        remaining_slots = MAX_UNIQUE_IDS - len(result)
        added_count = 0

        for shop_id in sorted_shops:
            shop_ids = shop_to_ids[shop_id]
            for id_val in shop_ids:
                if id_val not in used_ids and added_count < remaining_slots:
                    result.append(id_val)
                    used_ids.add(id_val)
                    added_count += 1
                    if added_count >= remaining_slots:
                        break
            if added_count >= remaining_slots:
                break

        print(f"Đã thêm {added_count} other IDs")
        print(f"Tổng danh sách cơ sở: {len(result)} ID")

        return result

    def process(self):
        if not self.conditions:
            return

        # QUAN TRỌNG: Lưu lại toàn bộ danh sách ID hiện tại
        all_original_ids = self.current_ids.copy()
        print(f"\n------------------------------------------------------------------------------")
        print(f"BẮT ĐẦU XỬ LÝ KHUNG GIỜ: {self.time_slot} (Cột {self.column})")
        print(f"------------------------------------------------------------------------------")
        print(f"Tổng số ID ban đầu của khung giờ {self.time_slot}: {len(all_original_ids)}")

        # Chuẩn hóa khung giờ hiện tại để so sánh
        normalized_time_slot = normalize_timeline(self.time_slot)
        print(f"Khung giờ chuẩn hóa: {normalized_time_slot}")

        # -----------------------------------------------------------------------------------
        # BƯỚC 0A: PHÂN TÍCH ID INPUT
        # -----------------------------------------------------------------------------------

        # Lấy tất cả ID từ các điều kiện input
        input_ids_all = set()
        for cond in self.conditions:
            input_ids_all.update(cond['ids'])

        # Phân loại ID Input: đã có sẵn vs mới cần thêm
        input_ids_already_in_timeline = [id_val for id_val in input_ids_all if id_val in all_original_ids]
        input_ids_new = [id_val for id_val in input_ids_all if id_val not in all_original_ids]

        print(f"=== PHÂN TÍCH ID INPUT ===")
        print(f"Tổng ID Input từ UI: {len(input_ids_all)}")
        print(f"ID Input đã có sẵn trong khung giờ: {len(input_ids_already_in_timeline)}")
        print(f"ID Input mới cần thêm: {len(input_ids_new)}")

        if input_ids_already_in_timeline:
            print("ID Input đã có sẵn:")
            for id_val in input_ids_already_in_timeline:
                print(f"  - {id_val}")

        if input_ids_new:
            print("ID Input mới cần thêm:")
            for id_val in input_ids_new:
                print(f"  - {id_val}")

        # -----------------------------------------------------------------------------------
        # BƯỚC 0B: XỬ LÝ ID EXCLUSIVE - TÁCH RA KHỎI QUÁ TRÌNH XỬ LÝ CHÍNH
        # -----------------------------------------------------------------------------------

        exclusive_id_positions = {}  # {id: position} - lưu vị trí gốc của ID exclusive
        non_exclusive_ids = []       # Danh sách ID không phải exclusive để xử lý

        if self.exclusive_ids:
            print(f"\n=== XỬ LÝ {len(self.exclusive_ids)} ID EXCLUSIVE ===")

            # Lưu vị trí gốc của các ID exclusive và tách chúng ra
            for idx, id_val in enumerate(all_original_ids):
                if id_val in self.exclusive_ids:
                    exclusive_id_positions[id_val] = idx
                    print(f"Bảo vệ ID Exclusive: {id_val} ở vị trí {idx} (sẽ không bị di chuyển)")
                else:
                    non_exclusive_ids.append(id_val)

            print(f"Đã tách {len(exclusive_id_positions)} ID exclusive ra khỏi quá trình xử lý")
            print(f"Còn lại {len(non_exclusive_ids)} ID không phải exclusive để xử lý theo logic bình thường")

            # Cập nhật current_ids để chỉ chứa ID không phải exclusive VÀ không phải TẤT CẢ ID Input
            # QUAN TRỌNG: Loại trừ TẤT CẢ ID Input (cả đã có sẵn và mới) để xử lý riêng theo điều kiện UI
            self.current_ids = [id_val for id_val in non_exclusive_ids if id_val not in input_ids_all]
            print(f"Đã loại trừ {len([id_val for id_val in non_exclusive_ids if id_val in input_ids_all])} ID Input (cả đã có sẵn và mới) khỏi current_ids")
            print(f"TẤT CẢ ID Input sẽ được xử lý riêng theo điều kiện UI, KHÔNG bị gom theo shopID/NO")
            print(f"=== KẾT THÚC XỬ LÝ ID EXCLUSIVE ===\n")
        else:
            # Không có ID exclusive, nhưng vẫn cần loại trừ TẤT CẢ ID Input
            non_exclusive_ids = [id_val for id_val in all_original_ids if id_val not in input_ids_all]
            self.current_ids = non_exclusive_ids.copy()
            print("Không có ID Exclusive nào được thiết lập cho khung giờ này")
            print(f"Đã loại trừ {len([id_val for id_val in all_original_ids if id_val in input_ids_all])} ID Input (cả đã có sẵn và mới) khỏi current_ids")
            print(f"TẤT CẢ ID Input sẽ được xử lý riêng theo điều kiện UI, KHÔNG bị gom theo shopID/NO")

        # Hiển thị thông tin dòng chi tiết nếu có
        if hasattr(self, 'timeline_row_data') and self.timeline_row_data:
            print(f"\n=== THÔNG TIN CHI TIẾT DÒNG CHO KHUNG GIỜ {self.time_slot} ===")
            total_rows = len(self.timeline_row_data)
            review_rows = sum(1 for row_data in self.timeline_row_data.values()
                            if row_data.get('review_m') or row_data.get('review_n'))

            print(f"Tìm thấy {total_rows} dòng dữ liệu chi tiết, trong đó {review_rows} dòng có review")

            # Phân tích theo NO
            no_counts = {}
            row_nos = {}  # Lưu số dòng theo từng NO
            for row_num, row_data in self.timeline_row_data.items():
                no_value = row_data.get('no', '')
                if no_value:
                    try:
                        no_value = int(no_value) if no_value.isdigit() else 0
                        if no_value not in no_counts:
                            no_counts[no_value] = 0
                            row_nos[no_value] = []
                        no_counts[no_value] += 1
                        row_nos[no_value].append(row_num)
                    except:
                        pass

            # Hiển thị phân bố theo NO
            print("\nPhân bố dòng theo NO:")
            for no_value in sorted(no_counts.keys()):
                count = no_counts[no_value]
                print(f"- NO {no_value}: {count} dòng")

                # Hiển thị chi tiết cho NO = 1
                if no_value == 1:
                    rows = row_nos.get(no_value, [])
                    for row_num in sorted(rows)[:5]:  # Hiển thị tối đa 5 dòng
                        row_data = self.timeline_row_data[row_num]
                        item_id = row_data.get('item_id', '')
                        review_m = row_data.get('review_m', '')
                        review_n = row_data.get('review_n', '')
                        has_review = bool(review_m or review_n)
                        print(f"  + Dòng {row_num}: ID {item_id}, Có review: {has_review}")

            print("=== KẾT THÚC THÔNG TIN CHI TIẾT DÒNG ===\n")
        else:
            print(f"Không có thông tin dòng chi tiết cho khung giờ {self.time_slot}")

        # Thêm kiểm tra chi tiết về khung giờ này
        print(f"\n=== KIỂM TRA CHI TIẾT KHUNG GIỜ {self.time_slot} ===")

        # Kiểm tra các ID có NO = 1 từ cả hai nguồn dữ liệu
        no1_in_timeline = []

        # PHƯƠNG PHÁP 1: Kiểm tra NO=1 từ dữ liệu dòng chi tiết (timeline_row_data)
        if hasattr(self, 'timeline_row_data') and self.timeline_row_data:
            print(f"Kiểm tra NO=1 từ dữ liệu dòng chi tiết:")
            no1_rows = []
            for row_num, row_data in self.timeline_row_data.items():
                no_value = row_data.get('no', '')
                if no_value == '1':
                    item_id = row_data.get('item_id', '')
                    review_m = row_data.get('review_m', '')
                    review_n = row_data.get('review_n', '')
                    has_review = bool(review_m or review_n)
                    in_current_ids = item_id in self.current_ids
                    no1_rows.append((row_num, item_id, has_review, in_current_ids))

                    # Thêm vào danh sách no1_in_timeline
                    if item_id not in [x[0] for x in no1_in_timeline]:
                        no1_in_timeline.append((item_id, has_review, in_current_ids))

            if no1_rows:
                print(f"Tìm thấy {len(no1_rows)} dòng có NO=1 từ dữ liệu dòng chi tiết:")
                for row_num, item_id, has_review, in_current_ids in no1_rows:
                    print(f"  + Dòng {row_num}: ID {item_id}, Có review: {has_review}, Trong danh sách hiện tại: {in_current_ids}")
            else:
                print("Không tìm thấy dòng nào có NO=1 từ dữ liệu dòng chi tiết!")

        # PHƯƠNG PHÁP 2: Kiểm tra từ id_to_no (phương pháp cũ)
        print(f"\nKiểm tra NO=1 từ Deal List (id_to_no):")
        no1_ids_in_deal_list = [id_val for id_val, no in self.deal_list_manager.id_to_no.items() if no == 1]
        print(f"Tìm thấy {len(no1_ids_in_deal_list)} ID có NO=1 trong Deal List")

        # Kiểm tra từng ID xem có thuộc timeline này không
        no1_ids_in_timeline_from_map = []

        # Kiểm tra từng ID từ danh sách với NO=1
        for id_val in no1_ids_in_deal_list:
            is_in_timeline = self.deal_list_manager.is_id_in_timeline(id_val, self.time_slot)
            has_review = self.deal_list_manager.has_review(id_val)
            in_current_ids = id_val in self.current_ids

            if is_in_timeline:
                no1_ids_in_timeline_from_map.append((id_val, has_review, in_current_ids))
                # Thêm vào danh sách tổng hợp nếu chưa có
                if id_val not in [x[0] for x in no1_in_timeline]:
                    no1_in_timeline.append((id_val, has_review, in_current_ids))

                # Hiển thị thông tin chi tiết cho ID có NO = 1 thuộc timeline này
                timeline_orig = self.deal_list_manager.original_timeline_values.get(id_val, "không có")
                timeline_norm = self.deal_list_manager.id_to_normalized_timeline.get(id_val, "không có")
                print(f"ID NO=1 trong timeline: {id_val}, Có review: {has_review}, Trong danh sách hiện tại: {in_current_ids}")
                print(f"  - Timeline gốc: '{timeline_orig}'")
                print(f"  - Timeline chuẩn hóa: '{timeline_norm}'")
                print(f"  - So sánh với: '{self.time_slot}' -> '{normalized_time_slot}'")
                print(f"  - Kết quả so sánh: {normalized_time_slot in timeline_norm}")

                # Tìm thông tin dòng chi tiết cho ID này
                if hasattr(self, 'timeline_row_data') and self.timeline_row_data:
                    found_rows = []
                    for row_num, row_data in self.timeline_row_data.items():
                        if row_data.get('item_id') == id_val:
                            found_rows.append(row_num)

                    if found_rows:
                        print(f"  - ID này được tìm thấy trong các dòng: {found_rows}")

        print(f"Tổng số ID có NO = 1 thuộc timeline này (phương pháp map): {len(no1_ids_in_timeline_from_map)}")
        print(f"Tổng số ID có NO = 1 thuộc timeline này (tất cả phương pháp): {len(no1_in_timeline)}")

        # Hiển thị danh sách ID nếu có sự khác biệt giữa hai phương pháp
        if len(no1_ids_in_timeline_from_map) != len(no1_in_timeline):
            print(f"\nCó sự khác biệt giữa 2 phương pháp nhận diện ID có NO = 1:")
            print(f"ID chỉ được tìm thấy qua dữ liệu dòng chi tiết:")
            extra_ids = [id_val for id_val, _, _ in no1_in_timeline if id_val not in [x[0] for x in no1_ids_in_timeline_from_map]]
            for id_val in extra_ids:
                print(f"  - ID {id_val}")

        # Bổ sung kiểm tra cho các NO khác (NO = 2, 3, ...)
        for no_value in range(2, 6):  # Kiểm tra NO từ 2 đến 5
            # Kiểm tra từ dữ liệu dòng chi tiết
            no_rows = []
            if hasattr(self, 'timeline_row_data') and self.timeline_row_data:
                for row_num, row_data in self.timeline_row_data.items():
                    if row_data.get('no', '') == str(no_value):
                        item_id = row_data.get('item_id', '')
                        has_review = bool(row_data.get('review_m', '') or row_data.get('review_n', ''))
                        in_current_ids = item_id in self.current_ids
                        no_rows.append((row_num, item_id, has_review, in_current_ids))

            if no_rows:
                print(f"\nTìm thấy {len(no_rows)} dòng có NO={no_value} từ dữ liệu dòng chi tiết:")
                for row_num, item_id, has_review, in_current_ids in no_rows[:5]:  # Hiển thị 5 dòng đầu tiên
                    print(f"  + Dòng {row_num}: ID {item_id}, Có review: {has_review}, Trong danh sách hiện tại: {in_current_ids}")
                if len(no_rows) > 5:
                    print(f"    ... và {len(no_rows) - 5} ID khác")

        # Hiển thị thông tin dòng từ Deal List cho timeline này nếu có
        if hasattr(self.deal_list_manager, 'timeline_rows'):
            timeline_key = self.time_slot.lower().strip()
            normalized_key = normalized_time_slot

            # Kiểm tra cả key gốc và key đã chuẩn hóa
            if timeline_key in self.deal_list_manager.timeline_rows:
                rows = self.deal_list_manager.timeline_rows[timeline_key]
                print(f"Timeline '{timeline_key}' được tìm thấy ở {len(rows)} dòng trong Deal List (từ dòng {min(rows)} đến {max(rows)})")
            elif normalized_key in self.deal_list_manager.timeline_rows:
                rows = self.deal_list_manager.timeline_rows[normalized_key]
                print(f"Timeline đã chuẩn hóa '{normalized_key}' được tìm thấy ở {len(rows)} dòng trong Deal List (từ dòng {min(rows)} đến {max(rows)})")
            else:
                print(f"Không tìm thấy thông tin dòng cho timeline '{timeline_key}' hoặc '{normalized_key}' trong Deal List")

        # -----------------------------------------------------------------------------------
        # BƯỚC 1: THU THẬP VÀ PHÂN LOẠI CÁC ID
        # -----------------------------------------------------------------------------------

        # input_ids_all đã được định nghĩa ở BƯỚC 0A
        group_ids_all = set()  # Theo dõi các ID thuộc nhóm

        # Tạo map từ ID đến top_n và is_group
        id_to_top_n = {}
        id_to_is_group = {}
        id_to_is_atc = {}

        # Phân tích các điều kiện input (input_ids_all đã có từ trước)
        for cond in self.conditions:
            if cond['is_group']:
                group_ids_all.update(cond['ids'])

            # Map ID đến top_n, is_group và is_atc
            for id_val in cond['ids']:
                is_atc = cond.get('is_atc', False)

                # Đối với điều kiện ATC
                if is_atc:
                    id_to_top_n[id_val] = cond['top_n']
                    id_to_is_group[id_val] = cond['is_group']
                    id_to_is_atc[id_val] = True
                # Đối với điều kiện thông thường
                else:
                    # Nếu ID chưa được xử lý hoặc có ưu tiên cao hơn (top_n nhỏ hơn)
                    if id_val not in id_to_top_n or cond['top_n'] < id_to_top_n[id_val]:
                        id_to_top_n[id_val] = cond['top_n']
                        id_to_is_group[id_val] = cond['is_group']
                        id_to_is_atc[id_val] = False

        # -----------------------------------------------------------------------------------
        # BƯỚC 2: PHÂN LOẠI ID THEO TIMELINE VÀ REVIEW STATUS
        # -----------------------------------------------------------------------------------

        # Phân loại ID theo timeline hiện tại và các timeline khác
        timeline_ids = []  # ID thuộc timeline hiện tại (KHÔNG bao gồm ID Input)
        other_ids = []     # ID không thuộc timeline hiện tại (bao gồm cả timeline khác và không có timeline)

        # QUAN TRỌNG: Phân loại các ID trong danh sách hiện tại
        # TẤT CẢ ID Input (cả mới và đã có sẵn) sẽ được xử lý riêng biệt theo điều kiện UI
        for id_val in all_original_ids:
            # BỎ QUA TẤT CẢ ID Input - chúng sẽ được xử lý riêng theo điều kiện UI
            if id_val in input_ids_all:
                if id_val in input_ids_already_in_timeline:
                    print(f"Bỏ qua ID Input đã có sẵn {id_val} khỏi phân loại timeline (sẽ xử lý riêng theo điều kiện UI)")
                else:
                    print(f"Bỏ qua ID Input mới {id_val} khỏi phân loại timeline (sẽ xử lý riêng theo điều kiện UI)")
                continue

            if self.deal_list_manager.is_id_in_timeline(id_val, self.time_slot):
                timeline_ids.append(id_val)  # Thuộc timeline hiện tại (chỉ ID cơ sở)
            else:
                other_ids.append(id_val)     # Không thuộc timeline hiện tại (chỉ ID cơ sở)

        print(f"\n--- PHÂN LOẠI ID THEO TIMELINE ---")
        print(f"- ID thuộc timeline {self.time_slot}: {len(timeline_ids)}")
        print(f"- ID không thuộc timeline {self.time_slot}: {len(other_ids)}")

        # Phân loại ID thuộc timeline hiện tại theo NO và review status
        timeline_review_ids = []      # ID có review thuộc timeline hiện tại
        timeline_non_review_ids = []  # ID không có review thuộc timeline hiện tại

        # Phân loại theo NO và review status
        for id_val in timeline_ids:
            # Kiểm tra trạng thái review
            has_review = self.deal_list_manager.has_review(id_val)
            if has_review:
                timeline_review_ids.append(id_val)
            else:
                timeline_non_review_ids.append(id_val)

        print(f"- Trong đó ID có review: {len(timeline_review_ids)}")
        print(f"- Trong đó ID không có review: {len(timeline_non_review_ids)}")

        # Xây dựng dict ID với NO cho ID thuộc timeline hiện tại có review
        id_to_no = {}      # {id: no_value} cho ID có review
        id_to_no_all = {}  # {id: no_value} cho tất cả ID

        # QUAN TRỌNG: Lưu trữ thông tin NO cho tất cả ID
        # Lấy từ cả hai nguồn: id_to_no và timeline_row_data
        for id_val in timeline_ids:
            # Lấy từ id_to_no (phương pháp cũ)
            no_value = self.deal_list_manager.id_to_no.get(id_val, 999999)
            id_to_no_all[id_val] = no_value

            # Chỉ lưu NO cho ID có review vào id_to_no
            if self.deal_list_manager.has_review(id_val):
                id_to_no[id_val] = no_value

        # Bổ sung thông tin từ timeline_row_data (phương pháp mới)
        if hasattr(self, 'timeline_row_data') and self.timeline_row_data:
            for row_num, row_data in self.timeline_row_data.items():
                item_id = row_data.get('item_id', '')
                if not item_id or item_id not in timeline_ids:
                    continue

                no_value_str = row_data.get('no', '')
                if no_value_str and no_value_str.isdigit():
                    no_value = int(no_value_str)

                    # Cập nhật id_to_no_all nếu NO tìm được nhỏ hơn
                    current_no = id_to_no_all.get(item_id, 999999)
                    if no_value < current_no:
                        id_to_no_all[item_id] = no_value

                    # Cập nhật id_to_no nếu có review và NO tìm được nhỏ hơn
                    has_review = bool(row_data.get('review_m', '') or row_data.get('review_n', ''))
                    if has_review:
                        if item_id not in id_to_no or no_value < id_to_no[item_id]:
                            id_to_no[item_id] = no_value

        # In thống kê chi tiết về ID có review với NO
        print(f"\n--- THỐNG KÊ ID REVIEW THEO NO (TRONG KHUNG GIỜ {self.time_slot}) ---")
        no_count = {}
        for id_val, no_value in id_to_no.items():
            if no_value not in no_count:
                no_count[no_value] = 0
            no_count[no_value] += 1

        # Thêm dữ liệu từ no_to_ids (ID từ timeline_row_data)
        if hasattr(self, 'no_to_ids'):
            for no_val_str, id_list in self.no_to_ids.items():
                if no_val_str.isdigit():
                    no_val = int(no_val_str)
                    for item_id in id_list:
                        has_review = self.deal_list_manager.has_review(item_id)
                        if has_review and (item_id not in id_to_no or no_val < id_to_no[item_id]):
                            id_to_no[item_id] = no_val
                            if no_val not in no_count:
                                no_count[no_val] = 0
                            no_count[no_val] += 1

        for no_value in sorted(no_count.keys()):
            print(f"  - NO {no_value}: {no_count[no_value]} ID review")

        # Kiểm tra nếu có NO 1
        if 1 in no_count:
            print(f"✓ Tìm thấy {no_count[1]} ID với NO = 1 trong khung giờ này")

            # Hiển thị chi tiết ID với NO = 1
            no1_ids = [id_val for id_val, no in id_to_no.items() if no == 1]
            for id_val in no1_ids:
                print(f"    - ID: {id_val}")
        else:
            print(f"⚠️ CẢNH BÁO: Không có ID với NO = 1 trong khung giờ này!")

        # -----------------------------------------------------------------------------------
        # BƯỚC 3: XÂY DỰNG KẾT QUẢ THEO THỨ TỰ ƯU TIÊN
        # -----------------------------------------------------------------------------------

        result = []
        used_ids = set()

        # 1. ƯU TIÊN HÀNG ĐẦU: ID thuộc timeline hiện tại và có review (theo NO tăng dần)
        print("\n--- 1. THÊM ID THUỘC TIMELINE HIỆN TẠI CÓ REVIEW (THEO NO) ---")

        # MỚI: Sử dụng cả dữ liệu dòng chi tiết để xác định ID có review và NO
        timeline_review_with_no = {}  # {id_val: {'no': no_value, 'review': has_review}}

        # Lấy thông tin về NO và review từ dữ liệu dòng chi tiết
        if hasattr(self, 'timeline_row_data') and self.timeline_row_data:
            for row_num, row_data in self.timeline_row_data.items():
                item_id = row_data.get('item_id', '')
                if not item_id or item_id not in self.current_ids:
                    continue

                no_value = row_data.get('no', '')
                review_m = row_data.get('review_m', '')
                review_n = row_data.get('review_n', '')
                has_review = bool(review_m or review_n)

                # Nếu ID này chưa được ghi nhận hoặc có NO thấp hơn NO đã biết
                if (item_id not in timeline_review_with_no or
                    (no_value.isdigit() and int(no_value) <
                     int(timeline_review_with_no[item_id]['no']) if timeline_review_with_no[item_id]['no'].isdigit() else 999)):
                    timeline_review_with_no[item_id] = {'no': no_value, 'review': has_review}

        # Thêm dữ liệu từ no_to_ids (thu thập trong scan_timeline_rows)
        if hasattr(self, 'no_to_ids'):
            for no_val_str, id_list in self.no_to_ids.items():
                for id_val in id_list:
                    if id_val in self.current_ids:
                        has_review = self.deal_list_manager.has_review(id_val)

                        # Nếu ID này chưa được ghi nhận hoặc có NO thấp hơn NO đã biết
                        if (id_val not in timeline_review_with_no or
                            (no_val_str.isdigit() and int(no_val_str) <
                             int(timeline_review_with_no[id_val]['no']) if timeline_review_with_no[id_val]['no'].isdigit() else 999)):
                            timeline_review_with_no[id_val] = {'no': no_val_str, 'review': has_review}

        # Kết hợp với dữ liệu ID có review từ id_to_no
        for id_val in timeline_review_ids:
            if id_val not in timeline_review_with_no:
                no_value = str(id_to_no.get(id_val, 999))
                timeline_review_with_no[id_val] = {'no': no_value, 'review': True}

        print(f"Tìm thấy {len(timeline_review_with_no)} ID thuộc timeline có review hoặc có NO")

        # Sắp xếp ID có review theo NO tăng dần và GMV giảm dần trong cùng một nhóm NO
        timeline_review_sorted = []

        # Nhóm các ID theo NO
        id_groups_by_no = {}
        for id_val, info in timeline_review_with_no.items():
            no_value = info['no']
            if info['review']:  # Chỉ xem xét ID có review
                if no_value not in id_groups_by_no:
                    id_groups_by_no[no_value] = []
                id_groups_by_no[no_value].append(id_val)

        # Sắp xếp theo NO tăng dần
        sorted_no_values = sorted(id_groups_by_no.keys(),
                                 key=lambda x: int(x) if x.isdigit() else 999)

        for no in sorted_no_values:
            ids_with_same_no = id_groups_by_no[no]

            # Sắp xếp theo GMV giảm dần
            sorted_ids = sorted(
                ids_with_same_no,
                key=lambda id_val: self.deal_list_manager.id_to_nmv.get(id_val, 0),
                reverse=True
            )

            # Thêm vào danh sách kết quả tạm thời
            timeline_review_sorted.extend(sorted_ids)

            # In thông tin cho NO quan trọng
            if no.isdigit() and int(no) <= 10:  # Chỉ in chi tiết cho 10 NO đầu tiên
                print(f"  NO {no}: Thêm {len(sorted_ids)} ID có review (vị trí {len(result)} - {len(result) + len(sorted_ids) - 1})")

        # Thêm tất cả ID thuộc timeline có review vào kết quả
        for id_val in timeline_review_sorted:
            result.append(id_val)
            used_ids.add(id_val)

            # In chi tiết các ID quan trọng (giới hạn số lượng hiển thị)
            if len(result) <= 20:  # Chỉ in chi tiết cho 20 ID đầu tiên
                no_value = timeline_review_with_no[id_val]['no']
                print(f"    + Vị trí {len(result)-1}: ID {id_val} (NO: {no_value})")

        # 2. THÊM TIMELINE NON-REVIEW VÀ OTHER IDS ĐỂ TẠO DANH SÁCH CƠ SỞ ĐẦY ĐỦ
        print(f"\n--- 2. TẠO DANH SÁCH CƠ SỞ ĐẦY ĐỦ ---")
        result = self._build_complete_base_list(result, used_ids, timeline_non_review_ids, other_ids)
        print(f"Danh sách cơ sở hoàn chỉnh: {len(result)} ID")

        # 3. ƯU TIÊN THỨ BA: ID input (sử dụng Smart Placement Engine)
        print(f"\n--- 3. THÊM ID INPUT VỚI SMART PLACEMENT ---")

        # Kiểm tra trùng lặp giữa input và đã thêm
        overlap_count = len([id_val for id_val in input_ids_all if id_val in used_ids])
        print(f"Tổng số ID input: {len(input_ids_all)}, trong đó {overlap_count} ID đã được thêm ở bước 1")

        # Lọc ra TẤT CẢ ID input chưa được thêm (bao gồm cả ID Input đã có sẵn và mới)
        remaining_input_ids = [id_val for id_val in input_ids_all if id_val not in used_ids]

        print(f"TẤT CẢ {len(remaining_input_ids)} ID Input sẽ được xử lý bằng Smart Placement theo điều kiện UI")
        if input_ids_already_in_timeline:
            print(f"Trong đó {len([id_val for id_val in input_ids_already_in_timeline if id_val not in used_ids])} ID đã có sẵn sẽ được di chuyển vị trí")

        if remaining_input_ids:
            print(f"=== BẮT ĐẦU CƠ CHẾ MODULE LẮP RÁP ===")
            print(f"Sử dụng Smart Placement Engine cho {len(remaining_input_ids)} ID input")
            print(f"Trạng thái hiện tại: {len(result)} ID đã được sắp xếp")

            # BƯỚC 1: Chuẩn bị dữ liệu cho Smart Placement Engine
            top_limits = {}
            is_grouped_flags = {}
            condition_groups = {}

            # Tạo condition_groups từ các điều kiện
            for condition_idx, condition in enumerate(self.conditions):
                for id_val in condition['ids']:
                    if id_val in remaining_input_ids:
                        condition_groups[id_val] = condition_idx

            for id_val in remaining_input_ids:
                # Xác định top limit
                is_atc = id_to_is_atc.get(id_val, False)
                top_limits[id_val] = id_to_top_n.get(id_val, 350 if is_atc else len(result))

                # Xác định flag nhóm
                is_grouped_flags[id_val] = id_to_is_group.get(id_val, False)

                condition_idx = condition_groups.get(id_val, 0)
                print(f"  ID {id_val}: top_limit={top_limits[id_val]}, is_grouped={is_grouped_flags[id_val]}, condition_idx={condition_idx}, is_atc={is_atc}")

            # BƯỚC 2: Smart Placement tính toán vị trí (không thay đổi danh sách)
            print(f"\n--- PHASE 1: SMART PLACEMENT TÍNH TOÁN VỊ TRÍ ---")
            placement_results = self.smart_placement_engine.find_optimal_positions(
                result.copy(),  # Truyền bản sao để không ảnh hưởng đến result gốc
                remaining_input_ids,
                top_limits,
                is_grouped_flags,
                condition_groups  # Truyền thông tin nhóm điều kiện
            )

            # BƯỚC 3: Xử lý kết quả Smart Placement với shop grouping optimization
            print(f"\n--- PHASE 2: XỬ LÝ KẾT QUẢ SMART PLACEMENT ---")
            adjusted_placements = []

            for id_val, position, placement_type in placement_results:
                top_limit = top_limits[id_val]

                # QUAN TRỌNG: ID Input KHÔNG được Shop Optimization
                # ID Input phải tuân thủ điều kiện UI (top_limit, grouping) trước tiên
                print(f"  Smart Placement: {id_val} -> vị trí {position + 1} (type: {placement_type}) - KHÔNG áp dụng Shop Optimization")
                adjusted_placements.append((id_val, position, placement_type))

            # BƯỚC 4: Chuẩn bị framework lắp ráp
            print(f"\n--- PHASE 3: CHUẨN BỊ FRAMEWORK LẮP RÁP ---")
            result = self._prepare_assembly_framework(result, adjusted_placements, timeline_non_review_ids, used_ids)

            # BƯỚC 5: Lắp ráp Input IDs vào framework
            print(f"\n--- PHASE 4: LẮP RÁP INPUT IDS ---")
            result = self._assemble_input_ids(result, adjusted_placements, top_limits, used_ids)

            # BƯỚC 6: Thống kê và validation cuối cùng
            print(f"\n--- PHASE 5: THỐNG KÊ VÀ VALIDATION ---")
            placement_stats = {}
            for _, _, placement_type in adjusted_placements:
                placement_stats[placement_type] = placement_stats.get(placement_type, 0) + 1

            print(f"THỐNG KÊ SMART PLACEMENT:")
            for placement_type, count in placement_stats.items():
                print(f"  {placement_type}: {count} ID")
            print(f"Tổng cộng: {len(adjusted_placements)} ID input đã được lắp ráp")
            print(f"Kích thước danh sách cuối cùng: {len(result)} ID")

            # VALIDATION: Kiểm tra avoid_position cho Input IDs
            print(f"\nVALIDATION AVOID_POSITION:")
            violations = []
            for id_val, _, placement_type in adjusted_placements:
                if id_val in result:
                    actual_pos = result.index(id_val)
                    top_limit = top_limits.get(id_val, len(result))
                    avoid_limit = self.smart_placement_engine._get_avoid_positions_for_top(top_limit)

                    if actual_pos < avoid_limit:
                        violations.append((id_val, actual_pos, avoid_limit, placement_type))

            if violations:
                print(f"❌ PHÁT HIỆN {len(violations)} VI PHẠM AVOID_POSITION:")
                for id_val, actual_pos, avoid_limit, placement_type in violations:
                    print(f"  - ID {id_val}: vị trí {actual_pos + 1} < avoid_limit {avoid_limit} ({placement_type})")
            else:
                print(f"✅ TẤT CẢ ID INPUT TUÂN THỦ AVOID_POSITION")

            print(f"=== KẾT THÚC CƠ CHẾ MODULE LẮP RÁP ===\n")
        else:
            print("Không có ID input nào cần xử lý")

        # 4. XỬ LÝ ID EXCLUSIVE (nếu có)
        print(f"\n--- 4. XỬ LÝ ID EXCLUSIVE ---")



        # -----------------------------------------------------------------------------------
        # BƯỚC CUỐI: CHÈN LẠI ID EXCLUSIVE VÀO ĐÚNG VỊ TRÍ GỐC
        # -----------------------------------------------------------------------------------

        if self.exclusive_ids and exclusive_id_positions:
            print(f"\n=== CHÈN LẠI {len(exclusive_id_positions)} ID EXCLUSIVE VÀO VỊ TRÍ GỐC ===")

            # Tạo danh sách kết quả cuối cùng với ID exclusive ở đúng vị trí
            final_result = [''] * max(len(result) + len(exclusive_id_positions), max(exclusive_id_positions.values()) + 1)

            # Đầu tiên, đặt tất cả ID exclusive vào đúng vị trí gốc
            for exclusive_id, original_position in exclusive_id_positions.items():
                if original_position < len(final_result):
                    final_result[original_position] = exclusive_id
                    print(f"Đặt ID Exclusive {exclusive_id} vào vị trí gốc {original_position}")
                else:
                    # Mở rộng danh sách nếu cần
                    while len(final_result) <= original_position:
                        final_result.append('')
                    final_result[original_position] = exclusive_id
                    print(f"Mở rộng danh sách và đặt ID Exclusive {exclusive_id} vào vị trí {original_position}")

            # Sau đó, chèn các ID không phải exclusive vào các vị trí còn trống
            result_index = 0
            for i in range(len(final_result)):
                if final_result[i] == '' and result_index < len(result):
                    final_result[i] = result[result_index]
                    result_index += 1

            # Thêm các ID còn lại vào cuối nếu có
            while result_index < len(result):
                final_result.append(result[result_index])
                result_index += 1

            # Loại bỏ các vị trí trống ở cuối
            while final_result and final_result[-1] == '':
                final_result.pop()

            print(f"Đã hoàn thành việc chèn ID Exclusive:")
            print(f"- Tổng số ID cuối cùng: {len(final_result)}")
            print(f"- Số ID Exclusive được bảo vệ: {len(exclusive_id_positions)}")
            print(f"- Số ID không phải Exclusive: {len([id for id in final_result if id not in exclusive_id_positions])}")

            # Kiểm tra xem ID exclusive có ở đúng vị trí không
            for exclusive_id, expected_position in exclusive_id_positions.items():
                if expected_position < len(final_result) and final_result[expected_position] == exclusive_id:
                    print(f"✓ ID Exclusive {exclusive_id} đã được đặt đúng vị trí {expected_position}")
                else:
                    print(f"❌ LỖI: ID Exclusive {exclusive_id} KHÔNG ở đúng vị trí {expected_position}")

            result = final_result
            print(f"=== KẾT THÚC CHÈN ID EXCLUSIVE ===\n")

        # Gán kết quả cuối cùng và trả về
        self.current_ids = result

        # Thêm vào đầu hàm process hoặc sau đoạn khởi tạo biến cơ bản
        print(f"\n=== KIỂM TRA CHI TIẾT KHUNG GIỜ {self.time_slot} ===")

        # Kiểm tra dữ liệu timeline trong DealListManager
        rows_with_timeline = []
        rows_with_timeline_and_no1 = []

        # Tạo biến để lưu trữ các dòng đã kiểm tra
        timeline_matched_count = 0
        normalized_time_slot = self.time_slot.lower().strip()

        # Duyệt qua toàn bộ dữ liệu timeline để tìm các dòng khớp với khung giờ hiện tại
        for id_val, timeline_str in self.deal_list_manager.id_to_timeline.items():
            if timeline_str and normalized_time_slot in timeline_str.lower():
                timeline_matched_count += 1
                no_value = self.deal_list_manager.id_to_no.get(id_val, 0)

                # Phân tích đặc biệt cho NO = 1
                if no_value == 1:
                    has_review = self.deal_list_manager.has_review(id_val)
                    in_current_ids = id_val in self.current_ids
                    rows_with_timeline_and_no1.append((id_val, has_review, in_current_ids))

        print(f"Tìm thấy {timeline_matched_count} ID có timeline khớp với '{normalized_time_slot}' trong Deal List")

        if rows_with_timeline_and_no1:
            print(f"Trong đó có {len(rows_with_timeline_and_no1)} ID có NO = 1:")
            for id_val, has_review, in_current_ids in rows_with_timeline_and_no1:
                print(f"  - ID: {id_val}, Có review: {has_review}, Có trong danh sách hiện tại: {in_current_ids}")
                # Hiển thị dữ liệu timeline gốc
                timeline_original = self.deal_list_manager.original_timeline_values.get(id_val, "không có")
                print(f"    Timeline gốc: '{timeline_original}'")

    def on_module_activated(self):
        """Được gọi khi module được kích hoạt từ main.py - không reset để bảo toàn dữ liệu"""
        # Basket arrangement có nhiều trạng thái phức tạp, không reset để tránh mất dữ liệu
        pass

# Dialog để ánh xạ cột trong Deal list
class ColumnMappingDialog(QDialog):
    def __init__(self, column_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Ánh xạ cột Deal list")
        self.setMinimumWidth(400)
        self.column_data = column_data

        # Layout chính
        self.layout = QVBoxLayout()

        # Label hướng dẫn
        self.label = QLabel("Thiết lập vị trí của các cột trong sheet Deal list. Nhập chữ cái cột (A, B, C,...) cho mỗi trường.")
        self.label.setWordWrap(True)
        self.layout.addWidget(self.label)

        # Form layout cho các trường ánh xạ
        self.form_layout = QFormLayout()

        # Thêm trường nhập liệu cho mỗi cột
        self.id_column = QLineEdit()
        self.id_column.setText(self.column_data.get("id_column", "A"))
        self.id_column.setMaxLength(3)  # Giới hạn nhập tối đa 3 ký tự (ví dụ: AAA)

        self.cluster_column = QLineEdit()
        self.cluster_column.setText(self.column_data.get("cluster_column", "B"))
        self.cluster_column.setMaxLength(3)

        self.nmv_column = QLineEdit()
        self.nmv_column.setText(self.column_data.get("nmv_column", "C"))
        self.nmv_column.setMaxLength(3)

        self.timeline_column = QLineEdit()
        self.timeline_column.setText(self.column_data.get("timeline_column", "R"))
        self.timeline_column.setMaxLength(3)

        # Thêm trường nhập liệu cho cột Shop ID
        self.shop_id_column = QLineEdit()
        self.shop_id_column.setText(self.column_data.get("shop_id_column", "F"))
        self.shop_id_column.setMaxLength(3)

        # Thêm các trường mới: Review M, Review N, và NO
        self.review_m_column = QLineEdit()
        self.review_m_column.setText(self.column_data.get("review_m_column", "M"))
        self.review_m_column.setMaxLength(3)

        self.review_n_column = QLineEdit()
        self.review_n_column.setText(self.column_data.get("review_n_column", "N"))
        self.review_n_column.setMaxLength(3)

        self.no_column = QLineEdit()
        self.no_column.setText(self.column_data.get("no_column", "S"))
        self.no_column.setMaxLength(3)

        self.brand_column = QLineEdit()
        self.brand_column.setText(self.column_data.get("brand_column", "U"))
        self.brand_column.setMaxLength(3)

        # Thêm validator để chỉ cho phép nhập chữ cái
        regex = QRegularExpression("[A-Za-z]+")
        validator = QRegularExpressionValidator(regex)
        self.id_column.setValidator(validator)
        self.cluster_column.setValidator(validator)
        self.nmv_column.setValidator(validator)
        self.timeline_column.setValidator(validator)
        self.shop_id_column.setValidator(validator)
        self.review_m_column.setValidator(validator)
        self.review_n_column.setValidator(validator)
        self.no_column.setValidator(validator)
        self.brand_column.setValidator(validator)

        # Tạo label cho các trường
        id_label = "Cột ID:"
        cluster_label = "Cột Cluster:"
        nmv_label = "Cột NMV:"
        timeline_label = "Cột Timeline (khung giờ):"
        shop_id_label = "Cột Shop ID:"
        review_m_label = "Cột Review M:"
        review_n_label = "Cột Review N:"
        no_label = "Cột NO (Thứ tự):"
        brand_label = "Cột Brand (Nhãn hàng):"

        # Thêm các field vào form
        self.form_layout.addRow(id_label, self.id_column)
        self.form_layout.addRow(cluster_label, self.cluster_column)
        self.form_layout.addRow(nmv_label, self.nmv_column)
        self.form_layout.addRow(timeline_label, self.timeline_column)
        self.form_layout.addRow(shop_id_label, self.shop_id_column)
        self.form_layout.addRow(review_m_label, self.review_m_column)
        self.form_layout.addRow(review_n_label, self.review_n_column)
        self.form_layout.addRow(no_label, self.no_column)
        self.form_layout.addRow(brand_label, self.brand_column)

        self.layout.addLayout(self.form_layout)

        # Thêm note về index cột
        self.note = QLabel("Lưu ý: Nhập chữ cái cột (A, B, C,...) tương ứng với vị trí của cột trong sheet Deal list. "
                          f"Header ở dòng {DEAL_LIST_HEADER_ROW}, dữ liệu bắt đầu từ dòng {DEAL_LIST_DATA_START_ROW}.")
        self.note.setWordWrap(True)
        self.layout.addWidget(self.note)

        # Buttons
        self.button_layout = QHBoxLayout()
        self.ok_button = QPushButton("Áp dụng")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("Hủy")
        self.cancel_button.clicked.connect(self.reject)

        self.button_layout.addWidget(self.ok_button)
        self.button_layout.addWidget(self.cancel_button)
        self.layout.addLayout(self.button_layout)

        self.setLayout(self.layout)

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #222;
                color: #EEE;
            }
            QLabel {
                color: #EEE;
            }
            QLineEdit {
                background-color: #333;
                color: #EEE;
                border: 1px solid #555;
                padding: 5px;
                border-radius: 3px;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 6px;
                border-radius: 3px;
                color: #EEE;
            }
            QPushButton:hover {
                background-color: #555;
            }
        """)

    def get_updated_mapping(self):
        """Trả về dict ánh xạ cột đã được cập nhật"""
        return {
            "id_column": self.id_column.text().strip().upper(),
            "cluster_column": self.cluster_column.text().strip().upper(),
            "nmv_column": self.nmv_column.text().strip().upper(),
            "timeline_column": self.timeline_column.text().strip().upper(),
            "shop_id_column": self.shop_id_column.text().strip().upper(),
            "review_m_column": self.review_m_column.text().strip().upper(),
            "review_n_column": self.review_n_column.text().strip().upper(),
            "no_column": self.no_column.text().strip().upper(),
            "brand_column": self.brand_column.text().strip().upper()
        }

# THÊM SAU DÒNG 1217, sau class ClusterSelectionDialog
# Dialog để quản lý ID Exclusive
class ExclusiveIDsDialog(QDialog):
    def __init__(self, time_slots, exclusive_ids_map, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Thiết lập ID Exclusive")
        self.setMinimumWidth(800)  # Tăng width để hiển thị đủ các cột
        self.setMinimumHeight(600)

        # Lưu trữ time slots và map của ID Exclusive
        self.time_slots = time_slots
        self.exclusive_ids_map = exclusive_ids_map.copy()  # Tạo bản sao để không ảnh hưởng đến bản gốc
        self.parent_app = parent

        # Layout chính
        self.layout = QVBoxLayout()

        # Vùng chọn khung giờ
        time_group = QGroupBox("Chọn khung giờ")
        time_layout = QVBoxLayout()

        self.time_combo = QComboBox()
        for time_slot in self.time_slots:
            self.time_combo.addItem(time_slot)

        self.time_combo.currentIndexChanged.connect(self.update_table_data)
        time_layout.addWidget(self.time_combo)
        time_group.setLayout(time_layout)
        self.layout.addWidget(time_group)

        # Vùng hiển thị bảng và tìm kiếm
        table_group = QGroupBox("Danh sách ID trong khung giờ")
        table_layout = QVBoxLayout()

        # Thêm ô tìm kiếm
        search_layout = QHBoxLayout()
        search_label = QLabel("Tìm kiếm:")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Tìm theo ID, Shop ID hoặc tên...")
        self.search_input.textChanged.connect(self.filter_table)

        # Thêm label thông tin cột
        self.column_info_label = QLabel("")

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input, 1)
        search_layout.addWidget(self.column_info_label)
        table_layout.addLayout(search_layout)

        # Tạo bảng hiển thị dữ liệu
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(["Chọn", "Shop ID", "Item ID", "STT", "Tên sản phẩm"])

        # Thiết lập thuộc tính bảng
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setAlternatingRowColors(True)
        self.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.table.setSortingEnabled(True)

        # Thiết lập chiều rộng cột
        self.table.setColumnWidth(0, 50)   # Cột Chọn
        self.table.setColumnWidth(1, 100)  # Shop ID
        self.table.setColumnWidth(2, 160)  # Item ID
        self.table.setColumnWidth(3, 50)   # STT
        self.table.setColumnWidth(4, 400)  # Tên sản phẩm

        table_layout.addWidget(self.table)

        # Hiển thị thông tin tổng số ID đã chọn
        self.selection_info = QLabel("0 ID đã được chọn")
        table_layout.addWidget(self.selection_info)

        table_group.setLayout(table_layout)
        self.layout.addWidget(table_group)

        # Hướng dẫn
        instruction_label = QLabel("ID đặt ở chế độ Exclusive sẽ được ưu tiên giữ nguyên vị trí hiện tại trong khung giờ được chọn. "
                                 "Các ID khác (kể cả Input ID) không được phép chen ngang vị trí của các ID Exclusive này.")
        instruction_label.setWordWrap(True)
        instruction_label.setStyleSheet("color: #AAA; font-style: italic;")
        self.layout.addWidget(instruction_label)

        # Buttons
        button_layout = QHBoxLayout()

        # Nút chọn/bỏ chọn tất cả
        self.select_all_button = QPushButton("Chọn tất cả")
        self.select_all_button.clicked.connect(self.select_all_rows)

        self.clear_all_button = QPushButton("Bỏ chọn tất cả")
        self.clear_all_button.clicked.connect(self.clear_all_rows)

        # Nút áp dụng và hủy
        self.apply_button = QPushButton("Áp dụng")
        self.apply_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("Hủy")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.select_all_button)
        button_layout.addWidget(self.clear_all_button)
        button_layout.addStretch(1)
        button_layout.addWidget(self.apply_button)
        button_layout.addWidget(self.cancel_button)
        self.layout.addLayout(button_layout)

        self.setLayout(self.layout)

        # Đặt timer để đảm bảo UI đã được khởi tạo trước khi load dữ liệu
        QTimer.singleShot(100, self.delayed_init)

    def log(self, message):
        """Ghi log thông qua parent_app nếu có"""
        print(message)  # Luôn in ra console
        if hasattr(self, 'parent_app') and hasattr(self.parent_app, 'log'):
            self.parent_app.log(message)

    def delayed_init(self):
        """Khởi tạo muộn để đảm bảo UI đã được tạo xong"""
        # Cập nhật dữ liệu bảng ban đầu
        if self.time_combo.count() > 0:
            self.update_table_data()

        # Style
        self.setStyleSheet("""
            QDialog {
                background-color: #222;
                color: #EEE;
            }
            QGroupBox {
                border: 1px solid #555;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                color: #EEE;
            }
            QPushButton {
                background-color: #444;
                border: 1px solid #666;
                padding: 6px;
                border-radius: 3px;
                color: #EEE;
            }
            QPushButton:hover {
                background-color: #555;
            }
            QComboBox, QLineEdit {
                background-color: #333;
                border: 1px solid #555;
                padding: 4px;
                border-radius: 3px;
                color: #EEE;
            }
            QLabel {
                color: #EEE;
            }
            QTableWidget {
                background-color: #2D2D2D;
                alternate-background-color: #353535;
                color: #EEE;
                gridline-color: #444;
                border: 1px solid #555;
                outline: none;
            }
            QTableWidget::item:selected {
                background-color: #3A6EA5;
            }
            QHeaderView::section {
                background-color: #444;
                color: #EEE;
                padding: 4px;
                border: 1px solid #555;
            }
            QCheckBox {
                color: #EEE;
            }
        """)

    def update_table_data(self):
        """Cập nhật dữ liệu bảng khi thay đổi khung giờ"""
        if self.time_combo.count() == 0:
            return

        current_time_slot = self.time_combo.currentText()
        self.table.setRowCount(0)  # Xóa tất cả dữ liệu hiện tại

        # Debug log
        print(f"Đang cập nhật dữ liệu cho khung giờ: '{current_time_slot}'")

        # Lấy danh sách ID exclusive cho khung giờ đã chọn
        exclusive_ids = self.exclusive_ids_map.get(current_time_slot, [])
        print(f"Số ID exclusive hiện tại trong khung giờ này: {len(exclusive_ids)}")

        # Lấy dữ liệu từ sheet hiện tại nếu có
        if hasattr(self.parent_app, 'sheet') and self.parent_app.sheet:
            try:
                # Debug: In ra time_slot_map của parent_app
                print("Debug - Tất cả khung giờ trong time_slot_map:")
                for ts, col in self.parent_app.time_slot_map.items():
                    print(f"  '{ts}' -> '{col}'")

                # Tìm cột tương ứng với khung giờ đã chọn
                column = None

                # Cách 1: Tìm khớp chính xác
                if current_time_slot in self.parent_app.time_slot_map:
                    column = self.parent_app.time_slot_map[current_time_slot]
                    print(f"Tìm thấy cột {column} khớp chính xác với khung giờ '{current_time_slot}'")
                else:
                    # Cách 2: Tìm khớp một phần hoặc dựa trên định dạng khác
                    print(f"Không tìm thấy khớp chính xác, thử tìm khớp một phần...")
                    for ts, col in self.parent_app.time_slot_map.items():
                        # Xóa khoảng trắng và chuyển về chữ thường để so sánh
                        normalized_ts = ts.lower().replace(' ', '')
                        normalized_current = current_time_slot.lower().replace(' ', '')

                        # Kiểm tra nếu chuỗi thời gian chứa nhau
                        if normalized_ts in normalized_current or normalized_current in normalized_ts:
                            column = col
                            print(f"Tìm thấy khớp một phần: '{ts}' ~ '{current_time_slot}' -> cột {column}")
                            break

                # Nếu vẫn không tìm thấy, thử tìm theo thời gian con (ví dụ: "16:00" trong "16:00-17:00")
                if not column:
                    print(f"Thử tìm theo thời gian con...")
                    time_parts = current_time_slot.split('-')
                    if len(time_parts) == 2:
                        start_time = time_parts[0].strip()
                        for ts, col in self.parent_app.time_slot_map.items():
                            if start_time in ts:
                                column = col
                                print(f"Tìm thấy khớp với thời gian bắt đầu: '{ts}' chứa '{start_time}' -> cột {column}")
                                break

                if not column:
                    print(f"Không tìm thấy cột tương ứng với khung giờ '{current_time_slot}'")
                    self.column_info_label.setText(f"Không tìm thấy cột cho khung giờ này")
                    self.column_info_label.setStyleSheet("color: red;")

                    # Thêm nút cho phép chọn cột thủ công
                    manual_select = QMessageBox.question(
                        self,
                        "Không tìm thấy cột",
                        f"Không tìm thấy cột tương ứng với khung giờ '{current_time_slot}'.\n\nBạn có muốn chọn cột thủ công hoặc xem dữ liệu cơ bản?",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )

                    if manual_select == QMessageBox.StandardButton.Yes:
                        # Hiển thị dialog để người dùng chọn cột
                        selected_column = self.select_manual_column(current_time_slot)
                        if selected_column:
                            column = selected_column
                            print(f"Sử dụng cột {column} đã chọn thủ công cho khung giờ '{current_time_slot}'")
                        else:
                            print("Người dùng không chọn cột, sẽ xem dữ liệu cơ bản")
                    else:
                        return

                print(f"Đã tìm thấy cột {column if column else 'N/A'} cho khung giờ '{current_time_slot}'")
                if column:
                    self.column_info_label.setText(f"Khung giờ: {current_time_slot} (Cột: {column})")
                else:
                    self.column_info_label.setText(f"Khung giờ: {current_time_slot} (Xem dữ liệu gốc)")
                self.column_info_label.setStyleSheet("color: lightgreen;")

                # Lấy tất cả dữ liệu từ sheet
                all_values = self.parent_app.sheet.get_all_values()
                print(f"Đã load sheet với {len(all_values)} dòng")

                # Chỉ mục cột được xác định theo quy tắc khoảng cách cố định
                # Mỗi khung giờ có 5 cột liên quan: khung giờ, Item ID, Shop ID, STT, tên Brand
                # Thông tin này đã được lưu trữ trong self.timeslot_columns_map

                # Sử dụng thông tin từ thuộc tính của self.parent_app
                column_info = None
                if hasattr(self.parent_app, 'timeslot_columns_map') and current_time_slot in self.parent_app.timeslot_columns_map:
                    column_info = self.parent_app.timeslot_columns_map[current_time_slot]
                    self.log(f"Đã tìm thấy thông tin cột cho khung giờ {current_time_slot}: {column_info}")

                    # Lấy chỉ mục cột
                    if column_info:
                        item_id_col = column_info.get("item_id_column", "")
                        shop_id_col = column_info.get("shop_id_column", "")
                        stt_col = column_info.get("stt_column", "")
                        brand_col = column_info.get("brand_column", "")

                        item_id_col_idx = col_to_index(item_id_col) if item_id_col else 1
                        shop_id_col_idx = col_to_index(shop_id_col) if shop_id_col else 0
                        stt_col_idx = col_to_index(stt_col) if stt_col else 2
                        brand_name_col_idx = col_to_index(brand_col) if brand_col else 3
                else:
                    # Giá trị mặc định nếu không tìm thấy thông tin
                    shop_id_col_idx = 0
                    item_id_col_idx = 1
                    stt_col_idx = 2
                    brand_name_col_idx = 3

                # Chỉ số dòng bắt đầu dữ liệu: dòng 5 (index 4), vì header ở dòng 4
                data_start_row_idx = DATA_START_ROW - 1  # Chuyển từ 1-indexed sang 0-indexed

                # Đếm số ID đã được loaded
                loaded_count = 0

                # Nếu có cột xác định và có thể load từ cột đó
                if column and column.strip():
                    col_idx = col_to_index(column)
                    print(f"Sẽ load dữ liệu từ cột {column} (index {col_idx})")

                    # Lấy dữ liệu từ vị trí cột ID thực tế thay vì cấu trúc cột cố định
                    for i, row in enumerate(all_values[DATA_START_ROW - 1:]):
                        # Kiểm tra xem dòng này có đủ cột không
                        if len(row) <= col_idx:
                            continue

                        item_id = row[col_idx].strip() if col_idx < len(row) else ""
                        if not item_id:
                            continue

                        # Khởi tạo các biến với giá trị mặc định
                        shop_id = ""
                        stt = ""
                        brand_name = ""

                        # Lấy dữ liệu từ các cột tương ứng
                        if len(row) > shop_id_col_idx:
                            shop_id = row[shop_id_col_idx].strip()
                        if len(row) > stt_col_idx:
                            stt = row[stt_col_idx].strip()
                        if len(row) > brand_name_col_idx:
                            brand_name = row[brand_name_col_idx].strip()

                        self.log(f"Đọc dữ liệu từ dòng {i + data_start_row_idx + 1}: Shop ID='{shop_id}', STT='{stt}', Brand='{brand_name}'")

                        loaded_count += 1

                        # Thêm dòng mới vào bảng
                        row_idx = self.table.rowCount()
                        self.table.insertRow(row_idx)

                        # Tạo checkbox
                        checkbox = QCheckBox()
                        checkbox.setChecked(item_id in exclusive_ids)
                        checkbox.stateChanged.connect(lambda state, row=row_idx: self.update_selection_count())
                        checkbox_cell = QWidget()
                        layout = QHBoxLayout(checkbox_cell)
                        layout.addWidget(checkbox)
                        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                        layout.setContentsMargins(0, 0, 0, 0)
                        checkbox_cell.setLayout(layout)

                        # Thêm thông tin vào bảng
                        self.table.setCellWidget(row_idx, 0, checkbox_cell)
                        self.table.setItem(row_idx, 1, QTableWidgetItem(shop_id))
                        self.table.setItem(row_idx, 2, QTableWidgetItem(item_id))
                        self.table.setItem(row_idx, 3, QTableWidgetItem(stt))
                        self.table.setItem(row_idx, 4, QTableWidgetItem(brand_name))

                    print(f"Đã load {loaded_count} ID từ cột {column}")
                else:
                    # Load dữ liệu từ cột cố định nếu không có cột ID được chỉ định
                    print("Không có cột ID xác định, load từ cấu trúc cột cố định (A-B-C-D)")

                    # Lấy dữ liệu từ sheet, bắt đầu từ dòng 4 và theo cấu trúc cột cụ thể
                    for i, row in enumerate(all_values[data_start_row_idx:]):
                        # Kiểm tra xem dòng này có phải dòng trống hoặc có #N/A không
                        if len(row) == 0 or (len(row) > 0 and row[0] == "#N/A"):
                            print(f"Dừng tại dòng {i + data_start_row_idx + 1} vì dòng trống hoặc có #N/A")
                            break

                        # Kiểm tra có đủ cột không
                        if len(row) <= max(shop_id_col_idx, item_id_col_idx, stt_col_idx, brand_name_col_idx):
                            print(f"Bỏ qua dòng {i + data_start_row_idx + 1} vì không đủ cột")
                            continue

                        # Lấy dữ liệu từ các cột
                        shop_id = row[shop_id_col_idx].strip() if shop_id_col_idx < len(row) else ""
                        item_id = row[item_id_col_idx].strip() if item_id_col_idx < len(row) else ""
                        stt = row[stt_col_idx].strip() if stt_col_idx < len(row) else ""
                        brand_name = row[brand_name_col_idx].strip() if brand_name_col_idx < len(row) else ""

                        # Bỏ qua nếu không có Item ID
                        if not item_id:
                            print(f"Bỏ qua dòng {i + data_start_row_idx + 1} vì không có Item ID")
                            continue

                        loaded_count += 1

                        # Thêm dòng mới vào bảng
                        row_idx = self.table.rowCount()
                        self.table.insertRow(row_idx)

                        # Tạo checkbox cho cột đầu tiên
                        checkbox = QCheckBox()
                        checkbox.setChecked(item_id in exclusive_ids)
                        checkbox.stateChanged.connect(lambda state, row=row_idx: self.update_selection_count())
                        checkbox_cell = QWidget()
                        layout = QHBoxLayout(checkbox_cell)
                        layout.addWidget(checkbox)
                        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                        layout.setContentsMargins(0, 0, 0, 0)
                        checkbox_cell.setLayout(layout)

                        # Thêm các thông tin vào bảng
                        self.table.setCellWidget(row_idx, 0, checkbox_cell)
                        self.table.setItem(row_idx, 1, QTableWidgetItem(shop_id))
                        self.table.setItem(row_idx, 2, QTableWidgetItem(item_id))
                        self.table.setItem(row_idx, 3, QTableWidgetItem(stt))
                        self.table.setItem(row_idx, 4, QTableWidgetItem(brand_name))

                    print(f"Đã load {loaded_count} ID từ cấu trúc cột cố định")

                # Cập nhật số lượng ID đã chọn
                self.update_selection_count()

            except Exception as e:
                print(f"Lỗi khi cập nhật bảng: {str(e)}")
                self.column_info_label.setText(f"Lỗi: {str(e)}")
                self.column_info_label.setStyleSheet("color: red;")
                import traceback
                traceback.print_exc()
        else:
            print("Không có sheet nào được load hoặc không thể truy cập sheet")
            self.column_info_label.setText("Không có sheet nào được load")
            self.column_info_label.setStyleSheet("color: red;")

    def update_selection_count(self):
        """Cập nhật số lượng ID đã chọn"""
        count = 0
        for row in range(self.table.rowCount()):
            checkbox_widget = self.table.cellWidget(row, 0)
            if checkbox_widget:
                layout = checkbox_widget.layout()
                checkbox = layout.itemAt(0).widget()
                if checkbox and checkbox.isChecked():
                    count += 1

        self.selection_info.setText(f"{count} ID đã được chọn")

    def filter_table(self, text):
        """Lọc bảng theo từ khóa tìm kiếm"""
        search_text = text.lower()
        for row in range(self.table.rowCount()):
            visible = False
            for col in range(1, self.table.columnCount()):  # Bắt đầu từ cột 1 (bỏ qua cột checkbox)
                item = self.table.item(row, col)
                if item and search_text in item.text().lower():
                    visible = True
                    break

            self.table.setRowHidden(row, not visible)

    def select_all_rows(self):
        """Chọn tất cả các hàng đang hiển thị"""
        for row in range(self.table.rowCount()):
            if not self.table.isRowHidden(row):
                checkbox_widget = self.table.cellWidget(row, 0)
                if checkbox_widget:
                    layout = checkbox_widget.layout()
                    checkbox = layout.itemAt(0).widget()
                    if checkbox:
                        checkbox.setChecked(True)

        self.update_selection_count()

    def clear_all_rows(self):
        """Bỏ chọn tất cả các hàng"""
        for row in range(self.table.rowCount()):
            checkbox_widget = self.table.cellWidget(row, 0)
            if checkbox_widget:
                layout = checkbox_widget.layout()
                checkbox = layout.itemAt(0).widget()
                if checkbox:
                    checkbox.setChecked(False)

        self.update_selection_count()

    def get_updated_exclusive_ids(self):
        """Trả về map đã cập nhật của các ID Exclusive theo khung giờ"""
        # Cập nhật map với thay đổi hiện tại
        current_time_slot = self.time_combo.currentText()

        # Lấy danh sách ID đã chọn
        selected_ids = []
        for row in range(self.table.rowCount()):
            checkbox_widget = self.table.cellWidget(row, 0)
            if checkbox_widget:
                layout = checkbox_widget.layout()
                checkbox = layout.itemAt(0).widget()
                if checkbox and checkbox.isChecked():
                    item_id_item = self.table.item(row, 2)  # Lấy Item ID từ cột 2
                    if item_id_item:
                        selected_ids.append(item_id_item.text())

        # Cập nhật map
        self.exclusive_ids_map[current_time_slot] = selected_ids

        # Loại bỏ các khung giờ có danh sách ID rỗng
        return {time_slot: ids for time_slot, ids in self.exclusive_ids_map.items() if ids}

    def select_manual_column(self, time_slot):
        """Cho phép người dùng chọn cột ID thủ công nếu không tìm thấy khớp tự động"""
        if not hasattr(self.parent_app, 'id_columns') or not self.parent_app.id_columns:
            return None

        dialog = QDialog(self)
        dialog.setWindowTitle("Chọn cột cho khung giờ")
        dialog.setMinimumWidth(300)

        layout = QVBoxLayout()

        label = QLabel(f"Chọn cột ID cho khung giờ '{time_slot}':")
        layout.addWidget(label)

        combo = QComboBox()
        for col in self.parent_app.id_columns:
            combo.addItem(col)
        layout.addWidget(combo)

        button_layout = QHBoxLayout()
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Hủy")

        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        dialog.setLayout(layout)

        result = dialog.exec()
        if result == QDialog.DialogCode.Accepted and combo.currentText():
            selected_column = combo.currentText()
            print(f"Người dùng đã chọn cột {selected_column} cho khung giờ '{time_slot}'")

            # Cập nhật time_slot_map của parent_app với lựa chọn mới
            if hasattr(self.parent_app, 'time_slot_map'):
                self.parent_app.time_slot_map[time_slot] = selected_column
                print(f"Đã cập nhật time_slot_map: '{time_slot}' -> '{selected_column}'")

            return selected_column

        return None

if __name__ == "__main__":
    app = QApplication(sys.argv)
    # Thiết lập thuộc tính để đảm bảo dropdowns được hiển thị đúng cách
    app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings)
    window = MainApp()
    window.show()
    sys.exit(app.exec())
 