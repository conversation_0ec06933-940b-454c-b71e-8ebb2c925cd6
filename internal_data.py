import os
import pickle
import base64
import json
from math import ceil
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton,
    QMessageBox, QApplication, QLabel, QGroupBox, QGridLayout,
    QRadioButton, QDialog, QPlainTextEdit, QSpacerItem, QSizePolicy, QCheckBox, QToolButton, QMenu
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QEvent
from PyQt6.QtGui import QClipboard

# Import lớp GoogleSheetManager từ module gsheet_manager.py
from gsheet_manager import GoogleSheetManager

# ------------------ CẤU HÌNH ------------------
# Cấu hình cho Type 1 và Type 2
DEFAULT_SHEET_ID_TYPE1 = "1DKDP_fjTT4e9ubsENiGP7hnLFOufnLz-l_R9b3K_tns"   # Sheet mẫu cho Type 1
DEFAULT_SHEET_ID_TYPE2 = "1uFuEn4CbcsmfhqS_CWapyOvpMlpcnER2sTlVQ4lmCCo"   # Sheet mẫu cho Type 2
DEFAULT_SHEET_ID = DEFAULT_SHEET_ID_TYPE1  # Mặc định sử dụng Type 1

# OAuth credentials chính cho Internal Data
CREDENTIALS_BASE64 = (
    "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
)

# OAuth credentials thay thế từ các module khác
ALTERNATIVE_CREDENTIALS = [
    # Credentials từ external_update.py
    "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
]

# ------------------ Force Google Sheet Recalculate Formulas ------------------
def force_recalculate_with_batch(sheets_service, spreadsheet_id):
    requests = [
        {"addSheet": {"properties": {"title": "TempSheet"}}},
        {"deleteSheet": {"sheetId": None}}  # Google Sheets sẽ tự xử lý
    ]
    try:
        sheets_service.spreadsheets().batchUpdate(
            spreadsheetId=spreadsheet_id,
            body={"requests": requests}
        ).execute()
    except Exception as e:
        print(f"Lỗi khi thực hiện batchUpdate: {str(e)}")

# ------------------ WorkerThread cho Auto Copy ------------------
class WorkerThreadAuto(QThread):
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    progress = pyqtSignal(str)  # New signal for progress updates

    def __init__(self, sheets_service, source_id, sheet_ids, new_title, original_mapping):
        super().__init__()
        self.sheets_service = sheets_service
        self.source_id = source_id
        self.sheet_ids = sheet_ids
        self.new_title = new_title
        self.original_mapping = original_mapping
        self._is_running = True

    def stop(self):
        self._is_running = False

    def run(self):
        try:
            if not self._is_running:
                return

            self.progress.emit("Đang tạo spreadsheet mới...")
            new_spreadsheet = self.sheets_service.spreadsheets().create(
                body={'properties': {'title': self.new_title, 'locale': 'en_US'}}
            ).execute()
            new_id = new_spreadsheet['spreadsheetId']

            if not self._is_running:
                return

            self.progress.emit("Đang copy sheets...")
            rename_requests = []
            total_sheets = len(self.sheet_ids)

            for idx, original_sheet_id in enumerate(self.sheet_ids, 1):
                if not self._is_running:
                    return
                self.progress.emit(f"Đang copy sheet {idx}/{total_sheets}...")
                try:
                    result = self.sheets_service.spreadsheets().sheets().copyTo(
                        spreadsheetId=self.source_id,
                        sheetId=original_sheet_id,
                        body={'destinationSpreadsheetId': new_id}
                    ).execute()
                except Exception as e:
                    self.error.emit(f"Lỗi khi sao chép sheet {idx}: {str(e)}")
                    return

            if not self._is_running:
                return

            self.progress.emit("Đang cập nhật tên sheets...")
            spreadsheet = self.sheets_service.spreadsheets().get(
                spreadsheetId=new_id
            ).execute()

            for sheet in spreadsheet.get('sheets', []):
                if not self._is_running:
                    return

                sheet_id = sheet['properties']['sheetId']
                title = sheet['properties'].get('title', '')

                if title.startswith("Copy of "):
                    new_title = title[8:]
                    rename_requests.append({
                        "updateSheetProperties": {
                            "properties": {
                                "sheetId": sheet_id,
                                "title": new_title
                            },
                            "fields": "title"
                        }
                    })

            if rename_requests and self._is_running:
                self.progress.emit("Đang đổi tên sheets...")
                self.sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=new_id,
                    body={"requests": rename_requests}
                ).execute()

            if not self._is_running:
                return

            # Xóa sheet mặc định nếu tồn tại
            self.progress.emit("Đang dọn dẹp...")
            new_spreadsheet_details = self.sheets_service.spreadsheets().get(
                spreadsheetId=new_id
            ).execute()

            default_sheet_id = None
            for sheet in new_spreadsheet_details.get('sheets', []):
                if sheet['properties'].get('title', '') in ("Trang tính 1", "Trang tính1", "Sheet1"):
                    default_sheet_id = sheet['properties']['sheetId']
                    break

            if default_sheet_id is not None and self._is_running:
                self.sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=new_id,
                    body={'requests': [{'deleteSheet': {'sheetId': default_sheet_id}}]}
                ).execute()

            if self._is_running:
                self.finished.emit(new_id)
                force_recalculate_with_batch(self.sheets_service, new_id)
        except Exception as e:
            self.error.emit(f"Lỗi khi tạo spreadsheet mới: {str(e)}")

# ------------------ WorkerThread cho Manual Copy ------------------
class WorkerThreadManual(QThread):
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
    progress = pyqtSignal(str)

    def __init__(self, sheets_service, source_id, destination_id, sheet_ids):
        super().__init__()
        self.sheets_service = sheets_service
        self.source_id = source_id
        self.destination_id = destination_id
        self.sheet_ids = sheet_ids
        self._is_running = True

    def stop(self):
        self._is_running = False

    def run(self):
        try:
            if not self._is_running:
                return
            self.progress.emit("Đang copy sheets...")
            copied_sheets = []
            total_sheets = len(self.sheet_ids)
            for idx, sheet_id in enumerate(self.sheet_ids, 1):
                if not self._is_running:  # Thêm kiểm tra
                    return
                self.progress.emit(f"Đang copy sheet {idx}/{total_sheets}...")
                result = self.sheets_service.spreadsheets().sheets().copyTo(
                    spreadsheetId=self.source_id,
                    sheetId=sheet_id,
                    body={'destinationSpreadsheetId': self.destination_id}
                ).execute()
                copied_sheets.append(result)
            if not self._is_running:
                return
            self.progress.emit("Đang cập nhật tên sheets...")
            spreadsheet = self.sheets_service.spreadsheets().get(
                spreadsheetId=self.destination_id
            ).execute()

            rename_requests = []
            for sheet in spreadsheet.get('sheets', []):
                sheet_id = sheet['properties']['sheetId']
                title = sheet['properties'].get('title', '')
                if title.startswith("Copy of "):
                    new_title = title[8:]
                    rename_requests.append({
                        "updateSheetProperties": {
                            "properties": {
                                "sheetId": sheet_id,
                                "title": new_title
                            },
                            "fields": "title"
                        }
                    })

            if rename_requests and self._is_running:
                self.progress.emit("Đang đổi tên sheets...")
                self.sheets_service.spreadsheets().batchUpdate(
                    spreadsheetId=self.destination_id,
                    body={"requests": rename_requests}
                ).execute()

            if self._is_running:
                self.finished.emit(self.destination_id)
                force_recalculate_with_batch(self.sheets_service, self.destination_id)
        except Exception as e:
            error_msg = f"Lỗi khi sao chép sheet: {str(e)}"
            self.error.emit(error_msg)

# ------------------ ClickableLineEdit ------------------
class ClickableLineEdit(QLineEdit):
    def mousePressEvent(self, event):
        super().mousePressEvent(event)
        clipboard = QApplication.clipboard()
        clipboard.setText(self.text())
        QMessageBox.information(self, "Thông báo", "Đã lưu vào clipboard.")

# ------------------ DealListDialog (Separate Window) ------------------
class DealListDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Nhập/Sửa Product ID List")
        self.setMinimumSize(400, 300)
        layout = QVBoxLayout(self)
        self.text_edit = QPlainTextEdit()
        self.text_edit.setPlaceholderText("Nhập danh sách ID")
        self.text_edit.textChanged.connect(self.update_count)
        layout.addWidget(self.text_edit)
        self.count_label = QLabel("Tổng Product ID: 0")
        layout.addWidget(self.count_label)
        button_layout = QHBoxLayout()
        btn_ok = QPushButton("OK")
        btn_cancel = QPushButton("Cancel")
        btn_ok.clicked.connect(self.accept)
        btn_cancel.clicked.connect(self.reject)
        button_layout.addWidget(btn_ok)
        button_layout.addWidget(btn_cancel)
        layout.addLayout(button_layout)
    def update_count(self):
        text = self.text_edit.toPlainText().strip()
        id_list = [line.strip() for line in text.splitlines() if line.strip()]
        unique_ids = list(dict.fromkeys(id_list))
        self.count_label.setText(f"Số ID: {len(unique_ids)}")
    def get_ids(self):
        text = self.text_edit.toPlainText().strip()
        id_list = [line.strip() for line in text.splitlines() if line.strip()]
        return list(dict.fromkeys(id_list))

# ------------------ CreateInternalWidget ------------------
import tempfile

# Thêm hằng số cho cả 2 loại sheets mặc định
DEFAULT_SHEET_ID_TYPE1 = "1DKDP_fjTT4e9ubsENiGP7hnLFOufnLz-l_R9b3K_tns"  # Link cho Type 1
DEFAULT_SHEET_ID_TYPE2 = "1uFuEn4CbcsmfhqS_CWapyOvpMlpcnER2sTlVQ4lmCCo"  # Link cũ (Type 2)

class CreateInternalWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.gsheet_manager = GoogleSheetManager(
            auth_type='oauth',
            credentials_data=CREDENTIALS_BASE64,
            all_oauth_credentials=ALTERNATIVE_CREDENTIALS
        )
        self.sheets_service = self.gsheet_manager.get_sheets_service()
        self.sheets_dict = {}  # Mapping từ tên sheet gốc sang sheet id gốc
        self.sheet_checkboxes = []  # Danh sách checkbox hiển thị theo grid
        self.new_spreadsheet_id = None
        self.deal_ids = []
        self.custom_sheet_id = ""
        self.default_sheets_dict = None
        self.temp_product_file_used = False
        self.temp_dest_file_used = False
        self.default_sheets_dict_type1 = None  # Cache cho Type 1
        self.default_sheets_dict_type2 = None  # Cache cho Type 2

        # Khởi tạo UI trước
        self.init_ui()

        # Tự động load sheets nếu Type 1 được chọn
        if self.rb_load_type1.isChecked():
            self.load_sheets()

    def init_ui(self):
        main_layout = QVBoxLayout(self)

        # Top Bar: Back Button
        top_bar = QHBoxLayout()
        btn_back = QPushButton("⬅️ Trở về giao diện chính")
        btn_back.setFixedSize(150, 40)
        btn_back.setStyleSheet("border: none; font-size: 12px; font-weight: bold;")
        btn_back.clicked.connect(self.handle_back)
        top_bar.addWidget(btn_back, alignment=Qt.AlignmentFlag.AlignLeft)

        self.context_button = QToolButton()
        self.context_button.setText("Chuyển đến ➡️")
        self.context_button.setFixedSize(100, 30)
        self.context_button.setStyleSheet("""
            QToolButton {
                border: none;
                background: transparent;
                font-size: 12px;
                font-weight: bold;
            }
            QToolButton::menu-indicator {
                image: none;
                width: 0px;
            }
        """)
        self.context_button.installEventFilter(self)
        self.context_button.setPopupMode(QToolButton.ToolButtonPopupMode.InstantPopup)
        menu = QMenu(self.context_button)
        menu.addAction("Data Scraping", lambda: self.goto_other_program("autoshopee_scraping"))
        menu.addAction("Import Data", lambda: self.goto_other_program("import_data"))
        menu.addAction("Data Handler", lambda: self.goto_other_program("data_handler"))
        menu.addAction("Image Scraping", lambda: self.goto_other_program("image_scraping"))
        menu.addAction("Update Level Model", lambda: self.goto_other_program("external_update"))
        menu.addAction("AI Classification", lambda: self.goto_other_program("ai_classification"))
        menu.addAction("Basket Arrangement", lambda: self.goto_other_program("basket_arrangement"))
        self.context_button.setMenu(menu)
        top_bar.addWidget(self.context_button, alignment=Qt.AlignmentFlag.AlignRight)

        main_layout.addLayout(top_bar)
        main_layout.addSpacing(10)

        # ---------- Load Sheet Group ----------
        load_group = QGroupBox("Original Sheets List")
        load_layout = QVBoxLayout(load_group)

        # New radio buttons
        load_mode_layout = QHBoxLayout()
        self.rb_load_type1 = QRadioButton("Type 1")
        self.rb_load_type2 = QRadioButton("Type 2")
        self.rb_load_custom = QRadioButton("Tùy chọn")

        # Theo mặc định, chọn Type 1
        self.rb_load_type1.setChecked(True)

        # Kết nối signals
        self.rb_load_type1.toggled.connect(self.update_load_mode)
        self.rb_load_type2.toggled.connect(self.update_load_mode)
        self.rb_load_custom.toggled.connect(self.update_load_mode)

        load_mode_layout.addWidget(self.rb_load_type1)
        load_mode_layout.addWidget(self.rb_load_type2)
        load_mode_layout.addWidget(self.rb_load_custom)
        load_layout.addLayout(load_mode_layout)

        # Text box for custom link
        self.custom_link_edit = QLineEdit()
        self.custom_link_edit.setPlaceholderText("Nhập ID Spreadsheet")
        self.custom_link_edit.textChanged.connect(self.parse_custom_link)
        self.custom_link_edit.setVisible(False)
        load_layout.addWidget(self.custom_link_edit)

        # Sắp xếp checkbox theo grid: 2 cột, theo thứ tự từ trên xuống dưới, sau đó sang cột bên phải
        self.sheet_list_widget = QWidget()
        self.sheet_list_layout = QGridLayout(self.sheet_list_widget)
        load_layout.addWidget(self.sheet_list_widget)
        # Replace single button with a row of buttons
        button_layout = QHBoxLayout()
        btn_load = QPushButton("Load Sheet")
        btn_load.clicked.connect(self.load_sheets)
        button_layout.addWidget(btn_load)
        self.btn_toggle_check = QPushButton()  # Toggle button
        self.btn_toggle_check.clicked.connect(self.toggle_all_sheets)
        button_layout.addWidget(self.btn_toggle_check)
        load_layout.addLayout(button_layout)
        main_layout.addWidget(load_group)

        # ---------- Tạo Bản Sao Group ----------
        copy_group = QGroupBox("Tạo Bản Sao")
        copy_layout = QVBoxLayout(copy_group)
        # Radio buttons: Auto Copy / Manual Copy
        option_layout = QHBoxLayout()
        self.rb_auto = QRadioButton("Auto Copy")
        self.rb_auto.setChecked(True)
        self.rb_manual = QRadioButton("Manual Copy")
        option_layout.addWidget(self.rb_auto)
        option_layout.addWidget(self.rb_manual)
        copy_layout.addLayout(option_layout)
        # Nếu Auto Copy: nhập tiêu đề; nếu Manual Copy: nhập Spreadsheet ID đích
        self.auto_copy_widget = QWidget()
        auto_layout = QHBoxLayout(self.auto_copy_widget)
        auto_layout.addWidget(QLabel("Tên Spreadsheet:"))
        self.auto_title_edit = QLineEdit()
        self.auto_title_edit.setPlaceholderText("Nhập tên cho spreadsheet mới")
        self.auto_title_edit.setText("[DD.MM] Internal | SPE x BK Deal List")
        auto_layout.addWidget(self.auto_title_edit)
        self.auto_copy_widget.setVisible(True)
        self.manual_copy_widget = QWidget()
        manual_layout = QHBoxLayout(self.manual_copy_widget)
        manual_layout.addWidget(QLabel("Spreadsheet ID:"))
        self.manual_dest_input = QLineEdit()
        self.manual_dest_input.setPlaceholderText("Nhập Spreadsheet ID")
        manual_layout.addWidget(self.manual_dest_input)
        self.manual_copy_widget.setVisible(False)
        copy_layout.addWidget(self.auto_copy_widget)
        copy_layout.addWidget(self.manual_copy_widget)
        self.rb_auto.toggled.connect(self.update_copy_mode)
        self.rb_manual.toggled.connect(self.update_copy_mode)
        # Link hiển thị (Auto Copy)
        self.link_output = ClickableLineEdit()
        self.link_output.setReadOnly(True)
        self.link_output.setPlaceholderText("Địa chỉ truy cập spreadsheet mới")
        copy_layout.addWidget(self.link_output)
        # Nút Tạo Bản Sao
        self.copy_btn = QPushButton("Tạo Bản Sao")
        self.copy_btn.clicked.connect(self.start_copy)
        copy_layout.addWidget(self.copy_btn)
        main_layout.addWidget(copy_group)

        # ---------- Cập nhật ô & Deal list (Hai cột ngang) ----------
        update_container = QHBoxLayout()

        # Left: Dynamic Formulas Group
        dyn_form_group = QGroupBox("Dynamic Formulas")
        dyn_form_layout = QVBoxLayout(dyn_form_group)
        global_layout = QHBoxLayout()
        self.global_formula_external_id = QLineEdit()
        self.global_formula_external_id.setPlaceholderText("External Spreadsheet ID")
        self.global_formula_external_sheet = QLineEdit()
        self.global_formula_external_sheet.setPlaceholderText("Tên sheet")
        global_layout.addWidget(self.global_formula_external_id)
        global_layout.addWidget(self.global_formula_external_sheet)
        dyn_form_layout.addLayout(global_layout)
        btns_layout = QHBoxLayout()
        self.btn_get_input = QPushButton("Lấy dữ liệu")
        self.btn_get_input.clicked.connect(self.get_input_data)
        self.btn_update_all = QPushButton("Cập nhật")
        self.btn_update_all.clicked.connect(self.update_all)
        btns_layout.addWidget(self.btn_get_input)
        btns_layout.addWidget(self.btn_update_all)
        dyn_form_layout.addLayout(btns_layout)
        formulas_layout = QVBoxLayout()
        cluster_layout = QHBoxLayout()
        cluster_layout.addWidget(QLabel("Cluster devider:"))
        self.cluster_id_edit = QLineEdit()
        self.cluster_id_edit.setPlaceholderText("External Spreadsheet ID")
        self.cluster_sheet_edit = QLineEdit()
        self.cluster_sheet_edit.setPlaceholderText("Tên sheet")
        cluster_layout.addWidget(self.cluster_id_edit)
        cluster_layout.addWidget(self.cluster_sheet_edit)
        formulas_layout.addLayout(cluster_layout)
        source_layout = QHBoxLayout()
        source_layout.addWidget(QLabel("Source control:"))
        self.source_control_id_edit = QLineEdit()
        self.source_control_id_edit.setPlaceholderText("External Spreadsheet ID")
        self.source_control_sheet_edit = QLineEdit()
        self.source_control_sheet_edit.setPlaceholderText("Tên sheet")
        source_layout.addWidget(self.source_control_id_edit)
        source_layout.addWidget(self.source_control_sheet_edit)
        formulas_layout.addLayout(source_layout)
        shorten_layout = QHBoxLayout()
        shorten_layout.addWidget(QLabel("Shorten of ext:"))
        self.shorten_id_edit = QLineEdit()
        self.shorten_id_edit.setPlaceholderText("External Spreadsheet ID")
        self.shorten_sheet_edit = QLineEdit()
        self.shorten_sheet_edit.setPlaceholderText("Tên sheet")
        shorten_layout.addWidget(self.shorten_id_edit)
        shorten_layout.addWidget(self.shorten_sheet_edit)
        formulas_layout.addLayout(shorten_layout)
        dyn_form_layout.addLayout(formulas_layout)

        # Right: Deal list Group
        deal_group = QGroupBox("Deal list")
        deal_layout = QVBoxLayout(deal_group)
        self.deal_count_label = QLabel("Tổng Product ID: 0")
        deal_layout.addWidget(self.deal_count_label)
        self.btn_toggle_deal_list = QPushButton("Nhập/Sửa Product ID List")
        self.btn_toggle_deal_list.clicked.connect(self.open_deal_list_dialog)
        deal_layout.addWidget(self.btn_toggle_deal_list)
        self.deal_update_btn = QPushButton("Cập nhật Deal list")
        self.deal_update_btn.clicked.connect(self.update_deal_list)
        deal_layout.addWidget(self.deal_update_btn)

        update_container.addWidget(dyn_form_group)
        update_container.addWidget(deal_group)
        main_layout.addLayout(update_container)

        self.setWindowTitle("Create Internal Data")
        self.setMinimumWidth(600)

        # Thêm xử lý textChanged cho global_formula_external_id
        self.global_formula_external_id.textChanged.connect(self.parse_external_id)

    def open_deal_list_dialog(self):
        dialog = DealListDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.deal_ids = dialog.get_ids()
            self.deal_count_label.setText(f"Số ID: {len(self.deal_ids)}")

    def update_copy_mode(self):
        if self.rb_auto.isChecked():
            self.auto_copy_widget.setVisible(True)
            self.manual_copy_widget.setVisible(False)
            self.link_output.setVisible(True)
        else:
            self.auto_copy_widget.setVisible(False)
            self.manual_copy_widget.setVisible(True)
            self.link_output.setVisible(False)

    def clear_sheet_checkboxes(self):
        while self.sheet_list_layout.count():
            child = self.sheet_list_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        self.sheet_checkboxes = []

    def populate_sheet_checkboxes(self, default_checked=True):
        sheet_names = list(self.sheets_dict.keys())
        n = len(sheet_names)
        num_cols = 2
        num_rows = ceil(n / num_cols)
        for col in range(num_cols):
            for row in range(num_rows):
                idx = row + col * num_rows
                if idx < n:
                    cb = QCheckBox(sheet_names[idx])
                    cb.setChecked(default_checked)
                    cb.toggled.connect(self.update_toggle_check_button)
                    self.sheet_checkboxes.append(cb)
                    self.sheet_list_layout.addWidget(cb, row, col)
        self.update_toggle_check_button()

    def toggle_all_sheets(self):
        total = len(self.sheet_checkboxes)
        checked = sum(1 for cb in self.sheet_checkboxes if cb.isChecked())
        if checked == total:
            for cb in self.sheet_checkboxes:
                cb.setChecked(False)
        else:
            for cb in self.sheet_checkboxes:
                cb.setChecked(True)
        self.update_toggle_check_button()

    def update_toggle_check_button(self):
        total = len(self.sheet_checkboxes)
        checked = sum(1 for cb in self.sheet_checkboxes if cb.isChecked())
        if total > 0 and checked == total:
            self.btn_toggle_check.setText("Uncheck All")
        else:
            self.btn_toggle_check.setText("Check All")

    def update_load_mode(self):
        """Cập nhật UI và dữ liệu dựa trên radio button được chọn"""
        self.custom_link_edit.setVisible(self.rb_load_custom.isChecked())

        if self.rb_load_custom.isChecked():
            self.clear_sheet_checkboxes()
        else:
            if self.rb_load_type1.isChecked():
                # Xử lý khi chọn Type 1
                if hasattr(self, 'default_sheets_dict_type1') and self.default_sheets_dict_type1 is not None:
                    self.sheets_dict = self.default_sheets_dict_type1
                    self.clear_sheet_checkboxes()
                    self.populate_sheet_checkboxes(default_checked=True)
                else:
                    # Load sheets từ ID Type 1
                    self.load_sheets()

            elif self.rb_load_type2.isChecked():
                # Xử lý khi chọn Type 2
                if hasattr(self, 'default_sheets_dict_type2') and self.default_sheets_dict_type2 is not None:
                    self.sheets_dict = self.default_sheets_dict_type2
                    self.clear_sheet_checkboxes()
                    self.populate_sheet_checkboxes(default_checked=True)
                else:
                    # Load sheets từ ID Type 2
                    self.load_sheets()

    def parse_custom_link(self, text):
        # Tự động trích xuất ID từ chuỗi link
        import re
        match = re.search(r"/d/([a-zA-Z0-9-_]+)/", text)
        if match:
            self.custom_sheet_id = match.group(1)
        else:
            self.custom_sheet_id = text.strip()

    def load_sheets(self):
        """Load danh sách sheets từ spreadsheet được chọn"""
        try:
            spreadsheet_id = None

            # Xác định spreadsheet ID dựa trên radio button được chọn
            if self.rb_load_type1.isChecked():
                spreadsheet_id = DEFAULT_SHEET_ID_TYPE1
                self.sheets_dict.clear() # Clear existing sheets first
                result = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
                self.sheets_dict = {
                    sheet['properties']['title']: sheet['properties']['sheetId']
                    for sheet in result.get('sheets', [])
                }
                self.default_sheets_dict_type1 = self.sheets_dict.copy()

            elif self.rb_load_type2.isChecked():
                spreadsheet_id = DEFAULT_SHEET_ID_TYPE2
                self.sheets_dict.clear() # Clear existing sheets first
                result = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
                self.sheets_dict = {
                    sheet['properties']['title']: sheet['properties']['sheetId']
                    for sheet in result.get('sheets', [])
                }
                self.default_sheets_dict_type2 = self.sheets_dict.copy()

            else:
                spreadsheet_id = self.custom_sheet_id

            # Validate spreadsheet_id
            if not spreadsheet_id:
                QMessageBox.warning(self, "Warning", "Vui lòng nhập link hoặc ID hợp lệ")
                return

            # Reload UI với sheets mới
            self.clear_sheet_checkboxes()
            self.populate_sheet_checkboxes(default_checked=(self.rb_load_type1.isChecked() or self.rb_load_type2.isChecked()))

        except Exception as e:
            QMessageBox.critical(self, "Error ❌", f"Không thể tải sheet:\n{str(e)}")

    def start_copy(self):
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            QMessageBox.warning(self, "Warning", "Đang có tiến trình copy đang chạy, vui lòng đợi hoàn thành!")
            return

        selected_sheet_ids = [self.sheets_dict[cb.text()] for cb in self.sheet_checkboxes if cb.isChecked()]
        if not selected_sheet_ids:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng chọn ít nhất một sheet để copy")
            return

        # Progress dialog
        self.progress_dialog = QDialog(self)
        self.progress_dialog.setWindowTitle("Đang xử lý")
        self.progress_dialog.setModal(True)
        layout = QVBoxLayout(self.progress_dialog)
        self.progress_label = QLabel("Đang khởi tạo...", self.progress_dialog)
        layout.addWidget(self.progress_label)
        cancel_button = QPushButton("Hủy", self.progress_dialog)
        layout.addWidget(cancel_button)

        # Xác định source_id dựa trên lựa chọn Type 1, Type 2 hoặc tùy chọn
        source_id = DEFAULT_SHEET_ID_TYPE1 if self.rb_load_type1.isChecked() else \
                    DEFAULT_SHEET_ID_TYPE2 if self.rb_load_type2.isChecked() else \
                    self.custom_sheet_id

        if self.rb_auto.isChecked():
            new_title = self.auto_title_edit.text().strip()
            if not new_title:
                QMessageBox.warning(self, "Warning 🚧", "Vui lòng nhập tiêu đề cho Spreadsheet mới")
                return

            original_mapping = {str(v): k for k, v in self.sheets_dict.items()}
            self.worker = WorkerThreadAuto(
                self.sheets_service,
                source_id,
                selected_sheet_ids,
                new_title,
                original_mapping
            )

            # Connect signals
            self.worker.progress.connect(self.progress_label.setText)
            self.worker.finished.connect(self.on_copy_success)
            self.worker.error.connect(self.on_copy_error)
            self.worker.finished.connect(self.progress_dialog.accept)
            self.worker.error.connect(self.progress_dialog.accept)

            # Cancel button
            cancel_button.clicked.connect(lambda: self.cancel_copy())

            self.copy_btn.setEnabled(False)
            self.worker.start()
            self.progress_dialog.exec()

        elif self.rb_manual.isChecked():
            destination_id = self.manual_dest_input.text().strip()
            if not destination_id:
                QMessageBox.warning(self, "Warning 🚧", "Vui lòng nhập Spreadsheet ID đích")
                return
            try:
                self.sheets_service.spreadsheets().get(spreadsheetId=destination_id).execute()
            except Exception as e:
                QMessageBox.critical(self, "Error ❌", f"Spreadsheet ID đích không hợp lệ hoặc không có quyền truy cập:\n{str(e)}")
                return

            self.worker = WorkerThreadManual(
                self.sheets_service,
                source_id,
                destination_id,
                selected_sheet_ids
            )

            # Connect signals
            self.worker.progress.connect(self.progress_label.setText)
            self.worker.finished.connect(self.on_copy_success)
            self.worker.error.connect(self.on_copy_error)
            self.worker.finished.connect(self.progress_dialog.accept)
            self.worker.error.connect(self.progress_dialog.accept)

            # Cancel button
            cancel_button.clicked.connect(lambda: self.cancel_copy())

            self.copy_btn.setEnabled(False)
            self.worker.start()
            self.progress_dialog.exec()
    def cancel_copy(self):
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            self.worker.stop()
            self.worker.wait()
            self.copy_btn.setEnabled(True)
            self.progress_dialog.accept()
            QMessageBox.information(self, "Thông báo", "Đã hủy quá trình copy")

    def on_copy_success(self, new_id):
        """Xử lý khi copy thành công"""
        self.new_spreadsheet_id = new_id

        # Luôn lưu ID vào file tạm bất kể mode nào
        temp_file_id = os.path.join(tempfile.gettempdir(), "temp_spreadsheet_id.txt")
        with open(temp_file_id, "w", encoding="utf-8") as f:
            f.write(new_id)

        if self.rb_auto.isChecked():
            link = f'https://docs.google.com/spreadsheets/d/{new_id}/edit'
            self.link_output.setText(link)

            # Tạo dialog thông báo với các lựa chọn
            result_dialog = QDialog(self)
            result_dialog.setWindowTitle("Tạo sheet thành công")
            result_dialog.setMinimumWidth(400)

            layout = QVBoxLayout(result_dialog)

            # Thông báo thành công
            success_label = QLabel("Đã tạo spreadsheet mới thành công!")
            success_label.setStyleSheet("font-weight: bold; color: green;")
            layout.addWidget(success_label)

            # Link và nút copy
            link_container = QHBoxLayout()
            link_label = QLineEdit(link)
            link_label.setReadOnly(True)
            copy_btn = QPushButton("Copy link")
            copy_btn.clicked.connect(lambda: [
                QApplication.clipboard().setText(link),
                QMessageBox.information(result_dialog, "Thông báo", "Đã copy link vào clipboard")
            ])
            link_container.addWidget(link_label)
            link_container.addWidget(copy_btn)
            layout.addLayout(link_container)

            # Các lựa chọn
            options_label = QLabel("\nBạn muốn:")
            layout.addWidget(options_label)

            # Button group
            btn_layout = QHBoxLayout()

            # 1. Tiếp tục chỉnh sửa sheet hiện tại
            edit_current_btn = QPushButton("Tiếp tục chỉnh sửa")
            edit_current_btn.clicked.connect(lambda: [
                result_dialog.accept(),
                self.copy_btn.setEnabled(False)  # Giữ nguyên trạng thái để tiếp tục chỉnh sửa
            ])

            # 2. Tạo sheet mới
            new_sheet_btn = QPushButton("Tạo sheet mới")
            new_sheet_btn.clicked.connect(lambda: [
                result_dialog.accept(),
                self.reset_copy_fields(),  # Chỉ reset các trường liên quan đến copy
                self.copy_btn.setEnabled(True)
            ])

            btn_layout.addWidget(edit_current_btn)
            btn_layout.addWidget(new_sheet_btn)
            layout.addLayout(btn_layout)

            result_dialog.exec()
        else:
            # Manual Copy mode
            message = (
                f"Sao chép vào Spreadsheet đích thành công.\n\n"
                f"ID: {new_id}\n\n"
                "ID đã được lưu để sử dụng cho Import Data."
            )
            QMessageBox.information(self, "Thành công", message)
            self.copy_btn.setEnabled(True)

    def reset_copy_fields(self):
        """Reset chỉ các trường liên quan đến việc copy"""
        self.auto_title_edit.clear()
        self.link_output.clear()
        self.new_spreadsheet_id = None

    def on_copy_error(self, error_msg):
        QMessageBox.critical(self, "Error ❌", f"Lỗi khi sao chép:\n{error_msg}")
        self.copy_btn.setEnabled(True)  # Re-enable copy button

    def get_input_data(self):
        """
        Lấy dữ liệu từ Global Formula và điền vào các ô input khác
        """
        global_id = self.global_formula_external_id.text().strip()
        global_sheet = self.global_formula_external_sheet.text().strip()

        if not global_id:
            QMessageBox.warning(self, "Warning", "Vui lòng nhập External Spreadsheet ID")
            return

        if not global_sheet:
            QMessageBox.warning(self, "Warning", "Vui lòng nhập tên Sheet")
            return

        # Block signals để tránh trigger parse_external_id
        self.cluster_id_edit.blockSignals(True)
        self.source_control_id_edit.blockSignals(True)
        self.shorten_id_edit.blockSignals(True)

        try:
            # Cập nhật các field theo cặp
            self.cluster_id_edit.setText(global_id)
            self.cluster_sheet_edit.setText(global_sheet)

            self.source_control_id_edit.setText(global_id)
            self.source_control_sheet_edit.setText(global_sheet)

            self.shorten_id_edit.setText(global_id)
            self.shorten_sheet_edit.setText(global_sheet)

            QMessageBox.information(self, "Thành công", "Đã cập nhật dữ liệu cho tất cả các trường")

        finally:
            # Restore signals
            self.cluster_id_edit.blockSignals(False)
            self.source_control_id_edit.blockSignals(False)
            self.shorten_id_edit.blockSignals(False)

    def parse_external_id(self, text):
        """Parse URL spreadsheet thành ID"""
        import re
        text = text.strip()
        # Kiểm tra nếu là URL
        match = re.search(r'/spreadsheets/d/([a-zA-Z0-9-_]+)', text)
        if match:
            sheet_id = match.group(1)
            # Cập nhật lại text box với ID đã parse
            self.global_formula_external_id.setText(sheet_id)

    def update_all(self):
        if not self.new_spreadsheet_id:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng tạo bản sao trước khi cập nhật")
            return
        cluster_id = self.cluster_id_edit.text().strip()
        cluster_sheet = self.cluster_sheet_edit.text().strip()
        if not cluster_id or not cluster_sheet:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng nhập đầy đủ thông tin cho Cluster devider")
            return
        formula_cluster_A3 = f'=IMPORTRANGE("{cluster_id}","{cluster_sheet}!D4:D")'
        formula_cluster_B3 = f'=IMPORTRANGE("{cluster_id}","{cluster_sheet}!H4:H")'
        source_id = self.source_control_id_edit.text().strip()
        source_sheet = self.source_control_sheet_edit.text().strip()
        if not source_id or not source_sheet:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng nhập đầy đủ thông tin cho Source control")
            return
        shorten_id = self.shorten_id_edit.text().strip()
        shorten_sheet = self.shorten_sheet_edit.text().strip()
        if not shorten_id or not shorten_sheet:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng nhập đầy đủ thông tin cho Shorten of ext")
            return
        formula_shorten_A1 = f'=IMPORTRANGE("{shorten_id}","{shorten_sheet}!D:N")'
        formula_shorten_L1 = f'=IMPORTRANGE("{shorten_id}","{shorten_sheet}!AA:AC")'
        try:
            data = [
                {"range": "'Cluster devider'!A3", "values": [[formula_cluster_A3]]},
                {"range": "'Cluster devider'!B3", "values": [[formula_cluster_B3]]},
                {"range": "'Source control'!B2", "values": [[source_id]]},
                {"range": "'Source control'!B3", "values": [[source_sheet]]},
                {"range": "'Shorten of ext'!A1", "values": [[formula_shorten_A1]]},
                {"range": "'Shorten of ext'!L1", "values": [[formula_shorten_L1]]}
            ]
            body = {"valueInputOption": "USER_ENTERED", "data": data}
            self.sheets_service.spreadsheets().values().batchUpdate(
                spreadsheetId=self.new_spreadsheet_id,
                body=body
            ).execute()
            QMessageBox.information(self, "Thành công", "Cập nhật tất cả thành công")
            # Xóa file tạm chứa Destination data nếu đã sử dụng
            if self.temp_dest_file_used:
                dest_file = os.path.join(tempfile.gettempdir(), "temp_dest_data.txt")
                if os.path.exists(dest_file):
                    os.remove(dest_file)
        except Exception as e:
            QMessageBox.critical(self, "Error ❌", f"Lỗi khi cập nhật:\n{str(e)}")

    def clean_product_id(self, id_str):
        """Làm sạch mã sản phẩm: loại bỏ khoảng trắng, dấu nháy và ký tự đặc biệt"""
        # Loại bỏ khoảng trắng đầu và cuối
        cleaned = id_str.strip()
        # Loại bỏ dấu nháy đơn, nháy kép
        cleaned = cleaned.replace('"', '').replace("'", "")
        # Loại bỏ khoảng trắng giữa các ký tự nếu có
        cleaned = cleaned.replace(" ", "")
        return cleaned

    def update_deal_list(self):
        """
        Cập nhật sheet "Deal list":
         1. Ghi danh sách ID (plain text, unique) vào cột B và cột D, bắt đầu từ dòng 4.
         2. Copy công thức từ dòng 4 (dòng mẫu chứa công thức) xuống các dòng mới cho các cột khác (ngoại trừ cột B và D).
            Giả sử công thức cần copy nằm ở:
                - Cột A (index 0)
                - Cột C (index 2)
                - Các cột từ E đến BU (index 4 đến 73)
        """
        if not self.new_spreadsheet_id:
            QMessageBox.warning(self, "Warning 🚧", "Vui lòng tạo bản sao trước khi cập nhật Deal list")
            return
        if not self.deal_ids:
            QMessageBox.warning(self, "Warning 🚧", "Chưa có danh sách ID nào được nhập")
            return

        # Làm sạch và loại bỏ trùng lặp từ danh sách ID
        cleaned_ids = []
        seen_ids = set()
        for id_val in self.deal_ids:
            cleaned_id = self.clean_product_id(id_val)
            if cleaned_id and cleaned_id not in seen_ids:
                cleaned_ids.append(cleaned_id)
                seen_ids.add(cleaned_id)

        num_rows = len(cleaned_ids)
        range_B = f"'Deal list'!B4:B{num_rows+3}"
        range_D = f"'Deal list'!D4:D{num_rows+3}"

        try:
            # Cập nhật cột B và D với ID đã được làm sạch
            id_values = [[id_val] for id_val in cleaned_ids]

            # Sử dụng batchUpdate để cập nhật cả hai cột cùng lúc
            batch_data = {
                'valueInputOption': 'RAW',
                'data': [
                    {
                        'range': range_B,
                        'values': id_values
                    },
                    {
                        'range': range_D,
                        'values': id_values
                    }
                ]
            }

            self.sheets_service.spreadsheets().values().batchUpdate(
                spreadsheetId=self.new_spreadsheet_id,
                body=batch_data
            ).execute()

        except Exception as e:
            QMessageBox.critical(self, "Error ❌", f"Lỗi khi cập nhật ID:\n{str(e)}")
            return

        try:
            ss = self.sheets_service.spreadsheets().get(
                spreadsheetId=self.new_spreadsheet_id
            ).execute()
            deal_sheet_id = None
            for sheet in ss.get('sheets', []):
                if sheet['properties'].get('title', '') == "Deal list":
                    deal_sheet_id = sheet['properties']['sheetId']
                    break
            if deal_sheet_id is None:
                QMessageBox.warning(self, "Warning 🚧", "Không tìm thấy sheet 'Deal list'")
                return
        except Exception as e:
            QMessageBox.critical(self, "Error ❌", f"Lỗi khi lấy thông tin sheet Deal list:\n{str(e)}")
            return

        destination_start = 4  # Dòng 5 (index 4)
        destination_end = num_rows + 3
        requests = []

        # Copy công thức cho cột A (index 0)
        requests.append({
            "copyPaste": {
                "source": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": 3,
                    "endRowIndex": 4,
                    "startColumnIndex": 0,
                    "endColumnIndex": 1
                },
                "destination": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": destination_start,
                    "endRowIndex": destination_end,
                    "startColumnIndex": 0,
                    "endColumnIndex": 1
                },
                "pasteType": "PASTE_FORMULA",
                "pasteOrientation": "NORMAL"
            }
        })

        # Copy công thức cho cột C (index 2)
        requests.append({
            "copyPaste": {
                "source": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": 3,
                    "endRowIndex": 4,
                    "startColumnIndex": 2,
                    "endColumnIndex": 3
                },
                "destination": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": destination_start,
                    "endRowIndex": destination_end,
                    "startColumnIndex": 2,
                    "endColumnIndex": 3
                },
                "pasteType": "PASTE_FORMULA",
                "pasteOrientation": "NORMAL"
            }
        })

        # Copy công thức cho các cột từ E đến BU (index 4 đến 73)
        requests.append({
            "copyPaste": {
                "source": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": 3,
                    "endRowIndex": 4,
                    "startColumnIndex": 4,
                    "endColumnIndex": 73
                },
                "destination": {
                    "sheetId": deal_sheet_id,
                    "startRowIndex": destination_start,
                    "endRowIndex": destination_end,
                    "startColumnIndex": 4,
                    "endColumnIndex": 73
                },
                "pasteType": "PASTE_FORMULA",
                "pasteOrientation": "NORMAL"
            }
        })

        try:
            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=self.new_spreadsheet_id,
                body={"requests": requests}
            ).execute()
            QMessageBox.information(self, "Thành công", "Cập nhật Deal list thành công")

            # Sau khi cập nhật thành công, xóa file tạm chứa mã sản phẩm nếu đã sử dụng
            if self.temp_product_file_used:
                product_file = os.path.join(tempfile.gettempdir(), "temp_product_codes.txt")
                if os.path.exists(product_file):
                    os.remove(product_file)

        except Exception as e:
            QMessageBox.critical(self, "Error ❌", f"Lỗi khi sao chép công thức:\n{str(e)}")

    def handle_back(self):
        if hasattr(self, 'worker') and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
        self.reset_ui()
        if hasattr(self, 'back_callback'):
            self.back_callback()

    def on_module_activated(self):
        """Được gọi khi module được kích hoạt từ main.py - reset về mặc định"""
        self.reset_ui()

    def reset_ui(self):
        """Reset toàn bộ UI về trạng thái ban đầu"""
        self.auto_title_edit.clear()
        self.manual_dest_input.clear()
        self.link_output.clear()
        self.global_formula_external_id.clear()
        self.global_formula_external_sheet.clear()
        self.cluster_id_edit.clear()
        self.cluster_sheet_edit.clear()
        self.source_control_id_edit.clear()
        self.source_control_sheet_edit.clear() # Sửa lại tên biến này
        self.shorten_id_edit.clear()
        self.shorten_sheet_edit.clear()
        self.deal_ids = []
        self.rb_auto.setChecked(True)
        self.update_copy_mode()
        for cb in self.sheet_checkboxes:
            cb.setChecked(True)

    def keyPressEvent(self, event):
        if event.key() == Qt.Key.Key_Backspace:
            self.handle_back()
        else:
            super().keyPressEvent(event)

    def uncheck_all_sheets(self):
        # Loop over all checkbox widgets to uncheck them
        for cb in self.sheet_checkboxes:
            cb.setChecked(False)
        self.update_check_buttons()

    def stop_creation(self):
        """Stop all internal data creation operations safely"""
        # Stop any running worker threads
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            try:
                self.worker.terminate()
                self.worker.wait()
            except:
                pass

        # Close Google Sheets connection
        if hasattr(self, 'sheets_service'):
            try:
                self.sheets_service.close()
            except:
                pass

        # Reset UI
        self.reset_ui()

    def closeEvent(self, event):
        self.cleanup_temp_files()
        self.stop_creation()
        super().closeEvent(event)

    def handle_goto_import(self):
        if hasattr(self, 'goto_import_callback'):
            self.goto_import_callback()

    def check_temp_files_on_start(self):
        """Kiểm tra và xử lý các temp files khi khởi động"""
        import os
        appdata_dir = os.getenv('LOCALAPPDATA')
        app_dir = os.path.join(appdata_dir, 'Data All in One')
        internal_dir = os.path.join(app_dir, 'Internal Data')

        if not os.path.exists(internal_dir):
            os.makedirs(internal_dir)

        # 1. Kiểm tra file mã sản phẩm
        product_file = os.path.join(internal_dir, "temp_product_codes.txt")

        # 2. Kiểm tra file destination data
        dest_file = os.path.join(internal_dir, "temp_dest_data.txt")

        # 3. Kiểm tra file spreadsheet ID
        spreadsheet_file = os.path.join(internal_dir, "temp_spreadsheet_id.txt")

        files_found = []
        data = {}

        # Kiểm tra từng file
        if os.path.exists(product_file):
            files_found.append("Mã sản phẩm")
            with open(product_file, "r", encoding="utf-8") as f:
                data["product_codes"] = [line.strip() for line in f if line.strip()]

        if os.path.exists(dest_file):
            files_found.append("Destination data")
            with open(dest_file, "r", encoding="utf-8") as f:
                lines = [line.strip() for line in f]
                if len(lines) >= 2:
                    data["dest_id"] = lines[0]
                    data["dest_sheet"] = lines[1]

        if os.path.exists(spreadsheet_file):
            files_found.append("Spreadsheet ID")
            with open(spreadsheet_file, "r", encoding="utf-8") as f:
                data["spreadsheet_id"] = f.read().strip()

        if files_found:
            from PyQt6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                "Thông báo",
                f"Đã tìm thấy nguồn dữ liệu sẵn có: {', '.join(files_found)}.\nBạn có muốn sử dụng không?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # Nạp dữ liệu từ các files temp
                if "product_codes" in data:
                    self.deal_ids = data["product_codes"]
                    self.deal_count_label.setText(f"Số ID: {len(self.deal_ids)}")
                    self.temp_product_file_used = True

                if "dest_id" in data and "dest_sheet" in data:
                    self.shorten_id_edit.setText(data["dest_id"])
                    self.shorten_sheet_edit.setText(data["dest_sheet"])
                    self.global_formula_external_id.setText(data["dest_id"])
                    self.global_formula_external_sheet.setText(data["dest_sheet"])
                    self.temp_dest_file_used = True

                if "spreadsheet_id" in data:
                    self.import_sheet_id_edit.setText(data["spreadsheet_id"])
                    self.load_sheets()  # Tự động load sheets

    def cleanup_temp_files(self):
        """Xóa các temp files khi đóng chương trình"""
        import os
        appdata_dir = os.getenv('LOCALAPPDATA')
        app_dir = os.path.join(appdata_dir, 'Data All in One')
        internal_dir = os.path.join(app_dir, 'Internal Data')

        if not os.path.exists(internal_dir):
            os.makedirs(internal_dir)

        temp_files = [
            "temp_product_codes.txt",
            "temp_dest_data.txt",
            "temp_spreadsheet_id.txt"
        ]

        for fname in temp_files:
            fpath = os.path.join(internal_dir, fname)
            if os.path.exists(fpath):
                try:
                    os.remove(fpath)
                except:
                    pass

    def goto_other_program(self, program_name):
        # Preserve data, do not reset
        pass

    def eventFilter(self, obj, event):
        if obj == self.context_button and event.type() == QEvent.Type.Enter:
            self.context_button.showMenu()
        return super().eventFilter(obj, event)

    def on_radio_button_toggled(self):
        if self.type1_radio.isChecked():
            # Update UI for Type 1
            pass
        elif self.type2_radio.isChecked():
            # Update UI for Type 2
            pass

    def show_internal_data(self):
        # ...existing code...
        self.check_temp_files_on_start()  # <-- call temp check here
        # ...existing code...